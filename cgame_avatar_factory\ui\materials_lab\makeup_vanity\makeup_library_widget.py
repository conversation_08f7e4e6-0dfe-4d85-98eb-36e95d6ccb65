"""
Makeup Library Widget Module

This widget displays and allows selection from a library of makeup presets.
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.materials_lab.makeup_vanity.makeup_lib_util import filter_by_category
from cgame_avatar_factory.materials_lab.makeup_vanity.makeup_lib_util import get_all_directory_categories
from cgame_avatar_factory.materials_lab.makeup_vanity.makeup_lib_util import read_makeup_paths
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.components.layout import FramelessVLayout
from cgame_avatar_factory.ui.materials_lab.makeup_vanity.makeup_library_view import MakeupLibraryViewSet
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class MakeupLibraryWidget(QtWidgets.QFrame):
    """
    Widget for displaying and selecting from a library of makeup presets
    """

    # Signal emitted when a makeup item is selected
    sig_makeup_item_selected = QtCore.Signal(dict)

    # Signal emitted when a makeup item is dragged
    sig_makeup_item_dragged = QtCore.Signal(dict)

    def __init__(self, parent=None):
        super(MakeupLibraryWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._makeup_items = []
        self._current_category = "全部"
        self.setup_ui()

    def setup_ui(self):
        """Initialize UI components"""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.setLayout(self.main_layout)

        # Setup toolbar with category filter
        self.setup_toolbar()

        # Setup makeup library view
        self.setup_library_view()

        # Load makeup items
        self.load_makeup_items()

    def setup_toolbar(self):
        """Setup toolbar with category filter"""
        self.toolbar_container = QtWidgets.QFrame()
        self.toolbar_layout = FramelessHLayout()
        self.toolbar_layout.setContentsMargins(0, 0, 0, 10)
        self.toolbar_container.setLayout(self.toolbar_layout)

        # Category filter
        self.category_label = dayu_widgets.MLabel("分类:")
        self.category_label.setFixedWidth(60)

        self.category_combobox = MStyleMixin.instance_wrapper(dayu_widgets.MComboBox())
        self.category_combobox.setFixedWidth(120)
        self.category_combobox.currentTextChanged.connect(self.slot_category_changed)

        self.toolbar_layout.addWidget(self.category_label)
        self.toolbar_layout.addWidget(self.category_combobox)
        self.toolbar_layout.addStretch()

        self.main_layout.addWidget(self.toolbar_container)

    def setup_library_view(self):
        """Setup makeup library view"""
        self.library_view_set = MakeupLibraryViewSet()
        self.library_view = self.library_view_set.item_view

        # Connect signals
        self.library_view.sig_item_clicked.connect(self.slot_item_clicked)

        self.main_layout.addWidget(self.library_view_set)

    def load_makeup_items(self):
        """Load makeup items from paths"""
        # Read makeup items
        self._makeup_items = read_makeup_paths()

        # Get all categories from directories, regardless of whether they contain items
        categories = get_all_directory_categories()
        self.category_combobox.clear()
        self.category_combobox.addItems(categories)
        self.category_combobox.setCurrentText("全部")

        # Update view
        self.update_view()

    def update_view(self):
        """Update the view with filtered makeup items"""
        # Filter items by category
        filtered_items = filter_by_category(self._makeup_items, self._current_category)

        # Convert to model data
        model_data = []
        for item in filtered_items:
            item_dict = item.to_dict()
            if item_dict is None:
                continue

            model_data.append(item_dict)

        # Update model
        self.library_view_set.setup_data(model_data)

    @QtCore.Slot(str)
    def slot_category_changed(self, category):
        """Handle category change"""
        self._current_category = category
        self.update_view()

    @QtCore.Slot(dict)
    def slot_item_clicked(self, item_data):
        """Handle item click"""
        self.logger.debug(f"Makeup item clicked: {item_data}")
        self.sig_makeup_item_selected.emit(item_data)
