# Import built-in modules
from datetime import datetime
import json
import logging
import os
import re

# Import third-party modules
from lightbox_paths.paths import normalize
from qtpy import QtCore


def path_normalize_decorator(func):
    """
    A decorator that wraps the result of a function with a path normalize function.

    Args:
        func (callable): The function to be decorated.

    Returns:
        callable: The decorated function that wraps the original function.
    """

    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        return normalize(result)

    return wrapper


def path_available(path):
    """Return if path is available."""
    return os.access(path, os.F_OK)


def path_exists(path):
    """Return if path exists."""
    return os.path.exists(path)


def get_file_name(file_path):
    """Return file name by file path."""
    return os.path.basename(file_path)


@path_normalize_decorator
def join_path(path, file_name):
    return os.path.join(path, file_name)


@path_normalize_decorator
def remove_extension(file_path):
    return os.path.splitext(file_path)[0]


@path_normalize_decorator
def get_file_directory(file_path):
    return os.path.dirname(file_path)


@path_normalize_decorator
def join_extension(file_path, extension):
    return normalize("{}{}".format(file_path, extension))


def join_extensions(file_path, extensions):
    paths = []
    for extension in extensions:
        paths.append(join_extension(file_path, extension))
    return paths


def generate_file_filter(extension_list, filter_name="supported files"):
    extensions = " ".join("*{}".format(ext) for ext in extension_list)
    return "{}({})".format(filter_name, extensions)


def validate_file_extensions(file_path, file_extension):
    """Return file extensions."""
    file_name = get_file_name(file_path)
    return file_name.endswith(file_extension)


def get_file_extension(file_path):
    """Return file extension with dot."""
    return os.path.splitext(file_path)[1].lower()


def widget_center_pos(widget):
    """Return widget's center position."""
    return widget.width() / 2.0, widget.height() / 2.0


def crop_pixmap_to_square(pixmap, resolution):
    """Crop pixmap to square."""
    # Scale pixmap, so it's short side match the resolution
    if not pixmap:
        return None

    width = pixmap.width()
    height = pixmap.height()

    if width == 0 or height == 0:
        return None

    scale_ratio = resolution / min(width, height)
    scaled_pixmap = pixmap.scaled(
        int(width * scale_ratio),
        int(height * scale_ratio),
        aspectRatioMode=QtCore.Qt.KeepAspectRatio,
    )

    # Crop the center of the scaled pixmap
    width = scaled_pixmap.width()
    height = scaled_pixmap.height()
    x = (width - resolution) / 2.0
    y = (height - resolution) / 2.0
    cropped_pixmap = scaled_pixmap.copy(QtCore.QRect(x, y, resolution, resolution))
    return cropped_pixmap


def append_current_time(input_str: str, time_format=None, join_format=None):
    """Return input_str with current time appended to it."""
    time_format = time_format or "%Y%m%d%H%M%S"
    join_format = join_format or "{}_{}"
    current_time = datetime.now().strftime(time_format)
    return join_format.format(input_str, current_time)


def get_file_path_with_time(file_dir, file_name, file_extension):
    """Return file path with current time appended to file name."""
    file_name = append_current_time(file_name, join_format="_{}_")

    # Double-check dot in file extension
    file_extension = file_extension.replace(".", "")

    file_path = os.path.join(file_dir, "{}.{}".format(file_name, file_extension))
    return file_path


def get_number_after_pattern(input_str, pattern):
    """Return number after pattern."""
    match = re.search(r"{}(\d+)".format(pattern), input_str)
    return int(match.group(1)) if match else None


def read_json(path: str):
    """Read and parse JSON file

    Args:
        path: Path to JSON file

    Returns:
        dict: Parsed JSON data
    """
    try:
        with open(path, "r") as f:
            return json.load(f)
    except FileNotFoundError as e:
        logging.error(f"文件不存在: {path}")
        raise e
    except json.JSONDecodeError as e:
        logging.error(f"JSON 格式错误: {path}, 错误: {e}")
        raise e
