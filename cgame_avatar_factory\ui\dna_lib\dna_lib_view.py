# Import built-in modules
import json

# Import third-party modules
import dayu_widgets
from qtpy import Qt<PERSON><PERSON>
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class DraggableMixin(object):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setViewMode(QtWidgets.QListView.IconMode)
        self.setAcceptDrops(True)  # 修改为True以启用拖放操作
        self.setDragEnabled(True)
        self.setDefaultDropAction(QtCore.Qt.MoveAction)

    def startDrag(self, supported_actions):
        current_index = self.currentIndex()
        data_dict = {}

        # 对应header list中的第一列，即dna文件路径
        dna_file_path_index = self.model().index(current_index.row(), 1)
        dna_file_path = dna_file_path_index.data(role=QtCore.Qt.UserRole)
        data_dict["dna_file_path"] = dna_file_path

        # 对应header list中的第二列，即dna缩略图
        thumbnail_path_index = self.model().index(current_index.row(), 2)
        thumbnail_path = thumbnail_path_index.data(role=QtCore.Qt.UserRole)
        data_dict["thumbnail_file_path"] = thumbnail_path

        json_str = json.dumps(data_dict)
        mime_data = QtCore.QMimeData()
        mime_data.setText(json_str)

        drag = QtGui.QDrag(self)
        drag.setMimeData(mime_data)
        drag.exec_(QtCore.Qt.MoveAction)


@MStyleMixin.cls_wrapper
class DNALibBigView(DraggableMixin, dayu_widgets.MBigView):
    """A draggable and droppable main view for displaying DNA files."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.text = "Avatar Factory"
        self.font_name = "Arial"
        self.font_size = 10
        self.bold_part = "Avatar"
        self.margin = 10
        self.text_color = const.DAYU_BG_OUT_COLOR

    def paintEvent(self, event):
        super().paintEvent(event)

        painter = QtGui.QPainter(self.viewport())
        painter.setPen(self.text_color)

        # 绘制文本
        font = QtGui.QFont(self.font_name, self.font_size)
        painter.setFont(font)

        parts = self.text.split(self.bold_part)
        bold_font = QtGui.QFont(self.font_name, self.font_size, QtGui.QFont.Bold)

        text_rect = self.rect().adjusted(0, 0, -self.margin, -self.margin)

        text_width = painter.fontMetrics().width(self.text)
        text_height = painter.fontMetrics().height()

        text_rect.setLeft(text_rect.right() - text_width - 15)
        text_rect.setTop(text_rect.bottom() - text_height)

        for i, part in enumerate(parts):
            if i > 0:
                painter.setFont(bold_font)
                painter.drawText(text_rect, QtCore.Qt.AlignLeft | QtCore.Qt.AlignTop, self.bold_part)
                text_rect.setLeft(text_rect.left() + painter.fontMetrics().width(self.bold_part))
                painter.setFont(font)
            painter.drawText(text_rect, QtCore.Qt.AlignLeft | QtCore.Qt.AlignTop, part)
            text_rect.setLeft(text_rect.left() + painter.fontMetrics().width(part))

    def contextMenuEvent(self, event):
        index = self.indexAt(event.pos())
        if index.isValid():
            context_info = {"index": index, "pos": event.globalPos()}
            self.sig_context_menu.emit(context_info)
