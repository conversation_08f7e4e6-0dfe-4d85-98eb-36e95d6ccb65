# Import built-in modules
import importlib
import random

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.constants as const
import cgame_avatar_factory.ui.pivot_move

importlib.reload(cgame_avatar_factory.ui.pivot_move)


class SceneCall:
    """Manages scene data and weight updates for merge operations

    Handles updating and tracking weights, ranges, and merge values
    for different areas in the scene. Controls attribute values and
    provides random merge functionality.

    Implemented as a singleton to ensure consistent state across the application.
    """

    _instance = None

    def __new__(cls):
        """Ensure only one instance exists (singleton pattern)"""
        if cls._instance is None:
            cls._instance = super(SceneCall, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self) -> None:
        """Initialize scene data manager (only once)"""
        if self._initialized:
            return

        self.weights = None
        self.pivot = None
        self.range = []
        self.dict_data = {}
        self.initialize_dict_data()
        self.random_merge_values = 0.0
        self._initialized = True

    def set_dict_data(self, _range, io_type, weights):
        """Set dictionary data for weights

        Process different formats of area data based on input type and set
        corresponding weight values for each area. This method unifies the handling
        of both dictionary and list formats of area data, reducing code duplication.

        Args:
            _range: Area data, can be a dictionary or list
            io_type: Input type, "dict" for dictionary format, "list" for list format
            weights: Weight values to set
        """
        if io_type == "dict":
            for key, values in _range.items():
                self.dict_data[values] = weights
        elif io_type == "list":
            for values in _range:
                self.dict_data[values] = weights

    def initialize_dict_data(self):
        """Initialize default weight values for all ranges"""
        weights = [1.0, 0.0, 0.0, 0.0]
        # self.dict_data["all"] = weights
        self.set_dict_data(const.PINCH_CTRL_ATTR, "dict", weights)
        self.set_dict_data(const.MOCK_MIRROR_AREA_CONFIG_LIST, "list", weights)

    def return_range(self):
        """Get current range

        Returns:
            list: Current range list
        """
        return self.range

    def update_weights(self, weights):
        """Update weights and apply to scene

        Args:
            weights: New weight values to apply
        """
        self.weights = weights
        self.weights_dict(weights)
        self.call_scene()

    def update_range(self, range):
        """Update current range

        Args:
            range: New range value
        """
        self.range = range

    def random_set_merge(self):
        """Apply random merge values to all ranges except 'all'"""
        for range, range_index in self.dict_data.items():
            p1 = 1 - self.random_merge_values
            p2 = random.uniform(0, self.random_merge_values)
            p3 = self.random_merge_values - p2

            random_values = [p2, p3, 0.0]
            random.shuffle(random_values)
            weight = [p1] + random_values
            self.dict_data[range] = weight
            if range in const.MOCK_MIRROR_AREA_CONFIG:
                for _range in const.MOCK_MIRROR_AREA_CONFIG[range]:
                    self.set_attr_values(_range, weight)
                    self.dict_data[_range] = weight
            else:
                self.set_attr_values(range, weight)

    def set_random_merge_values(self, values):
        """Set random merge value

        Args:
            values: New random merge value
        """
        self.random_merge_values = values

    def return_random_merge_values(self):
        """Get current random merge value

        Returns:
            float: Current random merge value
        """
        return self.random_merge_values

    def weights_dict(self, weights):
        """Update weights dictionary for ranges

        Args:
            weights: New weight values to apply
        """
        if "all" in self.range:
            self.dict_data["all"] = weights
            self.set_dict_data(const.PINCH_CTRL_ATTR, "dict", weights)
            self.set_dict_data(const.MOCK_MIRROR_AREA_CONFIG_LIST, "list", weights)
        else:
            for _range in self.range:
                if _range in const.MOCK_MIRROR_AREA_CONFIG_LIST:
                    self.set_dict_data(const.MOCK_MIRROR_AREA_CONFIG[_range], "list", weights)
                self.dict_data[_range] = weights

    def return_dict(self):
        """Get current weights dictionary

        Returns:
            dict: Current weights dictionary
        """
        return self.dict_data

    def call_scene(self):
        """Apply current weights to scene attributes"""
        if self.weights or self.range:
            if "all" in self.range:
                for _, range_index in const.PINCH_CTRL_ATTR.items():
                    self.set_attr_values(range_index, self.weights)
            else:
                for range in self.range:
                    if range in const.MOCK_MIRROR_AREA_CONFIG:
                        for _range in const.MOCK_MIRROR_AREA_CONFIG[range]:
                            self.set_attr_values(_range, self.weights)
                    else:
                        self.set_attr_values(range, self.weights)

    def reset_merge(self, target_range=None):
        """Reset merge values to defaults for specified range

        Args:
            target_range: Range to reset, if None uses self.range
        """

        default_weights = [1.0, 0.0, 0.0, 0.0]

        for range in target_range:
            if range in const.MOCK_MIRROR_AREA_CONFIG:
                for _range in const.MOCK_MIRROR_AREA_CONFIG[range]:
                    self.dict_data[_range] = default_weights.copy()
                    self.set_attr_values(_range, default_weights)
                self.dict_data[range] = default_weights.copy()
            else:
                self.dict_data[range] = default_weights.copy()
                self.set_attr_values(range, default_weights)

    def set_attr_values(self, range, weights):
        """Set attribute values for a range

        Args:
            range: Range to set values for
            weights: Weight values to apply
        """
        attr_name = f"{const.MIX_CTRL_NAME}.{range}"
        cmds.setAttr(f"{attr_name}_{const.SOURCE_SUFFIX[0]}", weights[1])
        cmds.setAttr(f"{attr_name}_{const.SOURCE_SUFFIX[1]}", weights[2])
        cmds.setAttr(f"{attr_name}_{const.SOURCE_SUFFIX[2]}", weights[3])
