# Import built-in modules
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import MFlowLayout
from dayu_widgets import utils as dayu_utils
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import utils
import cgame_avatar_factory.constants as const
from cgame_avatar_factory.ui.components.info_section import InfoSectionWidget
from cgame_avatar_factory.ui.components.info_section import FileSelectSectionWidget
from cgame_avatar_factory.ui.components.info_section import ThumbnailSelectSectionWidget
from cgame_avatar_factory.ui.dna_lib.dna_file import DnaFile
from cgame_avatar_factory.ui.dna_lib.dna_lib_view import DNALibBigView
from cgame_avatar_factory.ui.dna_lib.lib_filter_widgets import TagEditDialog
from cgame_avatar_factory.ui.dna_lib.lib_filter_widgets import TagWidget
from cgame_avatar_factory.ui.dna_lib.lib_filter_widgets import MFilterComboButton
from cgame_avatar_factory.ui.dna_lib.lib_filter_widgets import TagFilterPopupWidget
from cgame_avatar_factory.ui.dna_lib.lib_filter_widgets import TagSelectSectionWidget
import cgame_avatar_factory.ui.dna_lib.lib_util as lib_util
import cgame_avatar_factory.ui.dna_lib.tag_util as tag_util
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class DNAAddDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self._dna_data = None
        self.setup_ui()
        self.setWindowTitle("导入DNA")

    def setup_ui(self):
        self.setup_layout()
        self.setup_widgets()
        self.setup_style()
        self.setup_signals()

    def setup_layout(self):
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.main_layout)
        self.main_layout.setSpacing(0)

    def setup_widgets(self):
        self.setup_dna_select_section()
        self.setup_thumbnail_select_section()
        self.setup_dna_info_section()
        self.main_layout.addStretch(1)
        self.setup_confirm_cancel_section()

    def setup_style(self):
        self.setMinimumSize(600, 800)

    def setup_dna_select_section(self):
        self.dna_select_section = FileSelectSectionWidget(
            section_name="选择将要导入的角色文件",
            hint_text="选择角色文件",
            parent=self,
        )
        self.main_layout.addWidget(self.dna_select_section)

    def setup_thumbnail_select_section(self):
        self.thumbnail_select_section = ThumbnailSelectSectionWidget(
            section_name="选择DNA缩略图",
            hint_text="选择DNA缩略图",
            parent=self,
        )
        self.main_layout.addWidget(self.thumbnail_select_section)

    def setup_dna_info_section(self):
        dna_info_data = const.MOCK_DNA_FILE_DATA_CONFIG
        self.dna_info_section = InfoSectionWidget(dna_info_data, section_name="填写DNA信息", parent=self)
        self.main_layout.addWidget(self.dna_info_section)

    def setup_confirm_cancel_section(self):
        self.confirm_cancel_container = QtWidgets.QFrame()
        self.confirm_cancel_layout = QtWidgets.QHBoxLayout()
        self.confirm_cancel_container.setLayout(self.confirm_cancel_layout)

        self.confirm_button = dayu_widgets.MPushButton(parent=self)
        self.confirm_button.setText("确认")
        self.confirm_button.clicked.connect(self.accept)
        self.confirm_button.setMinimumWidth(100)
        self.cancel_button = dayu_widgets.MPushButton(parent=self)
        self.cancel_button.setText("取消")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setMinimumWidth(100)

        self.confirm_cancel_layout.addStretch(1)
        self.confirm_cancel_layout.addWidget(self.cancel_button)
        self.confirm_cancel_layout.addStretch(1)
        self.confirm_cancel_layout.addWidget(self.confirm_button)
        self.confirm_cancel_layout.addStretch(1)
        self.main_layout.addWidget(self.confirm_cancel_container)

    def setup_signals(self):
        self.dna_select_section.sig_file_select_button_clicked.connect(self.slot_select_dna_file)
        self.thumbnail_select_section.sig_file_select_button_clicked.connect(self.slot_select_thumbnail_file)

    def select_file(self, filter="*.*"):
        file_path, file_type = QtWidgets.QFileDialog.getOpenFileName(
            self,
            "选择文件",
            "",
            filter,
        )
        return file_path

    @QtCore.Slot()
    def slot_select_dna_file(self):
        filter = utils.generate_file_filter(const.SUPPORTED_DNA_FILE_EXTENSIONS, filter_name="角色文件")
        file_path = self.select_file(filter=filter)
        if not file_path:
            return
        self.dna_select_section.set_file_path(file_path)
        dna_file_name = utils.get_file_name(file_path)
        character_name = utils.remove_extension(dna_file_name)
        dna_directory = utils.get_file_directory(file_path)

        self.dna_info_section.update_info("character_name", character_name)
        possible_thumbnail_file_paths = utils.join_extensions(
            "{}/{}".format(dna_directory, character_name),
            const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS,
        )
        for possible_thumbnail_file_path in possible_thumbnail_file_paths:
            if utils.path_exists(possible_thumbnail_file_path):
                self.thumbnail_select_section.set_file_path(possible_thumbnail_file_path)
                break

    @QtCore.Slot()
    def slot_select_thumbnail_file(self):
        filter = utils.generate_file_filter(const.SUPPORTED_THUMBNAIL_FILE_EXTENSIONS, filter_name="缩略图")
        file_path = self.select_file(filter=filter)
        if not file_path:
            return
        self.thumbnail_select_section.set_file_path(file_path)

    @property
    def dna_data(self):
        return self._dna_data

    def accept(self):
        self._dna_data = {}

        dna_file_path = self.dna_select_section.get_file_path()
        if not dna_file_path:
            dayu_widgets.MToast.error("角色文件未选择", parent=self)
            return
        self._dna_data.update({"dna_file_path": dna_file_path})

        thumbnail_file_path = self.thumbnail_select_section.get_file_path()
        if not thumbnail_file_path:
            dayu_widgets.MToast.error("角色缩略图未选择", parent=self)
            return
        self._dna_data.update({"thumbnail_file_path": thumbnail_file_path})

        try:
            self._dna_data.update(self.dna_info_section.info_config)
        except Exception as e:
            dayu_widgets.MToast.error(str(e), parent=self)
            return

        super(DNAAddDialog, self).accept()


class DNAInfoEditDialog(DNAAddDialog):
    def __init__(self, dna_config={}, parent=None):
        self._dna_config = dna_config
        super(DNAInfoEditDialog, self).__init__(parent)
        self.setWindowTitle("编辑角色信息")

    def setup_dna_select_section(self):
        self.dna_select_section = FileSelectSectionWidget(
            section_name="选择角色文件的路径",
            hint_text="选择角色文件",
            parent=self,
        )
        if self._dna_config.get("dna_file_path", ""):
            self.dna_select_section.set_file_path(self._dna_config.get("dna_file_path", ""))
        self.main_layout.addWidget(self.dna_select_section)

    def setup_thumbnail_select_section(self):
        self.thumbnail_select_section = ThumbnailSelectSectionWidget(
            section_name="选择DNA缩略图的路径",
            hint_text="选择DNA缩略图",
            parent=self,
        )
        if self._dna_config.get("thumbnail_file_path", ""):
            self.thumbnail_select_section.set_file_path(self._dna_config.get("thumbnail_file_path", ""))
        self.main_layout.addWidget(self.thumbnail_select_section)

    def setup_dna_info_section(self):
        dna_info_data = const.MOCK_DNA_FILE_DATA_CONFIG
        self.dna_info_section = InfoSectionWidget(dna_info_data, section_name="填写DNA信息", parent=self)
        for key, value in self._dna_config.items():
            if key in self.dna_info_section.info_keys:
                self.dna_info_section.update_info(key, value)
        self.main_layout.addWidget(self.dna_info_section)
        self.setup_tag_info_section()

    def setup_tag_info_section(self):
        character_tags = tag_util.read_role_tags(
            os.path.dirname(self._dna_config["dna_file_path"]),
            self._dna_config["character_name"],
        )
        all_tags = tag_util.get_all_tags()
        self.tag_section = TagSelectSectionWidget(all_tags, section_name="角色标签", selected_tags=character_tags)
        self.main_layout.addWidget(self.tag_section)


@MStyleMixin.cls_wrapper
class DNALibWidget(QtWidgets.QWidget):
    sig_dna_start_merge = QtCore.Signal(dict)
    sig_batch_dna_start_merge = QtCore.Signal(list)

    _instance = None

    @staticmethod
    def instance():
        if DNALibWidget._instance is None:
            DNALibWidget._instance = DNALibWidget()
        return DNALibWidget._instance

    def __init__(self, parent=None):
        self._dna_list = []
        self._dna_info_list = []
        self.all_tags = []
        self.selected_tags = []
        self._thumbnail_size = 128
        super(DNALibWidget, self).__init__(parent=parent)
        self.setup_ui()

        lib_final_data = lib_util.read_resource_path()
        lib_util.set_default_lib("dna_info_list", lib_final_data)
        dayu_utils.add_settings(
            const.ORGANIZATION_NAME,
            const.PACKAGE_NAME,
            event_name="hideEvent",
        )(DNALibWidget)

        DNALibWidget.bind(
            self,
            "dna_info_list",
            self,
            "dna_info_list",
            default=[],
            formatter=self.dna_info_list_formatter,
        )
        self.update_all_tags()

    def amend_dna_resource_path(self, resource_final_data, final_data):
        resource_paths = {data["character_name"]: data["dna_file_path"] for data in resource_final_data}
        thumbnail_paths = {data["character_name"]: data["thumbnail_file_path"] for data in resource_final_data}

        for i in range(len(final_data)):
            char_name = final_data[i]["character_name"]
            if char_name in resource_paths:
                if final_data[i]["dna_file_path"] != resource_paths[char_name]:
                    final_data[i]["dna_file_path"] = resource_paths[char_name]
                    final_data[i]["thumbnail_file_path"] = thumbnail_paths[char_name]

        return final_data

    def setup_ui(self):
        self.setup_layouts()
        self.setup_widgets()
        self.setup_signals()

    def setup_widgets(self):
        self.setup_func_bar()
        self.setup_main_big_view()

    def setup_layouts(self):
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(5)
        self.setLayout(self.main_layout)

    def setup_signals(self):
        self.attr_select_menu.triggered.connect(lambda action: self.slot_attr_selection_changed(action.text()))

    def setup_func_bar(self):
        self.func_bar_container = QtWidgets.QFrame()
        self.func_bar_layout = QtWidgets.QHBoxLayout()
        self.func_bar_layout.setContentsMargins(0, 0, 0, 0)
        self.func_bar_container.setLayout(self.func_bar_layout)

        self.setup_settings_button()
        self.func_bar_layout.addStretch()
        self.setup_attr_drop_list()
        # TODO:Sort Info
        # self.setup_sort_drop_list()
        self.setup_filter_button()

        self.main_layout.addWidget(self.func_bar_container)

    def setup_settings_button(self):
        """Setup settings button with gear icon for configuring local library path"""
        # Import third-party modules

        self.settings_button = dayu_widgets.MToolButton().icon_only().svg("setting.svg")
        self.settings_button.setToolTip("配置个人库路径")
        self.settings_button.clicked.connect(self.slot_show_settings_dialog)
        self.func_bar_layout.addWidget(self.settings_button)

    def slot_show_settings_dialog(self):
        """Show dialog for configuring local library path"""
        current_path = lib_util.get_local_lib_path()

        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("配置个人库路径")
        dialog.setMinimumWidth(500)

        layout = QtWidgets.QVBoxLayout()
        dialog.setLayout(layout)

        form_layout = QtWidgets.QFormLayout()

        path_edit = QtWidgets.QLineEdit()
        path_edit.setText(current_path)
        path_edit.setMinimumWidth(350)

        browse_button = QtWidgets.QPushButton("浏览...")
        browse_button.clicked.connect(lambda: self.browse_folder(path_edit))

        path_layout = QtWidgets.QHBoxLayout()
        path_layout.addWidget(path_edit)
        path_layout.addWidget(browse_button)

        form_layout.addRow("个人库路径:", path_layout)
        layout.addLayout(form_layout)

        # Add note about library structure
        note_label = QtWidgets.QLabel("注意: 个人库需要按照角色文件夹结构组织，每个角色文件夹中包含DNA文件和对应的缩略图文件。")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)

        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        button_box.accepted.connect(lambda: self.save_local_library_path(path_edit.text(), dialog))
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        dialog.exec_()

    def browse_folder(self, line_edit):
        """Open folder browser dialog and set selected path to line edit"""
        folder_path = QtWidgets.QFileDialog.getExistingDirectory(
            self,
            "选择个人库文件夹",
            line_edit.text() or os.path.expanduser("~"),
        )
        if folder_path:
            line_edit.setText(folder_path)

    def save_local_library_path(self, path, dialog):
        """Save local library path and reload DNA list"""
        if path and not os.path.exists(path):
            QtWidgets.QMessageBox.warning(self, "路径错误", "所选路径不存在，请选择有效的文件夹。")
            return

        # Save the path
        lib_util.set_local_lib_path(path)

        # Reload DNA list
        self.reload_dna_list()

        dialog.accept()

    def reload_dna_list(self):
        """Reload DNA list from all resource paths"""
        # Read resource paths
        lib_final_data = lib_util.read_resource_path()

        # Update the DNA info list
        self._dna_info_list = []
        self._dna_list = []

        for dna_info in lib_final_data:
            self._dna_info_list.append(dna_info)
            # Create a copy of dna_info without resource_path for DnaFile initialization
            dna_file_info = dna_info.copy()
            if "resource_path" in dna_file_info:
                dna_file_info.pop("resource_path")
            dna = DnaFile(**dna_file_info)
            self._dna_list.append(dna)

        # Format the DNA info list to set icons
        self._dna_info_list = self.dna_info_list_formatter(self._dna_info_list)

        # Update view
        self.update_view_data_list()

    def setup_attr_drop_list(self):
        self.attr_drop_list = MStyleMixin.cls_wrapper(dayu_widgets.MComboBox)().small()
        self.attr_drop_list.frameless().border_radius(5)

        self.attr_drop_list.setSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.attr_drop_list.setMinimumWidth(150)

        self.attr_select_menu = dayu_widgets.MMenu(parent=self.attr_drop_list)

        dna_types_menu = [const.ALL_TYPE_NAME]
        for item in const.DNA_TYPE_ALL.keys():
            dna_types_menu.append(item)

        self.attr_select_menu.set_data(dna_types_menu)
        self.attr_drop_list.set_menu(self.attr_select_menu)
        self._current_attr_filter = dna_types_menu[0]
        self.func_bar_layout.addWidget(self.attr_drop_list)

        self.attr_drop_list.set_placeholder(self._current_attr_filter)

    def setup_sort_drop_list(self):
        self.sort_drop_list = MStyleMixin.cls_wrapper(dayu_widgets.MComboBox)().small()
        self.sort_drop_list.frameless().border_radius(5)
        self.sort_drop_list.set_placeholder("排序")
        self.sort_drop_list.setSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.func_bar_layout.addWidget(self.sort_drop_list)

    def setup_filter_button(self):
        self.filter_button = MFilterComboButton("筛选")
        self.filter_button.setSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.filter_button.clicked.connect(self.show_tag_filter_popup)
        self.func_bar_layout.addWidget(self.filter_button)

    def show_tag_filter_popup(self):
        if hasattr(self, "_tag_filter_popup") and self._tag_filter_popup.isVisible():
            self._tag_filter_popup.hide()
            return
        all_tags = self.get_all_tags()
        self._tag_filter_popup = TagFilterPopupWidget(all_tags, self.selected_tags, parent=self)
        self._tag_filter_popup.sig_tags_changed.connect(self.on_tags_changed)
        btn_width = self.filter_button.width()
        self._tag_filter_popup.setFixedWidth(btn_width)
        btn = self.filter_button
        pos = btn.mapToGlobal(QtCore.QPoint(0, btn.height()))
        self._tag_filter_popup.move(pos)
        self._tag_filter_popup.show()
        self._tag_filter_popup.setFocus()

    def on_tags_changed(self, tags):
        self.selected_tags = tags
        self.update_tag_filter_bar()
        self.update_view_data_list()

    def get_all_tags(self):
        return self.all_tags

    def update_all_tags(self):
        tag_set = set()
        for dna in self._dna_info_list:
            for tag in dna.get("tags", []):
                tag_set.add(tag)
        self.all_tags = list(tag_set)
        return self.all_tags

    def update_tag_filter_bar(self):
        if hasattr(self, "tag_filter_bar"):
            self.main_layout.removeWidget(self.tag_filter_bar)
            self.tag_filter_bar.deleteLater()
        self.tag_filter_bar = QtWidgets.QWidget()
        layout = MFlowLayout()
        self.tag_filter_bar.setLayout(layout)
        for tag in self.selected_tags:
            tag_widget = TagWidget(tag)
            tag_widget.sig_remove.connect(lambda t=tag: self.remove_tag_filter(t))
            layout.addWidget(tag_widget)
        self.main_layout.insertWidget(1, self.tag_filter_bar)  # 插在功能栏下方

    def remove_tag_filter(self, tag):
        self.selected_tags.remove(tag)
        self.update_tag_filter_bar()
        self.update_view_data_list()

    def setup_main_big_view(self):
        self.dna_lib_view = dayu_widgets.MItemViewSet(view_type=DNALibBigView)

        self.dna_lib_view.item_view.border_radius(5).frameless()
        self.dna_lib_view.item_view.background_color(const.DAYU_BG_IN_COLOR)
        self.dna_lib_view.item_view.border_radius(4, "::item")
        self.dna_lib_view.item_view.padding(5, "::item")
        self.dna_lib_view.item_view.background_color(const.DAYU_BG_COLOR, "::item")
        self.dna_lib_view.item_view.background_color(const.DAYU_BG_OUT_COLOR, "::item:hover")
        self.dna_lib_view.item_view.background_color(const.PRIMARY_COLOR, "::item:selected")

        self.dna_lib_view.item_view.setIconSize(QtCore.QSize(self._thumbnail_size, self._thumbnail_size))
        self.dna_lib_view.item_view.setGridSize(QtCore.QSize(self._thumbnail_size + 20, self._thumbnail_size + 40))
        self.dna_lib_view.item_view.setWrapping(True)
        self.dna_lib_view.item_view.setFlow(QtWidgets.QListView.LeftToRight)
        self.dna_lib_view.item_view.setLayoutMode(QtWidgets.QListView.Batched)
        self.dna_lib_view.item_view.setSpacing(10)
        self.dna_lib_view.set_header_list(
            [
                {
                    "label": "character_name",
                    "key": "character_name",
                    "searchable": True,
                    "draggable": True,
                    "font": lambda x, y: {"underline": False},
                    "icon": lambda x, y: y.get("icon"),
                },
                {
                    "label": "dna_file_path",
                    "key": "dna_file_path",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "thumbnail_file_path",
                    "key": "thumbnail_file_path",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "dna_id",
                    "key": "dna_id",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "age",
                    "key": "age",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "gender",
                    "key": "gender",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "race",
                    "key": "race",
                    "draggable": True,
                    "data": self.data_formatter,
                },
                {
                    "label": "is_official_dna",
                    "key": "is_official_dna",
                    "draggable": True,
                    "data": self.data_formatter,
                },
            ],
        )
        self.dna_lib_view.setup_data(self._dna_list)
        self.selection_model = QtCore.QItemSelectionModel(self.dna_lib_view.sort_filter_model)
        self.dna_lib_view.item_view.setSelectionModel(self.selection_model)
        self.dna_lib_view.item_view.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.dna_lib_view.item_view.sig_context_menu.connect(self.slot_lib_view_context_menu)

        self.main_layout.addWidget(self.dna_lib_view)

    def dna_file_path_formatter(self, value, full_data):
        return value

    def thumbnail_file_path_formatter(self, value, full_data):
        return value

    def data_formatter(self, value, full_data):
        return value

    def set_dna_info_list(self, dna_info_list):
        self._dna_info_list = dna_info_list

        for i, dna_info in enumerate(dna_info_list):
            filtered_dna_info = dna_info.copy()
            filtered_dna_info.pop("icon", None)

            expected_keys = [
                "dna_id",
                "dna_file_path",
                "thumbnail_file_path",
                "modified_date",
                "character_name",
                "gender",
                "age",
                "race",
                "is_official_dna",
                "thumbnail_size",
                "tags",
            ]
            unexpected_keys = [key for key in filtered_dna_info.keys() if key not in expected_keys]
            for key in unexpected_keys:
                filtered_dna_info.pop(key, None)

            dna = DnaFile(**filtered_dna_info)
            self._dna_list.append(dna)
        self.update_view_data_list()

    def get_dna_info_list(self):
        dna_info_list = []
        for dna in self._dna_list:
            dna_info_list.append(dna.to_dict())
        return dna_info_list

    def dna_info_list_formatter(self, dna_info_list):
        """This formatter is used to set dna icon, and recover list."""

        if not isinstance(dna_info_list, list):
            dna_info_list = [dna_info_list]

        for dna_info in dna_info_list:
            thumbnail_file_path = dna_info.get("thumbnail_file_path", None)
            if thumbnail_file_path:
                name = lib_util.find_small_icon(dna_info)
                small_icon_path = None
                if name:
                    small_icon_path = os.path.join(os.environ.get(const.PACKAGES_RESOURCE_NAME), "icons", name)
                dna_info["icon"] = self.get_thumbnail_icon(thumbnail_file_path, small_icon_path)

            # Ensure DNA info contains resource path identifier
            if not dna_info.get("resource_path"):
                # Determine resource path based on file path
                dna_file_path = dna_info.get("dna_file_path", "")
                if dna_file_path:
                    if const.PUBLIC_LIB_PATH in dna_file_path:
                        dna_info["resource_path"] = "PUBLIC_LIB_PATH"
                    elif const.PROJECT_LIB_PATH in dna_file_path:
                        dna_info["resource_path"] = "PROJECT_LIB_PATH"

        return dna_info_list

    dna_info_list = QtCore.Property(list, fget=get_dna_info_list, fset=set_dna_info_list)

    def get_thumbnail_icon(self, thumbnail_file_path, small_icon=None):
        if thumbnail_file_path:
            original_pixmap = QtGui.QPixmap(thumbnail_file_path)
            if original_pixmap.isNull():
                placeholder = QtGui.QPixmap(self._thumbnail_size, self._thumbnail_size)
                placeholder.fill(QtCore.Qt.lightGray)
                return QtGui.QIcon(placeholder)

            cropped_pixmap = utils.crop_pixmap_to_square(original_pixmap, self._thumbnail_size)

            if not cropped_pixmap:
                placeholder = QtGui.QPixmap(self._thumbnail_size, self._thumbnail_size)
                placeholder.fill(QtCore.Qt.lightGray)
                cropped_pixmap = placeholder
            if cropped_pixmap.width() != self._thumbnail_size or cropped_pixmap.height() != self._thumbnail_size:
                cropped_pixmap = cropped_pixmap.scaled(
                    self._thumbnail_size,
                    self._thumbnail_size,
                    aspectRatioMode=QtCore.Qt.IgnoreAspectRatio,
                    transformMode=QtCore.Qt.SmoothTransformation,
                )

            if small_icon:
                original_pixmap_icon = QtGui.QPixmap(small_icon)
                if not original_pixmap_icon.isNull():
                    cropped_pixmap_icon = utils.crop_pixmap_to_square(
                        original_pixmap_icon,
                        int(self._thumbnail_size * 0.3),
                    )
                    if cropped_pixmap_icon:
                        painter = QtGui.QPainter(cropped_pixmap)
                        painter.drawPixmap(
                            self._thumbnail_size - cropped_pixmap_icon.width() - 2,
                            2,
                            cropped_pixmap_icon,
                        )
                        painter.end()

            return QtGui.QIcon(cropped_pixmap)
        return None

    def create_dna(self, dna_dict):
        expected_keys = [
            "dna_id",
            "dna_file_path",
            "thumbnail_file_path",
            "modified_date",
            "character_name",
            "gender",
            "age",
            "race",
            "is_official_dna",
            "thumbnail_size",
            "tags",
        ]
        dna_dict_copy = dna_dict.copy()
        unexpected_keys = [key for key in dna_dict_copy.keys() if key not in expected_keys]
        for key in unexpected_keys:
            dna_dict_copy.pop(key, None)

        new_dna = DnaFile(**dna_dict_copy)
        new_dna_info = dna_dict.copy()
        thumbnail_file_path = dna_dict.get("thumbnail_file_path", None)
        if thumbnail_file_path:
            name = lib_util.find_small_icon(new_dna_info)
            small_icon_path = None
            if name:
                small_icon_path = os.path.join(
                    os.environ.get(const.PACKAGES_RESOURCE_NAME),
                    "icons",
                    name,
                )
            new_dna_info["icon"] = self.get_thumbnail_icon(thumbnail_file_path, small_icon_path)
        return new_dna, new_dna_info

    def add_dna(self, dna_dict):
        dna_dict["dna_id"] = len(self._dna_list)
        new_dna, new_dna_info = self.create_dna(dna_dict)

        self._dna_list.append(new_dna)
        self._dna_info_list.append(new_dna_info)
        self.update_view_data_list()

    def edit_dna(self, dna_dict):
        new_dna, new_dna_info = self.create_dna(dna_dict)
        self._dna_list[dna_dict["dna_id"]] = new_dna
        self._dna_info_list[dna_dict["dna_id"]] = new_dna_info
        self.update_view_data_list()

    def del_dna(self, dna_id):
        del self._dna_list[dna_id]
        del self._dna_info_list[dna_id]
        self.update_view_data_list()

    def update_view_data_list(self):
        filtered_list = lib_util.filter_dna_type(self._dna_info_list, self._current_attr_filter)
        if hasattr(self, "selected_tags") and self.selected_tags:
            selected_tags = set(self.selected_tags)
            filtered_list = [dna for dna in filtered_list if selected_tags & set(dna.get("tags") or [])]
        self.dna_lib_view.setup_data(filtered_list)
        self.write_settings()

    @QtCore.Slot(str)
    def slot_attr_selection_changed(self, text):
        """Handle attribute selection changes in the dropdown menu.

        Args:
            text (str): Selected option text ('All', 'Public Library' or 'Project Library')
        """
        self._current_attr_filter = text
        self.attr_drop_list.set_placeholder(text)
        self.update_view_data_list()

    def slot_lib_view_context_menu(self, context_info):
        pos = context_info.get("pos")
        clicked_index = context_info.get("index")
        selected_indexes = self.selected_indexes

        if clicked_index not in selected_indexes or len(selected_indexes) < 2:
            self.selection_model.clearSelection()
            self.selection_model.select(clicked_index, QtCore.QItemSelectionModel.Select)
            self.slot_single_item_context_menu(pos, clicked_index)
        else:
            self.slot_multi_item_context_menu(pos, selected_indexes)

    def slot_single_item_context_menu(self, pos, index):
        if not index.isValid():
            return

        dna_info = self.get_dna_info_by_index(index)

        menu = QtWidgets.QMenu(self)
        edit_action = self.create_edit_tag_action(dna_info)
        menu.addAction(edit_action)
        menu.exec_(pos)

    def slot_multi_item_context_menu(self, pos, indexes):
        if not all(index.isValid() for index in indexes):
            return

        dna_infos = self.get_dna_infos_by_indexes(indexes)

        menu = QtWidgets.QMenu(self)
        batch_edit_tag_action = self.create_batch_tag_edit_action(dna_infos)
        menu.addAction(batch_edit_tag_action)
        menu.exec_(pos)

    def get_dna_info_by_index(self, index):
        model = self.dna_lib_view.item_view.model()
        dna_info = {}
        for i in range(model.columnCount()):
            info = model.data(model.index(index.row(), i))
            dna_info[model.headerData(i, QtCore.Qt.Horizontal)] = info
        dna_info["dna_id"] = index.row()
        return dna_info

    def get_dna_infos_by_indexes(self, indexes):
        dna_infos = []
        for index in indexes:
            dna_info = self.get_dna_info_by_index(index)
            dna_infos.append(dna_info)
        return dna_infos

    def create_edit_tag_action(self, dna_info):
        action = QtWidgets.QAction("编辑DNA标签", self)
        action.triggered.connect(lambda: self.slot_edit_dna_tag(dna_info))
        return action

    def create_batch_tag_edit_action(self, dna_infos):
        action = QtWidgets.QAction("批量编辑标签", self)
        action.triggered.connect(lambda: self.slot_batch_edit_tag(dna_infos))
        return action

    def slot_batch_edit_tag(self, dna_infos):
        if not dna_infos:
            return

        all_tags = tag_util.get_all_tags()
        all_character_tags = []
        for dna_info in dna_infos:
            tags = tag_util.read_role_tags(
                os.path.dirname(dna_info["dna_file_path"]),
                dna_info["character_name"],
            )
            all_character_tags.append(set(tags))

        common_tags = set.intersection(*all_character_tags) if all_character_tags else set()

        dialog = TagEditDialog(all_tags, common_tags, windowtitle="批量修改角色标签", parent=self)
        if dialog.exec_():
            selected_tags = set(dialog.get_selected_tags())
            removed_tags = common_tags - selected_tags
            added_tags = selected_tags - common_tags

            def html_tag(tag, color=None):
                if color:
                    return f'<span style="color:{color};">{tag}</span>'
                return tag

            if common_tags:
                old_tags_html = []
                for tag in sorted(common_tags):
                    if tag in removed_tags:
                        old_tags_html.append(html_tag(tag, "red"))
                    else:
                        old_tags_html.append(html_tag(tag))
                old_tags_str = "，".join(old_tags_html)
            else:
                old_tags_str = "无"

            if selected_tags:
                new_tags_html = []
                for tag in sorted(selected_tags):
                    if tag in added_tags:
                        new_tags_html.append(html_tag(tag, "green"))
                    else:
                        new_tags_html.append(html_tag(tag))
                new_tags_str = "，".join(new_tags_html)
            else:
                new_tags_str = "无"

            msg = (
                f"你将批量修改 <b>{len(dna_infos)}</b> 个角色的标签。<br>"
                f"原有共同标签: {old_tags_str}<br>"
                f"修改后共同标签: {new_tags_str}<br><br>"
                "<span style='color:red;'>红色</span>为将被删除的标签，"
                "<span style='color:green;'>绿色</span>为新添加的标签。<br><br>"
                "是否确认批量修改？"
            )
            reply = QtWidgets.QMessageBox.question(
                self,
                "二次确认",
                msg,
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                QtWidgets.QMessageBox.No,
            )
            if reply != QtWidgets.QMessageBox.Yes:
                return  # 用户取消

            for idx, dna_info in enumerate(dna_infos):
                old_tags = all_character_tags[idx]
                new_tags = (old_tags - common_tags) | selected_tags
                tag_util.write_role_tags(
                    os.path.dirname(dna_info["dna_file_path"]),
                    dna_info["character_name"],
                    list(new_tags),
                )
        self.reload_dna_list()
        self.update_all_tags()

    def slot_edit_dna_tag(self, dna_info):
        if not dna_info:
            return

        tags = tag_util.read_role_tags(
            os.path.dirname(dna_info["dna_file_path"]),
            dna_info["character_name"],
        )
        all_tags = tag_util.get_all_tags()
        edit_dna_dialog = TagEditDialog(all_tags, tags, windowtitle="修改角色标签", parent=self)
        if edit_dna_dialog.exec_():
            character_tags = set(edit_dna_dialog.get_selected_tags())
            tag_util.write_role_tags(
                os.path.dirname(dna_info["dna_file_path"]),
                dna_info["character_name"],
                list(character_tags),
            )
        self.reload_dna_list()
        self.update_all_tags()

    @property
    def selected_indexes(self):
        return self.selection_model.selectedIndexes()

    @property
    def selected_dna_infos(self):
        return self.get_dna_infos_by_indexes(self.selected_indexes)
