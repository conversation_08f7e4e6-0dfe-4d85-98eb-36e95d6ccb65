"""Debug Hair Studio in Live Environment.

This script diagnoses Asset Library data display issues in the actual Maya/Qt environment.
Run this after starting with debug_start_hair_dev_direct.cmd
"""

import sys
import os
import logging

# Add the project root to the Python path
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

def setup_detailed_logging():
    """Set up very detailed logging to catch all issues."""
    # Create a detailed log file
    log_file = os.path.join(project_root, "hair_studio_live_debug.log")
    
    # Set up logging with multiple handlers
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Enable debug logging for all hair studio modules
    hair_studio_modules = [
        'cgame_avatar_factory.hair_studio',
        'cgame_avatar_factory.hair_studio.manager.hair_manager',
        'cgame_avatar_factory.hair_studio.data.mock_data_manager',
        'cgame_avatar_factory.hair_studio.ui.asset_library.asset_library',
        'cgame_avatar_factory.hair_studio.ui.base_hair_tab',
        'cgame_avatar_factory.hair_studio.ui.hair_studio_tab',
        'cgame_avatar_factory.hair_studio.ui.asset_library.asset_item',
    ]
    
    for module_name in hair_studio_modules:
        logger = logging.getLogger(module_name)
        logger.setLevel(logging.DEBUG)
    
    print(f"✓ 详细日志已启用，日志文件: {log_file}")
    return log_file

def test_data_layer():
    """Test the data layer components."""
    print("\n" + "=" * 60)
    print("数据层测试")
    print("=" * 60)
    
    try:
        # Test MockDataManager
        print("✓ 测试 MockDataManager...")
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        
        mock_manager = MockDataManager()
        all_assets = mock_manager.get_assets()
        print(f"✓ MockDataManager 总资产: {len(all_assets)}")
        
        for asset_type in ["card", "xgen", "curve"]:
            assets = mock_manager.get_assets(asset_type)
            print(f"  - {asset_type}: {len(assets)} 个")
            for asset in assets:
                print(f"    * {asset['name']} (ID: {asset['id']})")
        
        # Test HairManager
        print("\n✓ 测试 HairManager...")
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        hair_manager = HairManager()
        print("✓ HairManager 创建成功")
        
        for asset_type in ["card", "xgen", "curve"]:
            assets = hair_manager.get_assets(asset_type)
            print(f"  - HairManager {asset_type}: {len(assets)} 个")
        
        return True, hair_manager
        
    except Exception as e:
        print(f"✗ 数据层测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_asset_library_creation(hair_manager):
    """Test AssetLibrary creation and data loading."""
    print("\n" + "=" * 60)
    print("AssetLibrary 创建和数据加载测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        # Test each asset type
        for asset_type in ["card", "xgen", "curve"]:
            print(f"\n✓ 测试 {asset_type.upper()} AssetLibrary...")
            
            # Create AssetLibrary
            asset_library = AssetLibrary(asset_type, hair_manager)
            print(f"✓ {asset_type} AssetLibrary 创建成功")
            
            # Check initial assets
            print(f"✓ 初始资产数量: {len(asset_library.assets)}")
            
            # Test refresh
            print("✓ 测试 refresh()...")
            asset_library.refresh()
            print(f"✓ 刷新后资产数量: {len(asset_library.assets)}")
            
            # List assets
            if asset_library.assets:
                print("✓ 资产列表:")
                for asset in asset_library.assets:
                    print(f"    * {asset['name']} (ID: {asset['id']})")
            else:
                print("❌ 没有找到资产!")
                return False
            
            # Test UI grid update
            print("✓ 测试 UI 网格更新...")
            asset_library._update_assets_grid()
            
            # Check grid layout
            grid_count = asset_library.assets_layout.count()
            print(f"✓ 网格中的组件数量: {grid_count}")
            
            if grid_count == 0:
                print("❌ 网格中没有组件!")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ AssetLibrary 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_hair_studio_tab_integration(hair_manager):
    """Test full HairStudioTab integration."""
    print("\n" + "=" * 60)
    print("HairStudioTab 集成测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        
        # Create HairStudioTab
        print("✓ 创建 HairStudioTab...")
        hair_studio_tab = HairStudioTab(hair_manager)
        print("✓ HairStudioTab 创建成功")
        
        # Test each tab
        tab_names = ["Card", "XGen", "Curve"]
        for i, tab_name in enumerate(tab_names):
            print(f"\n✓ 测试 {tab_name} 标签页...")
            
            # Switch to tab
            hair_studio_tab.setCurrentIndex(i)
            current_tab = hair_studio_tab.get_current_tab()
            
            print(f"✓ 当前标签页类型: {current_tab.hair_type}")
            print(f"✓ AssetLibrary 资产数量: {len(current_tab.asset_library.assets)}")
            
            # List assets
            if current_tab.asset_library.assets:
                print("✓ 资产列表:")
                for asset in current_tab.asset_library.assets:
                    print(f"    * {asset['name']}")
            else:
                print("❌ 标签页中没有资产!")
                
                # Try manual refresh
                print("✓ 尝试手动刷新...")
                current_tab.refresh_asset_library()
                print(f"✓ 手动刷新后资产数量: {len(current_tab.asset_library.assets)}")
                
                if not current_tab.asset_library.assets:
                    print("❌ 手动刷新后仍然没有资产!")
                    return False
            
            # Check UI components
            grid_count = current_tab.asset_library.assets_layout.count()
            print(f"✓ UI 网格组件数量: {grid_count}")
            
            # Check if widgets are visible
            container = current_tab.asset_library.assets_container
            print(f"✓ 容器可见性: {container.isVisible()}")
            print(f"✓ 容器大小: {container.size().width()}x{container.size().height()}")
        
        return True, hair_studio_tab
        
    except Exception as e:
        print(f"✗ HairStudioTab 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_ui_visibility_and_layout(hair_studio_tab):
    """Test UI visibility and layout issues."""
    print("\n" + "=" * 60)
    print("UI 可见性和布局测试")
    print("=" * 60)
    
    try:
        # Get the card tab for detailed testing
        hair_studio_tab.setCurrentIndex(0)  # Card tab
        card_tab = hair_studio_tab.get_current_tab()
        asset_library = card_tab.asset_library
        
        print("✓ 详细 UI 组件检查...")
        
        # Check main components
        print(f"✓ AssetLibrary 可见性: {asset_library.isVisible()}")
        print(f"✓ AssetLibrary 大小: {asset_library.size().width()}x{asset_library.size().height()}")
        
        # Check container
        container = asset_library.assets_container
        print(f"✓ 资产容器可见性: {container.isVisible()}")
        print(f"✓ 资产容器大小: {container.size().width()}x{container.size().height()}")
        
        # Check layout
        layout = asset_library.assets_layout
        print(f"✓ 布局项目数量: {layout.count()}")
        
        # Check individual asset items
        for i in range(layout.count()):
            item = layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                print(f"✓ 资产项目 {i}: {widget.__class__.__name__}")
                print(f"    - 可见性: {widget.isVisible()}")
                print(f"    - 大小: {widget.size().width()}x{widget.size().height()}")
                
                # Check if it's an AssetItem
                if hasattr(widget, 'asset_data'):
                    print(f"    - 资产名称: {widget.asset_data.get('name', 'Unknown')}")
        
        # Check style sheets
        print(f"✓ AssetLibrary 样式表: {asset_library.styleSheet()}")
        print(f"✓ 容器样式表: {container.styleSheet()}")
        
        return True
        
    except Exception as e:
        print(f"✗ UI 可见性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function for live environment."""
    print("=" * 80)
    print("毛发工作室实时环境诊断")
    print("=" * 80)
    print("注意: 请确保已使用 debug_start_hair_dev_direct.cmd 启动环境")
    
    # Set up detailed logging
    log_file = setup_detailed_logging()
    
    # Run diagnostic tests
    tests = [
        ("数据层", test_data_layer),
    ]
    
    hair_manager = None
    hair_studio_tab = None
    
    # Test data layer first
    success, hair_manager = test_data_layer()
    if not success or not hair_manager:
        print("\n❌ 数据层测试失败，无法继续")
        return
    
    # Test AssetLibrary creation
    if not test_asset_library_creation(hair_manager):
        print("\n❌ AssetLibrary 创建测试失败")
        return
    
    # Test HairStudioTab integration
    success, hair_studio_tab = test_hair_studio_tab_integration(hair_manager)
    if not success or not hair_studio_tab:
        print("\n❌ HairStudioTab 集成测试失败")
        return
    
    # Test UI visibility
    if not test_ui_visibility_and_layout(hair_studio_tab):
        print("\n❌ UI 可见性测试失败")
        return
    
    print("\n" + "=" * 80)
    print("诊断完成")
    print("=" * 80)
    print("🎉 所有测试通过!")
    print("✅ 数据层正常工作")
    print("✅ AssetLibrary 正常创建")
    print("✅ HairStudioTab 正常集成")
    print("✅ UI 组件正常显示")
    print(f"\n📋 详细日志保存在: {log_file}")
    print("\n如果毛发素材库仍然没有显示数据，可能的原因:")
    print("1. UI 布局问题 - 检查窗口大小和滚动区域")
    print("2. 样式表问题 - 检查是否有样式隐藏了组件")
    print("3. 刷新时机问题 - 尝试手动切换标签页")
    print("4. Maya 集成问题 - 检查 Maya 环境是否正确加载")

if __name__ == "__main__":
    main()
