"""Test Asset Library Data Flow.

This script tests the data flow from MockDataManager to AssetLibrary
to identify why assets are not displaying in the UI.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

def test_mock_data_manager_assets():
    """Test MockDataManager asset retrieval."""
    print("=" * 60)
    print("MockDataManager 资产数据测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        
        # Create manager
        manager = MockDataManager()
        print("✓ MockDataManager 创建成功")
        
        # Test getting all assets
        all_assets = manager.get_assets()
        print(f"✓ 所有资产数量: {len(all_assets)}")
        
        for asset in all_assets:
            print(f"  - {asset['name']} ({asset['asset_type']}) ID: {asset['id']}")
        
        # Test filtering by type
        card_assets = manager.get_assets("card")
        xgen_assets = manager.get_assets("xgen")
        curve_assets = manager.get_assets("curve")
        
        print(f"\n按类型过滤:")
        print(f"  - Card 资产: {len(card_assets)} 个")
        print(f"  - XGen 资产: {len(xgen_assets)} 个")
        print(f"  - Curve 资产: {len(curve_assets)} 个")
        
        # Verify data structure
        if card_assets:
            sample_asset = card_assets[0]
            required_fields = ["id", "name", "asset_type", "thumbnail", "file_path"]
            print(f"\n资产数据结构验证 (样本: {sample_asset['name']}):")
            for field in required_fields:
                if field in sample_asset:
                    print(f"  ✓ {field}: {sample_asset[field]}")
                else:
                    print(f"  ✗ 缺少字段: {field}")
        
        return True, all_assets
        
    except Exception as e:
        print(f"✗ MockDataManager 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, []

def test_hair_manager_integration():
    """Test HairManager integration with MockDataManager."""
    print("\n" + "=" * 60)
    print("HairManager 集成测试")
    print("=" * 60)
    
    try:
        # Import without Qt dependencies
        import sys
        import os
        
        # Mock QtCore for testing
        class MockQtCore:
            class QObject:
                def __init__(self, parent=None):
                    pass
            class Signal:
                def __init__(self, *args):
                    pass
                def emit(self, *args):
                    pass
                def connect(self, *args):
                    pass
        
        # Temporarily replace QtCore
        sys.modules['qtpy'] = type('MockModule', (), {})()
        sys.modules['qtpy.QtCore'] = MockQtCore
        sys.modules['qtpy'].QtCore = MockQtCore
        
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create manager
        manager = HairManager()
        print("✓ HairManager 创建成功")
        
        # Test asset retrieval
        all_assets = manager.get_assets()
        print(f"✓ 通过 HairManager 获取所有资产: {len(all_assets)} 个")
        
        # Test filtered retrieval
        card_assets = manager.get_assets("card")
        print(f"✓ Card 资产: {len(card_assets)} 个")
        
        if card_assets:
            print("Card 资产详情:")
            for asset in card_assets:
                print(f"  - {asset['name']} (ID: {asset['id']})")
        
        return True, card_assets
        
    except Exception as e:
        print(f"✗ HairManager 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, []

def test_asset_library_data_processing():
    """Test AssetLibrary data processing logic."""
    print("\n" + "=" * 60)
    print("AssetLibrary 数据处理测试")
    print("=" * 60)
    
    try:
        # Test the data processing logic without UI
        sample_assets = [
            {
                "id": "card_1",
                "name": "Basic Hair Card",
                "asset_type": "card",
                "thumbnail": "card_1_thumb.jpg",
                "file_path": "assets/cards/basic_hair_card.ma",
            },
            {
                "id": "card_2",
                "name": "Curly Hair Card",
                "asset_type": "card",
                "thumbnail": "card_2_thumb.jpg",
                "file_path": "assets/cards/curly_hair_card.ma",
            }
        ]
        
        print(f"✓ 测试数据准备: {len(sample_assets)} 个资产")
        
        # Test filtering logic
        hair_type = "card"
        filtered_assets = [
            asset for asset in sample_assets 
            if asset["asset_type"] == hair_type
        ]
        
        print(f"✓ 类型过滤 ({hair_type}): {len(filtered_assets)} 个资产")
        
        # Test search filtering
        search_text = "basic"
        search_filtered = [
            asset for asset in filtered_assets
            if search_text.lower() in asset["name"].lower()
        ]
        
        print(f"✓ 搜索过滤 ('{search_text}'): {len(search_filtered)} 个资产")
        
        # Test grid layout calculation
        max_columns = 3
        for i, asset in enumerate(filtered_assets):
            row = i // max_columns
            col = i % max_columns
            print(f"  - {asset['name']}: 网格位置 ({row}, {col})")
        
        return True
        
    except Exception as e:
        print(f"✗ AssetLibrary 数据处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_compatibility():
    """Test signal parameter compatibility."""
    print("\n" + "=" * 60)
    print("信号参数兼容性测试")
    print("=" * 60)
    
    try:
        # Test the signal parameter mismatch issue
        sample_asset = {
            "id": "card_1",
            "name": "Basic Hair Card",
            "asset_type": "card",
            "thumbnail": "card_1_thumb.jpg",
            "file_path": "assets/cards/basic_hair_card.ma",
        }
        
        print("✓ 测试资产数据:")
        print(f"  - 名称: {sample_asset['name']}")
        print(f"  - ID: {sample_asset['id']}")
        print(f"  - 类型: {sample_asset['asset_type']}")
        
        # Test what AssetLibrary emits
        print("\n✓ AssetLibrary 发射的信号:")
        print(f"  - asset_selected.emit(asset) -> 完整资产字典")
        print(f"  - 数据类型: {type(sample_asset)}")
        
        # Test what BaseHairTab expects
        print("\n✓ BaseHairTab 期望的参数:")
        print(f"  - _on_asset_selected(asset_id) -> 只需要资产ID")
        print(f"  - 期望类型: str")
        print(f"  - 实际需要: {sample_asset['id']}")
        
        print("\n❌ 发现兼容性问题:")
        print("  - AssetLibrary 发射完整资产字典")
        print("  - BaseHairTab 期望只接收资产ID")
        print("  - 需要修复信号参数不匹配问题")
        
        return False  # This is the issue we need to fix
        
    except Exception as e:
        print(f"✗ 信号兼容性测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始 Asset Library 数据流诊断测试...")
    
    # Run diagnostic tests
    tests = [
        ("MockDataManager 资产数据", test_mock_data_manager_assets),
        ("HairManager 集成", test_hair_manager_integration),
        ("AssetLibrary 数据处理", test_asset_library_data_processing),
        ("信号参数兼容性", test_signal_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results.append(success)
            else:
                results.append(result)
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    if passed == total:
        print(f"✅ 所有测试通过 ({passed}/{total})")
    else:
        print(f"❌ 发现问题 ({passed}/{total} 通过)")
        
        print("\n🔍 已识别的问题:")
        print("1. 信号参数不匹配:")
        print("   - AssetLibrary.asset_selected 发射完整资产字典")
        print("   - BaseHairTab._on_asset_selected 期望资产ID字符串")
        print("   - 解决方案: 修改信号参数或处理方法")
        
        print("\n2. 可能的其他问题:")
        print("   - 检查 AssetLibrary 初始化时是否正确调用 refresh()")
        print("   - 验证 UI 组件是否正确创建和显示")
        print("   - 确认没有异常阻止资产显示")
    
    print("\n测试完成。")
