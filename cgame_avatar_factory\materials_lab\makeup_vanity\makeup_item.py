"""
Makeup Item Module

This module defines the MakeupItem class, which represents a makeup item in the makeup library.
"""

# Import built-in modules
import os

# Import third-party modules
from qtpy import QtGui

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory import utils


class MakeupItem:
    """
    Class representing a makeup item in the makeup library.

    Similar to DnaFile in the DNA library, this class holds information about a makeup item,
    including its path, category, name, and thumbnail.
    """

    def __init__(
        self,
        item_id,
        item_path,
        category,
        name=None,
        thumbnail_path=None,
        thumbnail_size=128,
    ):
        """
        Initialize a MakeupItem.

        Args:
            item_id (int): Unique identifier for the item
            item_path (str): Path to the makeup item folder
            category (str): Category of the makeup item
            name (str, optional): Name of the makeup item. Defaults to folder name.
            thumbnail_path (str, optional): Path to the thumbnail image. Defaults to None.
            thumbnail_size (int, optional): Size of the thumbnail. Defaults to 128.
        """
        self.item_id = item_id
        self.item_path = item_path
        self.category = category
        self.name = name if name else os.path.basename(item_path)
        self.thumbnail_path = thumbnail_path
        self.thumbnail_size = thumbnail_size

        self._valid = False
        self.validate_item()
        self.generate_thumbnail_icon()

    def validate_item(self):
        """Validate that the makeup item path exists."""
        # For dummy test data, allow paths that start with /dummy/
        if self.item_path.startswith("/dummy/"):
            self._valid = True
            return

        # For real data, check if path exists
        if not os.path.exists(self.item_path):
            raise ValueError(f"Makeup item path {self.item_path} does not exist.")

        self._valid = True

    def generate_thumbnail_icon(self):
        """Generate a thumbnail icon for the makeup item."""
        # If a thumbnail path is provided and exists, use it
        if self.thumbnail_path and os.path.exists(self.thumbnail_path):
            # Handle SVG files differently
            if self.thumbnail_path.lower().endswith(".svg"):
                self.icon = QtGui.QIcon(self.thumbnail_path)
                return
            else:
                original_pixmap = QtGui.QPixmap(self.thumbnail_path)
        # Otherwise use the default thumbnail
        else:
            # Check if default thumbnail exists
            if os.path.exists(const.DEFAULT_MAKEUP_THUMBNAIL):
                # Handle SVG files differently
                if const.DEFAULT_MAKEUP_THUMBNAIL.lower().endswith(".svg"):
                    self.icon = QtGui.QIcon(const.DEFAULT_MAKEUP_THUMBNAIL)
                    return
                else:
                    original_pixmap = QtGui.QPixmap(const.DEFAULT_MAKEUP_THUMBNAIL)
            else:
                # Create a colored square as fallback
                original_pixmap = QtGui.QPixmap(self.thumbnail_size, self.thumbnail_size)
                original_pixmap.fill(QtGui.QColor("#2683d9"))  # Use app primary color

        # Crop to square
        cropped_pixmap = utils.crop_pixmap_to_square(original_pixmap, self.thumbnail_size)
        self.icon = QtGui.QIcon(cropped_pixmap)

    def to_dict(self):
        """
        Convert the makeup item to a dictionary.

        Returns:
            dict: Dictionary representation of the makeup item
        """
        if not self._valid:
            return None

        result = {
            "item_id": self.item_id,
            "item_path": self.item_path,
            "category": self.category,
            "name": self.name,
            "thumbnail_path": self.thumbnail_path,
        }

        # 添加图标信息
        if hasattr(self, "icon") and self.icon:
            result["icon"] = self.icon

        return result
