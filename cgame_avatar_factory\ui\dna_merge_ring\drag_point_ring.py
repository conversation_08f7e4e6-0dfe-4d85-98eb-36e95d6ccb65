# Import built-in modules
import logging

# Import third-party modules
import numpy as np
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.constants as const
from cgame_avatar_factory.ui.dna_merge_ring.circle_ui import DNARingWidget
from cgame_avatar_factory.ui.pivot_move import UIPivotMove


class DragPointRingWidget(DNARingWidget):
    sig_point_moved = QtCore.Signal(float, float)
    sig_point_pos_changed = QtCore.Signal(QtCore.QPointF)
    sig_weights_changed = QtCore.Signal(dict)
    sig_pos_changed = QtCore.Signal(dict)
    sig_component_changed = QtCore.Signal(list)
    sig_component_selected = QtCore.Signal(str)

    def __init__(self, radius=200, line_width=20, color=QtGui.QColor(55, 55, 55), parent=None):
        """Initialize the DragPointRingWidget

        Args:
            radius (int, optional): Radius of the ring widget. Defaults to 200.
            line_width (int, optional): Width of the ring line. Defaults to 20.
            color (QColor, optional): Color of the ring. Defaults to QColor(55, 55, 55).
            parent (QWidget, optional): Parent widget. Defaults to None.
        """
        super().__init__(radius=radius, line_width=line_width, color=color, parent=parent)
        self.logger = logging.getLogger(__name__)

        self._point_radius = 10
        self._point_color = QtGui.QColor(0, 120, 215)
        self._point_selected_color = QtGui.QColor(255, 140, 0)
        self._init_point_pos()
        self._is_point_selected = False
        self._dragging = False

        self._ripple_radius = 0
        self._ripple_opacity = 1
        self._ripple_animation = None
        self._show_ripple = True
        self._ripple_pos = QtCore.QPointF(self.width() / 2.0, self.height() / 2.0)
        self._ripple_animation_active = False
        self._continuous_ripple = True

        self._snap_distance = 60
        self._is_snapped = False
        self._original_point_pos = QtCore.QPointF(self.width() / 2.0, self.height() / 2.0)

        self._component_distances = {}
        self._components = []
        self._weights = {}
        self._selected_component = None
        self._last_mouse_pos = None
        self._last_component_pos = None
        self._pending_animation_count = 0
        self._overlay_ready = False

        self._overlay = PivotOverlayWidget(self)
        self._overlay.raise_()

        self._overlay.sig_point_moved.connect(self.sig_point_moved)
        self._overlay.sig_point_pos_changed.connect(self.sig_point_pos_changed)

        self.sig_point_pos_changed.emit(self._point_pos)

    def check_and_update_overlay_ready(self, component):
        """Check and update overlay ready state for a component

        Args:
            component: The component to check animation status
        """
        if hasattr(component, "animation"):
            self._pending_animation_count += 1
            component.animation.finished.connect(self._on_animation_finished)

        self._check_and_update_overlay_ready()

    def _check_and_update_overlay_ready(self):
        """Check and update the overlay ready state

        Updates the overlay ready state based on component count and animation status.
        If ready, initializes weights and makes weight labels visible.
        """
        new_ready_state = len(self.components) >= const.REQUIRED_DNA_COUNT and self._pending_animation_count == 0
        if new_ready_state != self._overlay_ready:
            self._overlay_ready = new_ready_state

        if self._overlay_ready:
            self.update_distances(force=True)
            if len(self.components) >= const.REQUIRED_DNA_COUNT:
                self._update_weights()

                for component in self.components[: const.REQUIRED_DNA_COUNT]:
                    if hasattr(component, "set_weight_visible"):
                        component.set_weight_visible(True)

                self._overlay.raise_()
                self._overlay.update()

    def _on_animation_finished(self):
        """Handle animation finished event

        Resets the pending animation count and updates overlay ready state.
        """
        self._pending_animation_count = 0
        self.logger.debug("Animation finished, resetting animation count to 0")

        self._check_and_update_overlay_ready()

    def add_component_to_pos(self, widget, pos_x, pos_y):
        """Add a component to the specified position

        Args:
            widget: The widget component to add
            pos_x: X coordinate position
            pos_y: Y coordinate position
        """
        super().add_component_to_pos(widget, pos_x, pos_y)
        self.check_and_update_overlay_ready(widget)

    def remove_component(self):
        """Remove the last added component

        Resets overlay ready state and updates distances after component removal.
        """
        super().remove_component()
        self._overlay_ready = False
        self._pending_animation_count = 0
        self.update_distances(force=True)
        if self._overlay:
            self._overlay.update()

    def is_ready_for_overlay(self):
        """Check if the widget is ready for overlay display

        Returns:
            bool: True if the widget has enough components and no pending animations,
                  False otherwise
        """
        if not self.components or len(self.components) < const.REQUIRED_DNA_COUNT:
            return False

        return self._overlay_ready and self._pending_animation_count == 0

    def mousePressEvent(self, event):
        """Handle mouse press events

        Implements left-click snap-to-cursor functionality for the control point.
        When clicking within snap distance of the control point, the point snaps to cursor position.

        Args:
            event: QMouseEvent containing mouse press information
        """
        if event.button() == QtCore.Qt.LeftButton:
            distance_to_point = self.calculate_distance(QtCore.QPointF(event.pos()), self._original_point_pos)

            if distance_to_point <= self._snap_distance:
                self._point_pos = QtCore.QPointF(event.pos())
                self._ripple_pos = QtCore.QPointF(self._point_pos)
                self._is_snapped = True

                self._is_point_selected = True
                self._dragging = True

                self._show_ripple = True
                self._start_ripple_animation()

                self._overlay.update()
                event.accept()
                return

            point_rect = QtCore.QRectF(
                self._point_pos.x() - self._point_radius,
                self._point_pos.y() - self._point_radius,
                2 * self._point_radius,
                2 * self._point_radius,
            )

            if point_rect.contains(event.pos()):
                self._is_point_selected = True
                self._dragging = True
                event.accept()
                return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events

        When dragging the control point, updates its position and ensures it stays within
        the ring boundaries. Also updates ripple animation and emits position change signals.

        Args:
            event: QMouseEvent containing mouse move information
        """
        current_pos = event.pos()

        if self._dragging:
            center = QtCore.QPointF(self.width() / 2.0, self.height() / 2.0)
            vector = QtCore.QPointF(current_pos.x() - center.x(), current_pos.y() - center.y())

            distance = (vector.x() ** 2 + vector.y() ** 2) ** 0.5

            if distance > self._ring_radius:
                scale = self._ring_radius / distance
                new_x = center.x() + vector.x() * scale
                new_y = center.y() + vector.y() * scale
                self._point_pos = QtCore.QPointF(new_x, new_y)
            else:
                self._point_pos = QtCore.QPointF(current_pos)

            self._ripple_pos = QtCore.QPointF(self._point_pos)

            if self._continuous_ripple and not self._ripple_animation_active:
                self._start_ripple_animation()

            self._original_point_pos = QtCore.QPointF(self._point_pos)
            self._is_snapped = False

            self.sig_point_moved.emit(self._point_pos.x(), self._point_pos.y())
            self.sig_point_pos_changed.emit(self._point_pos)
            self.update()
            self._overlay.update()
            event.accept()
            return

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events

        When releasing the control point after dragging, updates weights based on the
        new position and updates the original position for future snap operations.

        Args:
            event: QMouseEvent containing mouse release information
        """
        if event.button() == QtCore.Qt.LeftButton and self._dragging:
            self._dragging = False
            self._is_point_selected = False

            self._original_point_pos = QtCore.QPointF(self._point_pos)

            self._update_weights()

            self._overlay.update()
            event.accept()
            return

        super().mouseReleaseEvent(event)

    def resizeEvent(self, event):
        """Handle resize events

        Updates overlay geometry and component distances when the widget is resized.

        Args:
            event: QResizeEvent containing resize information
        """
        super().resizeEvent(event)
        if self._overlay:
            self._overlay.setGeometry(self.rect())
            self.update_distances(force=True)

    def calculate_distance(self, p1, p2):
        """Compute Euclidean distance between two points

        Args:
            p1: First point coordinates (QPointF)
            p2: Second point coordinates (QPointF)

        Returns:
            float: Euclidean distance between the two points
        """
        dx = p1.x() - p2.x()
        dy = p1.y() - p2.y()
        return (dx**2 + dy**2) ** 0.5

    def update_distances(self, force=False):
        """Update distances between control point and components

        Calculates and stores the distances between the control point and each component.
        Updates weights based on these distances if there are enough components.

        Args:
            force (bool, optional): If True, update even if animations are pending. Defaults to False.
        """
        if self._pending_animation_count > 0 and not force:
            return

        self._component_distances.clear()
        if not self.components or len(self.components) < const.REQUIRED_DNA_COUNT:
            return

        for i, component in enumerate(self.components[: const.REQUIRED_DNA_COUNT]):
            geo = component.geometry()
            center = QtCore.QPointF(
                geo.x() + geo.width() / 2,
                geo.y() + geo.height() / 2,
            )
            distance = self.calculate_distance(self._point_pos, center)
            self._component_distances[i] = {
                "center": center,
                "distance": distance,
            }

        if len(self.components) >= const.REQUIRED_DNA_COUNT and not force:
            self._update_weights()

    def set_pivot_position(self, pos):
        """Set the position of the control point

        Ensures the position stays within widget boundaries and updates distances
        and overlay when the position changes.

        Args:
            pos: New position for the control point (QPointF or QPoint)
        """
        if not isinstance(pos, QtCore.QPointF):
            pos = QtCore.QPointF(pos.x(), pos.y())

        pos.setX(max(self._point_radius, min(self.width() - self._point_radius, pos.x())))
        pos.setY(max(self._point_radius, min(self.height() - self._point_radius, pos.y())))

        if self._point_pos != pos:
            self._point_pos = pos
            self.update_distances()
            self._update_weights()
            if self._overlay:
                self._overlay.update()
            self.sig_point_pos_changed.emit(self._point_pos)

    def _init_point_pos(self):
        """Initialize the control point position

        Sets the initial position of the control point to the center of the widget
        and stores this as the original position for snap operations.
        """
        self._point_pos = QtCore.QPointF(self.width() / 2.0, self.height() / 2.0)
        self._original_point_pos = QtCore.QPointF(self._point_pos)

    def update_components(self):
        """Update components

        Updates component positions and distances, then refreshes the overlay.
        """
        super().update_components()
        self.update_distances()
        if self._overlay:
            self._overlay.update()

    def update_components_weights(self, weights):
        """Update weights for all components

        Args:
            weights: List of weight values to apply to components
        """
        for i, component in enumerate(self.components[: const.REQUIRED_DNA_COUNT]):
            if hasattr(component, "update_weight"):
                component.update_weight(weights[i])

    def connect_components_signals(self, widget):
        """Connect signals from a component widget

        Connects weight label text change signal to handle weight updates.

        Args:
            widget: Component widget to connect signals from
        """
        super().connect_components_signals(widget)
        if hasattr(widget, "weight_label"):
            widget.weight_label.sig_text_changed.connect(lambda text: self._on_weight_text_changed(widget, text))

    def _on_weight_text_changed(self, widget, text):
        """Handle weight text changes from component weight labels

        Validates weight values, updates all component weights, and repositions
        the control point based on the new weights. Shows error toast if validation fails.

        Args:
            widget: The component widget whose weight changed
            text: New weight text value
        """
        try:
            new_value = float(text)
            if new_value < 0 or new_value > 1:
                raise ValueError("权重值必须在0到100之间")

            weights = {}
            for i, comp in enumerate(self.components[: const.REQUIRED_DNA_COUNT]):
                weights[i] = comp.weight_label.get_text()

            total_weight = sum(weights.values())
            if total_weight > 1:
                raise ValueError("权重之和超过100")

            weights[self.components.index(widget)] = new_value

            for i, comp in enumerate(self.components[: const.REQUIRED_DNA_COUNT]):
                comp.weight_label.set_text(weights[i])

            new_pos = UIPivotMove.calculate_position_from_weights(weights, self.get_component_positions())
            self.set_pivot_position(new_pos)

        except (ValueError, TypeError) as e:
            # Import third-party modules
            from dayu_widgets import MToast

            MToast.error(
                parent=self,
                text=str(e),
                duration=2000,
            )

    def _update_weights(self):
        """Update weights based on the current pivot position.

        Calculates weights inversely proportional to distances and updates component weights.
        Emits weights_changed signal with the new weight values.

        Note:
            When mirror mode is enabled, weight changes affect both primary and mirror areas.
            For 'all' area, updates are applied to each affected area individually.
        """
        if len(self._components) >= const.REQUIRED_DNA_COUNT:
            positions = self.get_component_positions()
            base_pos = positions[0]
            outer_positions = positions[1 : const.REQUIRED_DNA_COUNT]

            current_vector = np.array([self._point_pos.x() - base_pos.x(), self._point_pos.y() - base_pos.y()])
            current_distance = np.sqrt(
                (self._point_pos.x() - base_pos.x()) ** 2 + (self._point_pos.y() - base_pos.y()) ** 2,
            )

            outer_vectors = [np.array([p.x() - base_pos.x(), p.y() - base_pos.y()]) for p in outer_positions]
            max_distance = max(np.linalg.norm(v) for v in outer_vectors) if outer_vectors else 0
            distance_ratio = min(current_distance / max_distance, 1.0) if max_distance > 0 else 0.0

            current_angle_rad = np.arctan2(current_vector[1], current_vector[0]) if current_vector.any() else 0
            current_angle_deg = np.degrees(
                current_angle_rad if current_angle_rad >= 0 else current_angle_rad + 2 * np.pi,
            )

            weights = UIPivotMove._calculate_weights(current_angle_deg, distance_ratio)

            self.update_components_weights(weights)
            self.sig_weights_changed.emit({i: w for i, w in enumerate(weights)})

            self.update()

    def get_component_positions(self):
        positions = []
        for data in self._component_distances.values():
            positions.append(data["center"])
        return positions

    @property
    def point_pos(self):
        return self._point_pos

    @point_pos.setter
    def point_pos(self, pos):
        self.set_pivot_position(pos)

    def _start_ripple_animation(self):
        """Start the ripple animation effect for the control point

        Creates and configures animations for ripple radius and opacity.
        The ripple expands from the control point radius to three times its size
        while fading out over 800ms duration.
        """
        self._ripple_radius = 0
        self._ripple_opacity = 0.6
        self._ripple_animation_active = True

        if self._ripple_animation is not None:
            self._ripple_animation.stop()

        self._ripple_animation = QtCore.QPropertyAnimation(self, b"ripple_radius")
        self._ripple_animation.setDuration(800)
        self._ripple_animation.setStartValue(self._point_radius)
        self._ripple_animation.setEndValue(self._point_radius * 3)
        self._ripple_animation.setEasingCurve(QtCore.QEasingCurve.OutQuad)

        self._opacity_animation = QtCore.QPropertyAnimation(self, b"ripple_opacity")
        self._opacity_animation.setDuration(800)
        self._opacity_animation.setStartValue(0.6)
        self._opacity_animation.setEndValue(0.0)
        self._opacity_animation.setEasingCurve(QtCore.QEasingCurve.OutQuad)

        self._animation_group = QtCore.QParallelAnimationGroup()
        self._animation_group.addAnimation(self._ripple_animation)
        self._animation_group.addAnimation(self._opacity_animation)
        self._animation_group.finished.connect(self._on_ripple_animation_finished)

        self._animation_group.start()

    def _on_ripple_animation_finished(self):
        """Handle ripple animation finished event

        Resets animation active flag and restarts animation if continuous mode
        is enabled and the control point is being dragged.
        """
        self._ripple_animation_active = False

        if self._continuous_ripple and self._dragging:
            self._start_ripple_animation()

        self.update()

    def get_ripple_radius(self):
        """Get the current ripple radius

        Returns:
            float: Current ripple radius value
        """
        return self._ripple_radius

    def set_ripple_radius(self, radius):
        """Set the ripple radius and update display

        Args:
            radius (float): New ripple radius value
        """
        self._ripple_radius = radius
        self.update()
        self._overlay.update()

    ripple_radius = QtCore.Property(float, get_ripple_radius, set_ripple_radius)

    def get_ripple_opacity(self):
        """Get the current ripple opacity

        Returns:
            float: Current ripple opacity value (0.0-1.0)
        """
        return self._ripple_opacity

    def set_ripple_opacity(self, opacity):
        """Set the ripple opacity and update display

        Args:
            opacity (float): New ripple opacity value (0.0-1.0)
        """
        self._ripple_opacity = opacity
        self.update()
        self._overlay.update()

    ripple_opacity = QtCore.Property(float, get_ripple_opacity, set_ripple_opacity)


class PivotOverlayWidget(QtWidgets.QWidget):
    """Overlay widget for displaying the pivot point and related visual elements

    This widget is transparent for mouse events and draws the control point, ripple effects,
    and debug information if enabled.
    """

    sig_point_moved = QtCore.Signal(float, float)
    sig_point_pos_changed = QtCore.Signal(QtCore.QPointF)

    def __init__(self, parent=None):
        """Initialize the PivotOverlayWidget

        Args:
            parent (QWidget, optional): Parent widget. Defaults to None.
        """
        super().__init__(parent)
        self.setAttribute(QtCore.Qt.WA_NoSystemBackground)
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)
        self.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents)
        self._dragging = False
        self._is_point_selected = False

    def get_pivot_rect(self):
        """Get the rectangle that bounds the pivot point

        Returns:
            QRectF: Rectangle centered on the pivot point with dimensions based on point radius,
                   or None if parent is not a DragPointRingWidget
        """
        parent = self.parent()
        if not parent or not isinstance(parent, DragPointRingWidget):
            return None

        return QtCore.QRectF(
            parent._point_pos.x() - parent._point_radius,
            parent._point_pos.y() - parent._point_radius,
            2 * parent._point_radius,
            2 * parent._point_radius,
        )

    def paintEvent(self, event):
        """Paint the overlay elements

        Draws the control point, ripple effect, and debug information if enabled.
        The overlay is only painted if the parent is ready for overlay display.

        Args:
            event: QPaintEvent containing paint event information
        """
        super().paintEvent(event)
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)

        parent = self.parent()
        if not parent or not isinstance(parent, DragPointRingWidget):
            return
        if not parent.is_ready_for_overlay():
            return

        if const.DEBUG_MODE:
            painter.setPen(QtGui.QPen(parent._point_color, 1, QtCore.Qt.DashLine))

            positions = []
            for i in range(const.REQUIRED_DNA_COUNT):
                data = parent._component_distances.get(i)
                if data:
                    positions.append(data["center"])

            if len(positions) == const.REQUIRED_DNA_COUNT:
                weights = UIPivotMove.calculate_barycentric_coordinates(parent._point_pos, positions)

                calculated_pos = UIPivotMove.calculate_position_from_weights(weights, positions)

                painter.setPen(QtGui.QPen(QtGui.QColor(0, 255, 0), 1))
                path = QtGui.QPainterPath()
                path.moveTo(positions[0])
                path.lineTo(positions[1])
                path.lineTo(positions[2])
                path.lineTo(positions[0])
                painter.drawPath(path)

                painter.setPen(QtGui.QPen(parent._point_color, 1, QtCore.Qt.DashLine))
                for i, (pos, weight) in enumerate(zip(positions, weights)):
                    painter.drawLine(parent._point_pos, pos)

                    mid_x = (parent._point_pos.x() + pos.x()) / 2
                    mid_y = (parent._point_pos.y() + pos.y()) / 2
                    painter.drawText(QtCore.QPointF(mid_x, mid_y), f"W: {weight:.3f}")

                painter.setPen(QtGui.QPen(QtGui.QColor(0, 0, 255), 2))
                painter.drawEllipse(calculated_pos, 3, 3)

        current_color = parent._point_selected_color if parent._is_point_selected else parent._point_color
        painter.setBrush(current_color)
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawEllipse(parent._point_pos, parent._point_radius, parent._point_radius)

        if hasattr(parent, "_show_ripple") and parent._show_ripple:
            ripple_color = QtGui.QColor(parent._point_color)
            ripple_color.setAlpha(int(parent._ripple_opacity * 255))
            painter.setPen(QtCore.Qt.NoPen)
            painter.setBrush(ripple_color)
            painter.drawEllipse(parent._ripple_pos, parent._ripple_radius, parent._ripple_radius)
