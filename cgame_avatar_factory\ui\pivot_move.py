# Import built-in modules
import logging

# Import third-party modules
import numpy as np
from qtpy import QtCore

# Import local modules
import cgame_avatar_factory.constants as const


class UIPivotMove(QtCore.QObject):
    """Handles the pivot point movement and weight calculation using widget data."""

    sig_pivot_weight_changed = QtCore.Signal(list)

    def __init__(self, ring_widget=None, parent=None):
        """Initialize PivotMove with a ring widget.

        Args:
            ring_widget: DragPointRingWidget instance
            parent: Parent QObject
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._ring_widget = ring_widget
        self._weights = [0] * const.REQUIRED_DNA_COUNT

        if self._ring_widget:
            self._ring_widget.sig_point_pos_changed.connect(self._count_weight)
            self.sig_pivot_weight_changed.connect(self._ring_widget.update_components_weights)
            self.init_pivot()

    def init_pivot(self):
        """Initialize pivot position to the first component's position."""
        if not self._ring_widget:
            return

        positions = self._ring_widget.get_component_positions()
        if not positions:
            return

        self._ring_widget.blockSignals(True)
        self._ring_widget.set_pivot_position(positions[0])
        self._ring_widget.blockSignals(False)

        self._weights = [1] + [0] * (const.REQUIRED_DNA_COUNT - 1)
        self.sig_pivot_weight_changed.emit(self._weights)

    def _validate_weights_and_positions(self, weights, positions):
        """Validate the weights and positions data.

        Args:
            weights: Weight list
            positions: Position list

        Returns:
            tuple: (is_valid, normalized_weights)
            - is_valid: Whether the data is valid
            - normalized_weights: Normalized weights
        """
        if len(positions) < const.REQUIRED_DNA_COUNT or len(weights) != const.REQUIRED_DNA_COUNT:
            return False, None

        total = sum(weights)
        if total == 0:
            return False, None

        return True, [w / total for w in weights]

    def _find_weight_range(self, weights):
        """Determine the weight range based on the weights.

        Args:
            weights: Normalized weight list [base, p1, p2, p3]

        Returns:
            tuple: (start_angle, end_angle, ratio)
        """
        p1, p2, p3 = weights[1:]

        if p1 > 0 and p2 > 0:
            ratio = p2 / (p1 + p2)
            if ratio <= 0:
                return 270, 270, 0
            elif ratio >= 1:
                return 30, 30, 1
            else:
                return 270, 30, ratio
        elif p2 > 0 and p3 > 0:
            ratio = p3 / (p2 + p3)
            if ratio <= 0:
                return 30, 30, 0
            elif ratio >= 1:
                return 150, 150, 1
            else:
                return 30, 150, ratio
        elif p3 > 0 and p1 > 0:
            ratio = p1 / (p3 + p1)
            if ratio <= 0:
                return 150, 150, 0
            elif ratio >= 1:
                return 270, 270, 1
            else:
                return 150, 270, ratio
        else:
            if p1 > 0:
                return 270, 270, 1
            elif p2 > 0:
                return 30, 30, 1
            elif p3 > 0:
                return 150, 150, 1
            else:
                return 0, 0, 0

    def _calculate_target_angle(self, start_angle, end_angle, ratio):
        """Calculate the target angle.

        Args:
            start_angle: Start angle
            end_angle: End angle
            ratio: Ratio

        Returns:
            float: Target angle
        """
        if start_angle == end_angle:
            return start_angle

        if start_angle == 270 and end_angle == 30:
            if ratio <= 0:
                return start_angle
            elif ratio >= 1:
                return end_angle
            else:
                target = start_angle + ratio * 120
                if target >= 360:
                    target -= 360
                return target
        else:
            return start_angle + ratio * (end_angle - start_angle)

    def _calculate_target_position(self, angle_deg, distance_ratio, base_pos, outer_positions):
        """Calculate the target position.

        Args:
            angle_deg: Angle in degrees
            distance_ratio: Distance ratio
            base_pos: Base position
            outer_positions: Outer positions

        Returns:
            QPointF: Target position
        """
        max_distance = max(np.sqrt((p.x() - base_pos.x()) ** 2 + (p.y() - base_pos.y()) ** 2) for p in outer_positions)

        target_distance = distance_ratio * max_distance
        angle_rad = np.radians(angle_deg)

        target_x = base_pos.x() + target_distance * np.cos(angle_rad)
        target_y = base_pos.y() + target_distance * np.sin(angle_rad)

        return QtCore.QPointF(target_x, target_y)

    def set_pos_by_weight(self, weights):
        """Set the pivot position based on the weights.

        Args:
            weights: Weight list [w1, w2, w3, w4] where w1 is base character weight
        """
        if not self._ring_widget:
            return

        positions = self._ring_widget.get_component_positions()
        is_valid, normalized_weights = self._validate_weights_and_positions(weights, positions)
        if not is_valid:
            return

        distance_ratio = 1.0 - normalized_weights[0]
        start_angle, end_angle, ratio = self._find_weight_range(normalized_weights)
        target_angle = self._calculate_target_angle(start_angle, end_angle, ratio)

        target_pos = self._calculate_target_position(
            target_angle,
            distance_ratio,
            positions[0],
            positions[1 : const.REQUIRED_DNA_COUNT],
        )

        self._ring_widget.set_pivot_position(target_pos)

    def _calculate_distance_info(self, point_pos, base_pos, outer_positions):
        """Calculate the distance information.

        Args:
            point_pos: Point position
            base_pos: Base position
            outer_positions: Outer positions

        Returns:
            tuple: (current_vector, current_distance, max_distance, distance_ratio)
        """
        current_vector = np.array([point_pos.x() - base_pos.x(), point_pos.y() - base_pos.y()])
        current_distance = np.sqrt((point_pos.x() - base_pos.x()) ** 2 + (point_pos.y() - base_pos.y()) ** 2)

        outer_vectors = [np.array([p.x() - base_pos.x(), p.y() - base_pos.y()]) for p in outer_positions]
        max_distance = max(np.linalg.norm(v) for v in outer_vectors)
        distance_ratio = min(current_distance / max_distance, 1.0) if max_distance > 0 else 0.0

        return current_vector, current_distance, max_distance, distance_ratio

    def _calculate_angles(self, current_vector, base_pos, outer_positions):
        """Calculate the angles.

        Args:
            current_vector: Current vector
            base_pos: Base position
            outer_positions: Outer positions

        Returns:
            tuple: (current_angle_deg, outer_angles_deg)
        """
        outer_vectors = [np.array([p.x() - base_pos.x(), p.y() - base_pos.y()]) for p in outer_positions]
        outer_angles_rad = [np.arctan2(v[1], v[0]) for v in outer_vectors]
        outer_angles_deg = [np.degrees(a if a >= 0 else a + 2 * np.pi) for a in outer_angles_rad]

        current_angle_rad = np.arctan2(current_vector[1], current_vector[0])
        current_angle_deg = np.degrees(current_angle_rad if current_angle_rad >= 0 else current_angle_rad + 2 * np.pi)

        return current_angle_deg, outer_angles_deg

    @staticmethod
    def _calculate_weights(current_angle_deg, distance_ratio):
        """Calculate the weights.

        Args:
            current_angle_deg: Current angle in degrees
            distance_ratio: Distance ratio

        Returns:
            list: Weights
        """
        weights = [1.0 - distance_ratio] + [0.0] * (const.REQUIRED_DNA_COUNT - 1)

        if 150 <= current_angle_deg <= 270:
            ratio = (current_angle_deg - 150) / 120
            ratio = max(0, min(1, ratio))
            weights[3] = distance_ratio * (1 - ratio)
            weights[1] = distance_ratio * ratio
        elif 270 <= current_angle_deg <= 360 or 0 <= current_angle_deg <= 30:
            ratio = (current_angle_deg - 270) / 120 if current_angle_deg >= 270 else (current_angle_deg + 90) / 120
            ratio = max(0, min(1, ratio))
            weights[1] = distance_ratio * (1 - ratio)
            weights[2] = distance_ratio * ratio
        elif 30 <= current_angle_deg <= 150:
            ratio = (current_angle_deg - 30) / 120
            ratio = max(0, min(1, ratio))
            weights[2] = distance_ratio * (1 - ratio)
            weights[3] = distance_ratio * ratio

        return weights

    def _count_weight(self, point_pos):
        """Calculate the weights based on the pivot position.

        Args:
            point_pos: Pivot position
        """
        if not self._ring_widget:
            return

        positions = self._ring_widget.get_component_positions()
        if len(positions) < const.REQUIRED_DNA_COUNT:
            return

        base_pos = positions[0]
        outer_positions = positions[1 : const.REQUIRED_DNA_COUNT]

        current_vector, current_distance, max_distance, distance_ratio = self._calculate_distance_info(
            point_pos,
            base_pos,
            outer_positions,
        )

        current_angle_deg, _ = self._calculate_angles(
            current_vector,
            base_pos,
            outer_positions,
        )

        # 调用静态方法计算权重
        weights = UIPivotMove._calculate_weights(current_angle_deg, distance_ratio)
        self._weights = weights
        self.sig_pivot_weight_changed.emit(self._weights)

    @property
    def weights(self):
        """Get current weights."""
        return self._weights
