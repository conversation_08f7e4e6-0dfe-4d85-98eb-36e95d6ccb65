
"""
毛发工作室基本功能测试脚本
用于测试基本启动功能，避免触发递归问题
"""

import sys
import os
import logging

# 添加项目路径到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_mock_modules():
    """设置必要的Mock模块"""
    import types
    
    # Mock maya.app.general.mayaMixin
    maya_app = types.ModuleType('maya.app')
    maya_app.general = types.ModuleType('maya.app.general')
    maya_app.general.mayaMixin = types.ModuleType('maya.app.general.mayaMixin')
    
    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass
    
    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    
    sys.modules['maya.app'] = maya_app
    sys.modules['maya.app.general'] = maya_app.general
    sys.modules['maya.app.general.mayaMixin'] = maya_app.general.mayaMixin
    
    # Mock lightbox_ui.banner
    lightbox_ui = sys.modules.get('lightbox_ui')
    if lightbox_ui is None:
        lightbox_ui = types.ModuleType('lightbox_ui')
        sys.modules['lightbox_ui'] = lightbox_ui
    
    if not hasattr(lightbox_ui, 'banner'):
        lightbox_ui.banner = types.ModuleType('lightbox_ui.banner')
        lightbox_ui.banner.setup_banner = lambda *args, **kwargs: None
        sys.modules['lightbox_ui.banner'] = lightbox_ui.banner

def setup_environment():
    """设置测试环境"""
    os.environ['CGAME_AVATAR_FACTORY_RESOURCE'] = os.path.join(project_root, 'cgame_avatar_factory', 'resources')
    os.environ['THM_LOG_LEVEL'] = 'DEBUG'
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_basic_startup():
    """测试基本启动功能"""
    print("=" * 60)
    print("毛发工作室基本功能测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    setup_mock_modules()
    
    try:
        # 导入Qt相关模块
        from qtpy import QtWidgets, QtCore
        from dayu_widgets import dayu_theme
        print("✓ Qt模块导入成功")
        
        # 导入毛发工作室模块
        from cgame_avatar_factory.hair_studio import HairStudioTab
        print("✓ 毛发工作室模块导入成功")
        
        # 创建应用程序
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # 设置主题
        dayu_theme.set_theme("dark")
        dayu_theme.set_primary_color(dayu_theme.blue)
        
        # 创建主窗口
        main_window = QtWidgets.QMainWindow()
        main_window.setWindowTitle("毛发工作室 - 基本测试")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # 创建毛发工作室tab
        hair_studio_tab = HairStudioTab()
        print("✓ 毛发工作室tab创建成功")
        
        # 应用主题
        dayu_theme.apply(main_window)
        dayu_theme.apply(hair_studio_tab)
        
        # 设置中央widget
        main_window.setCentralWidget(hair_studio_tab)
        
        # 显示窗口
        main_window.show()
        
        print("✓ 毛发工作室UI启动成功！")
        print("✓ 窗口已显示")
        print("✓ 基本功能测试通过")
        
        # 简单测试tab数量
        tab_count = hair_studio_tab.count()
        print(f"✓ Tab数量: {tab_count}")
        
        # 测试获取当前tab（不切换）
        current_index = hair_studio_tab.currentIndex()
        current_widget = hair_studio_tab.currentWidget()
        print(f"✓ 当前tab索引: {current_index}")
        print(f"✓ 当前widget类型: {type(current_widget).__name__}")
        
        print("\n" + "=" * 60)
        print("基本测试完成！所有功能正常")
        print("窗口将保持打开状态...")
        print("按Ctrl+C退出测试")
        print("=" * 60)
        
        # 运行应用
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_startup()
    if success:
        print("\n✓ 基本功能测试成功")
    else:
        print("\n✗ 基本功能测试失败")
        sys.exit(1)
