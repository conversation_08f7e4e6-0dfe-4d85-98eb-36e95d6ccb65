# 毛发工作室开发环境使用指南

## 概述

本指南介绍如何在不启动Maya GUI的情况下，独立运行毛发工作室UI进行开发和测试。

## 环境要求

- lightbox开发环境（thm命令可用）
- Maya 2022或更高版本
- 项目依赖包（通过package.py自动管理）

## 快速启动

### 方法1：一键启动（推荐）

```bash
.\start_hair_dev_direct.cmd
```

这个脚本会：
1. 自动加载所有必需的依赖包
2. 使用mayapy（Maya的Python解释器）运行
3. 启动毛发工作室UI，无需Maya GUI
4. 提供完整的开发测试环境

### 方法2：使用tox环境

```bash
.\start_hair_dev_simple.cmd
```

这个脚本使用预配置的tox环境。

## 环境配置详情

### 新增的package.py配置

在`package.py`中新增了`hair_dev` action：

```python
"hair_dev": {
    "command": "mayapy",
    "requires": ["maya-2022", "python-3.7", "pydantic-2.5"],
    "env": {
        "PYTHONPATH": {"action": "prepend", "value": "{this.root};"},
        "CGAME_AVATAR_FACTORY_RESOURCE": {"action": "prepend", "value": "{this.root}/cgame_avatar_factory/resources"},
        "THM_LOG_LEVEL": {"action": "set", "value": "DEBUG"},
        "THM_MENU_CONFIG_PATH": {"action": "prepend", "value": "{this.root}/config/menu.yaml"},
    },
    "tox_options": {
        "passenv": "*",
        "description": "Hair Studio development environment using mayapy",
    },
},
```

### 核心脚本

- `debug_run_hair_studio_dev.py`: 主要的开发环境启动脚本
- `debug_start_hair_dev_direct.cmd`: 直接启动脚本（推荐）
- `debug_start_hair_dev_simple.cmd`: 使用tox的启动脚本

## 功能特性

✅ **完整的依赖管理**: 自动加载所有必需的包
✅ **无Maya GUI**: 只运行mayapy，不启动Maya界面
✅ **完整的Qt支持**: 包含qtpy和dayu_widgets
✅ **实时开发**: 支持代码修改后的实时测试
✅ **错误日志**: 详细的调试信息和错误追踪

## 开发工作流

1. **启动环境**:
   ```bash
   .\start_hair_dev_direct.cmd
   ```

2. **查看输出**: 
   - 环境检查信息
   - UI启动状态
   - 错误和警告信息

3. **进行开发**:
   - 修改毛发工作室相关代码
   - 重启环境查看效果

4. **退出环境**:
   - 按Ctrl+C或关闭窗口

## 当前状态

### ✅ 已实现
- 独立的毛发工作室UI启动
- 完整的依赖环境配置
- Maya环境集成（无GUI）
- dayu_widgets主题支持

### ✅ 已修复的问题
以下问题已经修复：

1. **AssetLibrary类**:
   - ✅ 添加了`update_assets`方法

2. **ComponentList类**:
   - ✅ 添加了`component_deleted`信号
   - ✅ 添加了`update_components`方法

### 🔧 下一步开发任务
1. 测试UI交互功能
2. 完善错误处理
3. 实现资产拖拽功能
4. 添加更多UI组件功能

## 故障排除

### 常见问题

1. **thm命令不可用**
   - 确保lightbox开发环境已正确安装
   - 检查PATH环境变量

2. **Maya环境问题**
   - 确保Maya 2022已安装
   - 检查Maya安装路径

3. **依赖包问题**
   - 运行`thm dev --ignore-standard-cmd tox -a`查看可用环境
   - 检查package.py中的依赖配置

### 调试模式

启动脚本会显示详细的调试信息，包括：
- 环境检查结果
- 模块导入状态
- UI启动过程
- 错误堆栈信息

## 总结

通过这个开发环境，你可以：
- 独立开发和测试毛发工作室UI
- 避免启动完整的Maya环境
- 快速迭代和调试代码
- 专注于UI功能开发

这个环境为毛发工作室模块提供了一个高效的开发测试平台。
