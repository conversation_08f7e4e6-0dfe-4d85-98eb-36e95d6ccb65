# Import built-in modules
import logging
import os
import shutil
import subprocess
import time

# Import third-party modules
import maya.OpenMaya as om
import maya.cmds as cmds
import maya.mel as mel
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.constants as const


def setup_goz_files():
    """Set up GoZ files and directories

    Creates necessary directories and copies required files for GoZ integration.
    If ZBrush config exists, sets up Maya GoZ config and finishes setup.
    """
    if not os.path.exists(const.PIXOLOGIC_PATH):
        os.makedirs(const.PIXOLOGIC_PATH)
    goz_projects_path = os.path.join(const.PIXOLOGIC_PATH, const.ZBRUSH_PORJECTS[0])
    if not os.path.exists(goz_projects_path):
        default_path = os.path.join(goz_projects_path, const.ZBRUSH_PORJECTS[1])
        os.makedirs(default_path)

    for folder in const.ZBRUSH_FOLDERS:
        target_path = os.path.join(const.PIXOLOGIC_PATH, folder)

        if os.path.exists(target_path):
            if get_zbrush_config():
                setup_maya_goz_config()
                _finish_send_to_goz()
                return
            else:
                shutil.rmtree(target_path)
                source_path = os.path.join(const.GOZRESOURES, folder)
                shutil.copytree(source_path, target_path)

        else:
            source_path = os.path.join(const.GOZRESOURES, folder)
            shutil.copytree(source_path, target_path)

    setup_zbrush()


def is_zbrush_running():
    """Check if ZBrush is currently running

    Returns:
        bool: True if ZBrush process is found, False otherwise
    """
    try:
        output = subprocess.check_output('tasklist /FI "IMAGENAME eq ZBrush.exe"', shell=True).decode("gbk")
        return "ZBrush.exe" in output
    except (subprocess.SubprocessError, UnicodeDecodeError):
        return False


def wait_for_zbrush():
    """Wait for ZBrush to start running

    Shows a progress dialog while waiting for ZBrush to launch.
    Dialog can be cancelled by user.
    """
    progress = QtWidgets.QProgressDialog("ZBrush disposition ing....", None, 0, 0)
    progress.setWindowTitle("ZBrush")
    progress.setWindowModality(QtCore.Qt.WindowModal)
    progress.setCancelButton(None)
    progress.setMinimumDuration(0)
    progress.setRange(0, 0)

    while True:
        if is_zbrush_running():
            zbrush_path = get_zbrush_process_path()
            if zbrush_path:
                update_goz_config(zbrush_path)
            progress.setValue(100)
            progress.close()
            show_send_dialog()
            return True
        time.sleep(0.1)
        QtCore.QCoreApplication.processEvents()
        progress.setValue(0)


def setup_zbrush():
    """Set up ZBrush environment

    Launches ZBrush if not running and waits for it to start.
    """
    bld_env_path = os.getenv(const.BLADE_THM_INSTALL_ENV)
    if not bld_env_path:
        logging.error(f"未找到{const.BLADE_THM_INSTALL_ENV}环境变量")
        return False

    try:
        subprocess.Popen(
            const.BLD_ZBRUSH_CMD,
            cwd=bld_env_path,
            shell=True,
        )
        if wait_for_zbrush():
            logging.info("ZBrush已成功启动")
            return True
        else:
            logging.warning("等待ZBrush启动超时")
            return False
    except Exception as e:
        logging.error(f"启动ZBrush失败: {str(e)}")
        return False


def create_blendshape():
    """Create blendshape for selected meshes

    Creates blendshape deformers for selected meshes and handles
    various mesh operations and cleanup.
    """
    all_goz = cmds.ls(const.ALL_GOZ_MESH_NAME, long=True)
    if not all_goz:
        raise ValueError(f"No mesh found matching {const.ALL_GOZ_MESH_NAME}")

    goz_mesh = [
        mesh
        for mesh in all_goz
        if cmds.nodeType(mesh) == "transform" and cmds.nodeType(cmds.listRelatives(mesh, shapes=True)[0]) == "mesh"
    ]

    logging.info(goz_mesh)

    if not goz_mesh:
        raise ValueError(f"No valid mesh found matching {const.ALL_GOZ_MESH_NAME}")

    if not cmds.objExists(const.BASE_HEAD_MESH_NAME):
        raise ValueError(f"Base head mesh {const.BASE_HEAD_MESH_NAME} not found")

    cmds.select(goz_mesh[0], replace=True)
    cmds.select(const.BASE_HEAD_MESH_NAME, add=True)

    selection = cmds.ls(sl=True, long=True)
    if len(selection) != 2:
        raise ValueError("Please select exactly two meshes: source mesh first, then target mesh")

    source_mesh = selection[0]
    target_mesh = selection[1]

    sel_list = om.MSelectionList()
    sel_list.add(source_mesh)
    dag_path = om.MDagPath()
    sel_list.getDagPath(0, dag_path)
    mesh_fn = om.MFnMesh(dag_path)

    space = om.MSpace.kWorld
    points = om.MPointArray()
    mesh_fn.getPoints(points, space)
    source_points = [(points[i].x, points[i].y, points[i].z) for i in range(points.length())]

    sel_list.clear()
    sel_list.add(target_mesh)
    sel_list.getDagPath(0, dag_path)
    mesh_fn = om.MFnMesh(dag_path)
    mesh_fn.getPoints(points, space)
    target_points = [(points[i].x, points[i].y, points[i].z) for i in range(points.length())]

    if len(source_points) != len(target_points):
        raise ValueError("Meshes must have the same number of vertices")

    blend_node_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
    existing_blendshape = cmds.ls(blend_node_name, type=const.GOZ_BLEND_SHAPE_SUFFIX)

    source_mesh = source_mesh.split("|")[-1]

    if existing_blendshape:
        blend_node = existing_blendshape[0]
        targets = cmds.listAttr(f"{blend_node}.w", multi=True) or []
        num_targets = len(targets)

        for target in targets:
            cmds.setAttr(f"{blend_node}.{target}", 0)

        if source_mesh in targets:
            backup_count = 0
            for target in targets:
                if target.startswith(f"{source_mesh}_bak_"):
                    backup_count += 1
            target_index = targets.index(source_mesh)
            bak_name = f"{source_mesh}_bak_{(backup_count + 1):02d}"
            cmds.aliasAttr(bak_name, f"{blend_node}.w[{target_index}]")

        cmds.blendShape(blend_node, edit=True, target=(target_mesh, num_targets, source_mesh, 1.0))
        cmds.setAttr(f"{blend_node}.{source_mesh}", 1)
    else:
        blend_node = cmds.blendShape(source_mesh, target_mesh, n=blend_node_name)[0]
        cmds.setAttr(f"{blend_node}.{source_mesh}", 1)

    cmds.delete(source_mesh)
    return blend_node


def send_to_goz():
    """Send selected objects to ZBrush via GoZ"""
    setup_goz_files()


def _finish_send_to_goz():
    """Complete GoZ send operation

    Internal function to finalize sending objects to ZBrush.
    """
    if not cmds.objExists(const.BASE_HEAD_MESH_NAME):
        raise ValueError(f"未找到{const.BASE_HEAD_MESH_NAME}模型")

    blend_node_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"
    existing_blendshape = cmds.ls(blend_node_name, type=const.GOZ_BLEND_SHAPE_SUFFIX)

    if existing_blendshape:
        num_targets = len(cmds.listAttr(f"{existing_blendshape[0]}.w", multi=True) or [])
        new_name = f"{const.GOZ_MESH_NAME}_{num_targets}"
    else:
        new_name = f"{const.GOZ_MESH_NAME}_0"

    new_mesh = cmds.duplicate(const.BASE_HEAD_MESH_NAME, name=new_name)[0]
    current_parent = cmds.listRelatives(new_mesh, parent=True)
    if current_parent:
        cmds.parent(new_mesh, world=True)
    cmds.sets(new_mesh, edit=True, forceElement="initialShadingGroup")

    for attr in const.ATTR_LIST:
        cmds.setAttr(f"{new_mesh}{attr}", lock=False)
        cmds.setAttr(f"{new_mesh}{attr}", channelBox=True)

    cmds.select(new_mesh, replace=True)

    if not cmds.pluginInfo("gozMaya", query=True, loaded=True):
        goz_plugin_path = get_goz_plugin_path()
        if not goz_plugin_path:
            raise ValueError("未找到GoZ插件，请确认Maya GoZ插件已正确安装")
        cmds.loadPlugin(goz_plugin_path)

    mel.eval(const.MEL_START)
    cmds.delete(new_mesh)
    return True


def get_goz_plugin_path():
    """Get Maya GoZ plugin path

    Returns:
        str: Path to Maya GoZ plugin
    """
    plugins_path = os.path.join(
        const.GOZRESOURES,
        const.ZBRUSH_FOLDERS[0],
        const.MAYA_APP_NAME,
        "Maya2022",
        "gozMaya.mll",
    )

    if os.path.exists(plugins_path):
        return plugins_path
    return None


def get_zbrush_config():
    """Get ZBrush configuration

    Returns:
        bool: True if valid ZBrush config exists
    """
    config_path = os.path.join(const.PIXOLOGIC_PATH, const.ZBRUSH_FOLDERS[1], const.GOZ_CONFIG_FILE)

    if not os.path.exists(config_path):
        logging.warning(f"配置文件不存在: {config_path}")
        return False
    try:
        with open(config_path, "r") as f:
            for line in f:
                if line.strip().startswith("PATH"):
                    zbrush_path = line.split("=")[1].strip().strip('"').strip("'").strip()
                    if os.path.exists(zbrush_path):
                        return True
                    logging.warning(f"ZBrush.exe不存在: {zbrush_path}")
                    return False
    except Exception as e:
        logging.error(f"读取配置文件出错: {str(e)}")
    return False


def get_zbrush_process_path():
    """Get path of running ZBrush process

    Returns:
        str: Path to ZBrush executable
    """
    """Get the path of running ZBrush.exe process using Windows Management Instrumentation Command-line (WMIC)"""
    try:
        cmd = "wmic process where \"name='ZBrush.exe'\" get ExecutablePath"
        output = subprocess.check_output(cmd, shell=True).decode("utf-8", errors="ignore")
        paths = [line.strip() for line in output.split("\n") if line.strip() and "ExecutablePath" not in line]
        return paths[0] if paths else None
    except (subprocess.SubprocessError, IndexError):
        return None


def update_goz_config(zbrush_path):
    """Update GoZ configuration

    Args:
        zbrush_path: Path to ZBrush executable
    """
    config_path = os.path.join(const.PIXOLOGIC_PATH, const.ZBRUSH_FOLDERS[1], const.GOZ_CONFIG_FILE)
    config_dir = os.path.dirname(config_path)

    if not os.path.exists(config_dir):
        os.makedirs(config_dir)

    try:
        existing_content = []
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                for line in f:
                    if not line.strip().startswith("PATH"):
                        existing_content.append(line.rstrip())

        with open(config_path, "w") as f:
            f.write(f'PATH="{zbrush_path}"\n')
            if existing_content:
                f.write("\n".join(existing_content))

        return True
    except Exception as e:
        logging.error(f"更新配置文件失败: {str(e)}")
        return False


def setup_maya_goz_config():
    """Set up Maya GoZ configuration

    Configures Maya environment for GoZ integration.
    """
    config_file = os.path.join(const.PIXOLOGIC_PATH, const.ZBRUSH_FOLDERS[1], const.GOZ_CONFIG_FILE)
    source_mel = os.path.join(
        const.PROJECT_AVATAR_FACTORY_PATH,
        "goz",
        const.ZBRUSH_FOLDERS[0],
        const.MAYA_APP_NAME,
        const.GOZ_MAYA_MEL,
    )
    target_dir = os.path.join(const.PIXOLOGIC_PATH, const.ZBRUSH_FOLDERS[0], const.MAYA_APP_NAME)
    target_mel = os.path.join(target_dir, const.GOZ_MAYA_MEL)

    has_maya_amend = False
    if os.path.exists(config_file):
        with open(config_file, "r") as f:
            content = f.read()
            has_maya_amend = "TO MAYA AMEND" in content

    os.makedirs(target_dir, exist_ok=True)

    shutil.copy2(source_mel, target_mel)

    if not has_maya_amend:
        original_content = ""
        if os.path.exists(config_file):
            with open(config_file, "r") as f:
                original_content = f.read().rstrip()

        with open(config_file, "w") as f:
            f.write(original_content)
            if original_content:
                f.write("\n")
            f.write("TO MAYA AMEND = COMPLETE")


def show_send_dialog():
    """Show GoZ send dialog

    Displays dialog for sending objects to ZBrush.
    """
    dialog = QtWidgets.QDialog()
    dialog.setWindowTitle("发送到ZBrush")
    dialog.setModal(True)

    layout = QtWidgets.QVBoxLayout()

    label = QtWidgets.QLabel("等待ZBrush完全启动后点击发送")
    layout.addWidget(label)

    send_button = QtWidgets.QPushButton("发送")
    layout.addWidget(send_button)

    dialog.setLayout(layout)

    def on_send():
        dialog.accept()
        _finish_send_to_goz()

    send_button.clicked.connect(on_send)
    return dialog.exec_()
