"""Mock Data Manager for Hair Studio.

This module provides mock data for development and testing purposes,
implementing realistic hair components and assets for demonstration.
"""

import uuid
from typing import List, Dict, Any, Optional

# Define constants locally to avoid circular imports
HAIR_TYPE_CARD = "card"
HAIR_TYPE_XGEN = "xgen"
HAIR_TYPE_CURVE = "curve"

DEFAULT_COMPONENT_WIDTH = 1.0
DEFAULT_COMPONENT_HEIGHT = 2.0
DEFAULT_XGEN_DENSITY = 1000
DEFAULT_XGEN_LENGTH = 5.0
DEFAULT_CURVE_THICKNESS = 0.1
DEFAULT_CURVE_SUBDIVISIONS = 8

PROPERTY_NAME = "name"
PROPERTY_VISIBLE = "visible"
PROPERTY_WIDTH = "width"
PROPERTY_HEIGHT = "height"
PROPERTY_DENSITY = "density"
PROPERTY_LENGTH = "length"
PROPERTY_THICKNESS = "thickness"
PROPERTY_SUBDIVISIONS = "subdivisions"

# Sample assets data
SAMPLE_ASSETS = [
    {
        "id": "card_1",
        "name": "Basic Hair Card",
        "asset_type": HAIR_TYPE_CARD,
        "thumbnail": "card_1_thumb.jpg",
        "file_path": "assets/cards/basic_hair_card.ma",
    },
    {
        "id": "card_2",
        "name": "Curly Hair Card",
        "asset_type": HAIR_TYPE_CARD,
        "thumbnail": "card_2_thumb.jpg",
        "file_path": "assets/cards/curly_hair_card.ma",
    },
    {
        "id": "xgen_1",
        "name": "Fine Hair XGen",
        "asset_type": HAIR_TYPE_XGEN,
        "thumbnail": "xgen_1_thumb.jpg",
        "file_path": "assets/xgen/fine_hair.xgen",
    },
    {
        "id": "xgen_2",
        "name": "Thick Hair XGen",
        "asset_type": HAIR_TYPE_XGEN,
        "thumbnail": "xgen_2_thumb.jpg",
        "file_path": "assets/xgen/thick_hair.xgen",
    },
    {
        "id": "curve_1",
        "name": "Guide Curves",
        "asset_type": HAIR_TYPE_CURVE,
        "thumbnail": "curve_1_thumb.jpg",
        "file_path": "assets/curves/guide_curves.ma",
    },
    {
        "id": "curve_2",
        "name": "Style Curves",
        "asset_type": HAIR_TYPE_CURVE,
        "thumbnail": "curve_2_thumb.jpg",
        "file_path": "assets/curves/style_curves.ma",
    },
]


class MockHairComponent:
    """Mock hair component for testing and demonstration."""

    def __init__(
        self, component_id: str, name: str, hair_type: str, asset_id: str = None
    ):
        """Initialize a mock hair component.

        Args:
            component_id (str): Unique identifier for the component
            name (str): Display name of the component
            hair_type (str): Type of hair (card, xgen, curve)
            asset_id (str, optional): ID of the source asset
        """
        self.id = component_id
        self.name = name
        self.type = hair_type
        self.asset_id = asset_id
        self.visible = True

        # Type-specific properties
        if hair_type == HAIR_TYPE_CARD:
            self.width = DEFAULT_COMPONENT_WIDTH
            self.height = DEFAULT_COMPONENT_HEIGHT
        elif hair_type == HAIR_TYPE_XGEN:
            self.density = DEFAULT_XGEN_DENSITY
            self.length = DEFAULT_XGEN_LENGTH
        elif hair_type == HAIR_TYPE_CURVE:
            self.thickness = DEFAULT_CURVE_THICKNESS
            self.subdivisions = DEFAULT_CURVE_SUBDIVISIONS

    def to_dict(self) -> Dict[str, Any]:
        """Convert component to dictionary representation.

        Returns:
            Dict[str, Any]: Component data as dictionary
        """
        data = {
            "id": self.id,
            PROPERTY_NAME: self.name,
            "type": self.type,
            "asset_id": self.asset_id,
            PROPERTY_VISIBLE: self.visible,
        }

        # Add type-specific properties
        if self.type == HAIR_TYPE_CARD:
            data[PROPERTY_WIDTH] = self.width
            data[PROPERTY_HEIGHT] = self.height
        elif self.type == HAIR_TYPE_XGEN:
            data[PROPERTY_DENSITY] = self.density
            data[PROPERTY_LENGTH] = self.length
        elif self.type == HAIR_TYPE_CURVE:
            data[PROPERTY_THICKNESS] = self.thickness
            data[PROPERTY_SUBDIVISIONS] = self.subdivisions

        return data


class MockDataManager:
    """Mock data manager for Hair Studio development and testing."""

    def __init__(self):
        """Initialize the mock data manager."""
        self._components: List[MockHairComponent] = []
        self._assets: List[Dict[str, Any]] = SAMPLE_ASSETS.copy()
        self._selected_component_id: Optional[str] = None

        # Create some initial mock components for demonstration
        self._create_initial_components()

    def _create_initial_components(self):
        """Create initial mock components for demonstration."""
        # Card components
        card_components = [
            ("Front Bangs", "card_1"),
            ("Side Hair Left", "card_2"),
            ("Side Hair Right", "card_1"),
            ("Back Hair", "card_2"),
        ]

        for name, asset_id in card_components:
            component = MockHairComponent(
                component_id=str(uuid.uuid4()),
                name=name,
                hair_type=HAIR_TYPE_CARD,
                asset_id=asset_id,
            )
            self._components.append(component)

        # XGen components
        xgen_components = [
            ("Scalp Hair Base", "xgen_1"),
            ("Hair Volume Layer", "xgen_2"),
            ("Fine Hair Details", "xgen_1"),
        ]

        for name, asset_id in xgen_components:
            component = MockHairComponent(
                component_id=str(uuid.uuid4()),
                name=name,
                hair_type=HAIR_TYPE_XGEN,
                asset_id=asset_id,
            )
            # Vary the properties for demonstration
            component.density = DEFAULT_XGEN_DENSITY + (len(self._components) * 200)
            component.length = DEFAULT_XGEN_LENGTH + (len(self._components) * 0.2)
            self._components.append(component)

        # Curve components
        curve_components = [
            ("Hair Guide Curves", "curve_1"),
            ("Styling Curves", "curve_2"),
            ("Detail Curves", "curve_1"),
        ]

        for name, asset_id in curve_components:
            component = MockHairComponent(
                component_id=str(uuid.uuid4()),
                name=name,
                hair_type=HAIR_TYPE_CURVE,
                asset_id=asset_id,
            )
            # Vary the properties for demonstration
            component.thickness = DEFAULT_CURVE_THICKNESS + (
                len(self._components) * 0.02
            )
            component.subdivisions = DEFAULT_CURVE_SUBDIVISIONS + (
                len(self._components) % 3
            )
            self._components.append(component)

    def get_components(self, hair_type: str = None) -> List[Dict[str, Any]]:
        """Get all components, optionally filtered by hair type.

        Args:
            hair_type (str, optional): Filter by hair type

        Returns:
            List[Dict[str, Any]]: List of component dictionaries
        """
        components = self._components
        if hair_type:
            components = [c for c in components if c.type == hair_type]

        return [c.to_dict() for c in components]

    def get_component(self, component_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific component by ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            Optional[Dict[str, Any]]: Component data or None if not found
        """
        for component in self._components:
            if component.id == component_id:
                return component.to_dict()
        return None

    def create_component(self, asset_id: str) -> Optional[Dict[str, Any]]:
        """Create a new component from an asset.

        Args:
            asset_id (str): ID of the asset to create component from

        Returns:
            Optional[Dict[str, Any]]: Created component data or None if failed
        """
        # Find the asset
        asset = None
        for a in self._assets:
            if a["id"] == asset_id:
                asset = a
                break

        if not asset:
            return None

        # Create new component
        component_id = str(uuid.uuid4())
        component_name = f"New {asset['name']}"

        component = MockHairComponent(
            component_id=component_id,
            name=component_name,
            hair_type=asset["asset_type"],
            asset_id=asset_id,
        )

        self._components.append(component)
        return component.to_dict()

    def update_component(self, component_id: str, **kwargs) -> bool:
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Properties to update

        Returns:
            bool: True if update successful, False otherwise
        """
        for component in self._components:
            if component.id == component_id:
                # Update basic properties
                if PROPERTY_NAME in kwargs:
                    component.name = kwargs[PROPERTY_NAME]
                if PROPERTY_VISIBLE in kwargs:
                    component.visible = kwargs[PROPERTY_VISIBLE]

                # Update type-specific properties
                if component.type == HAIR_TYPE_CARD:
                    if PROPERTY_WIDTH in kwargs:
                        component.width = kwargs[PROPERTY_WIDTH]
                    if PROPERTY_HEIGHT in kwargs:
                        component.height = kwargs[PROPERTY_HEIGHT]
                elif component.type == HAIR_TYPE_XGEN:
                    if PROPERTY_DENSITY in kwargs:
                        component.density = kwargs[PROPERTY_DENSITY]
                    if PROPERTY_LENGTH in kwargs:
                        component.length = kwargs[PROPERTY_LENGTH]
                elif component.type == HAIR_TYPE_CURVE:
                    if PROPERTY_THICKNESS in kwargs:
                        component.thickness = kwargs[PROPERTY_THICKNESS]
                    if PROPERTY_SUBDIVISIONS in kwargs:
                        component.subdivisions = kwargs[PROPERTY_SUBDIVISIONS]

                return True
        return False

    def delete_component(self, component_id: str) -> bool:
        """Delete a component.

        Args:
            component_id (str): ID of the component to delete

        Returns:
            bool: True if deletion successful, False otherwise
        """
        for i, component in enumerate(self._components):
            if component.id == component_id:
                del self._components[i]
                if self._selected_component_id == component_id:
                    self._selected_component_id = None
                return True
        return False

    def select_component(self, component_id: Optional[str]):
        """Select a component.

        Args:
            component_id (Optional[str]): ID of component to select, or None to clear selection
        """
        self._selected_component_id = component_id

    def get_selected_component_id(self) -> Optional[str]:
        """Get the currently selected component ID.

        Returns:
            Optional[str]: Selected component ID or None
        """
        return self._selected_component_id

    def get_assets(self, asset_type: str = None) -> List[Dict[str, Any]]:
        """Get all assets, optionally filtered by type.

        Args:
            asset_type (str, optional): Filter by asset type

        Returns:
            List[Dict[str, Any]]: List of asset dictionaries
        """
        assets = self._assets
        if asset_type:
            assets = [a for a in assets if a["asset_type"] == asset_type]

        return assets.copy()
