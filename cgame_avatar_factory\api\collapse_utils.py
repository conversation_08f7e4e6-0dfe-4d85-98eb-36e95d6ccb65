# Import built-in modules

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.api import dna_utils as dna
from cgame_avatar_factory.merge import mesh_util as mesh


def collapse_model():
    """Clean up the scene and prepare for model collapse

    Perform cleanup operations required for model collapse, including deleting mesh history and unnecessary nodes
    """
    delete_mesh_history()


def delete_nodes():
    nodes_to_delete = [
        const.ROOT_PINCH_JOINT,
        const.FACE_CTRL_BASIC_GRP,
        const.FACE_CTRL_DETAILED_GRP,
        const.PINCE_CTRL_NAME,
        const.MIX_CTRL_NAME,
        const.FACE_CTRL_NAME,
    ]

    for node in nodes_to_delete:
        mesh.delete_node(node)


def delete_mesh_history():
    """Delete history for all objects in the scene while preserving current shape

    This function first duplicates the current mesh shapes to preserve their current state,
    then deletes the original meshes and renames the duplicates to match the original names.
    This approach ensures that the current visual state is maintained while removing all history.
    """
    mesh_names = dna.TemplateDna().get_mesh_name()
    for mesh_name in mesh_names:
        if cmds.objExists(mesh_name):
            cmds.delete(mesh_name, constructionHistory=True)
