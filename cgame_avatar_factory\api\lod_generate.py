# Import built-in modules
import logging
import os

# Import third-party modules
import maya.cmds as cmds
import numpy as np
from scipy.interpolate import Rbf
from scipy.spatial import cKDTree

# Import local modules
from cgame_avatar_factory import utils
import cgame_avatar_factory.constants as const

METHOD_AUTO = "Auto"
METHOD_MANUAL = "Manual"


class LODGeneration:
    def __init__(self):
        pass

    def transfer_attributes(self, source_mesh, target_mesh):
        """
        Transfer attributes from source mesh to target mesh based on UVs.

        Args:
            source_mesh (str): The name of the source mesh.
            target_mesh (str): The name of the target mesh.
        """
        if not cmds.objExists(source_mesh):
            logging.warning(f"Source mesh '{source_mesh}' does not exist.")
            return
        if not cmds.objExists(target_mesh):
            logging.warning(f"Target mesh '{target_mesh}' does not exist.")
            return
        source_uv_set = self.get_uv_set(source_mesh)
        target_uv_set = self.get_uv_set(target_mesh)
        # 执行属性转移
        cmds.transferAttributes(
            source_mesh,
            target_mesh,
            transferPositions=1,  # 转移顶点位置
            transferNormals=1,  # 转移法线
            transferUVs=0,  # 转移UV
            sampleSpace=3,
            sourceUvSpace=source_uv_set if source_uv_set else "",  # 源UV集
            targetUvSpace=target_uv_set if target_uv_set else "",  # 目标UV集
            searchMethod=3,  # 使用最近邻搜索方法
        )
        logging.info(f"Attributes transferred from '{source_mesh}' to '{target_mesh}'.")

    @staticmethod
    def get_lod_mesh_names(base_mesh_name):
        """
        Generate LOD meshes for the given base mesh.

        Args:
            base_mesh_name (str): The base name of the base mesh.

        Returns:
            list: A list of LOD mesh names.
        """
        lod_mesh_names = []
        for lod_level in range(1, 8):  # LOD levels from 1 to 7
            lod_mesh_name = f"{base_mesh_name}_lod{lod_level}_mesh"
            if cmds.objExists(lod_mesh_name):
                lod_mesh_names.append(lod_mesh_name)

        return lod_mesh_names

    @staticmethod
    def get_uv_set(mesh_name):
        if not cmds.objExists(mesh_name):
            logging.warning(f"Mesh '{mesh_name}' does not exist.")
            return ""

        # 获取 UV 集名称
        uv_sets = cmds.polyUVSet(mesh_name, query=True, allUVSets=True)
        return uv_sets[0] if uv_sets else ""

    @staticmethod
    def extract_lod_number(s: str):
        # 统一转为小写以忽略大小写差异
        s_lower = s.lower()
        lod_pos = s_lower.find("lod")

        if lod_pos == -1:
            return None  # 未找到 lod 标记

        # 定位数字起始位置（跳过 'lod' 的3个字符）
        num_start = lod_pos + 3
        if num_start >= len(s):
            return None  # lod 后无内容

        # 提取连续数字字符
        num_str = ""
        for char in s[num_start:]:  # 注意使用原字符串保留大小写
            if char.isdigit():
                num_str += char
            else:
                break  # 遇到非数字停止

        return int(num_str) if num_str else None

    def generate_lod(self):
        lod_generate_config_json = os.path.join(const.CONFIG_PATH, const.LOD_GENERATE_JSON_PATH)
        if not os.path.exists(lod_generate_config_json):
            lod_generate_config_json = os.path.join(const.RESOURCE_ROOT, const.LOD_GENERATE_JSON_PATH)
        lod_generate_config = utils.read_json(lod_generate_config_json)
        lod_levels = lod_generate_config["lod_levels"]
        base_parts = lod_generate_config["base_parts"]
        special_parts = lod_generate_config["special_parts"]
        select_parts = lod_generate_config["select_parts"]
        select_method = lod_generate_config["select_method"]
        wrap_suffix = lod_generate_config["wrap_suffix"]
        lod_vertex_index = lod_generate_config["lod_vertex_index"]

        for lod_level in lod_levels:
            for part in select_parts:
                LODGeneration.create_wrap_mesh(part, lod_level, wrap_suffix)

        for base_name in base_parts:
            lod_mesh_names = self.get_lod_mesh_names(base_name)
            source_mesh_name = f"{base_name}_lod0_mesh"
            for target_mesh_name in lod_mesh_names:
                self.transfer_attributes(source_mesh_name, target_mesh_name)
                cmds.delete(target_mesh_name, constructionHistory=True)

        lod_vertex_index = self.standard_vertex_index(lod_vertex_index, select_parts, select_method, lod_levels)
        for base_name in special_parts:
            lod_mesh_names = self.get_lod_mesh_names(base_name)
            for target_mesh_name in lod_mesh_names:
                lod_idx = self.extract_lod_number(target_mesh_name)
                related_geos = [f"{base_name}_lod{lod_idx}_mesh" for base_name in select_parts]
                before_meshes = [geo + wrap_suffix for geo in related_geos]
                after_meshes = related_geos
                deformer = RBFDeformer(source_mesh=target_mesh_name)
                vertex_index = lod_vertex_index[lod_idx - 1]
                deformer.get_select_points(
                    before_meshes=before_meshes,
                    after_meshes=after_meshes,
                    select_method=select_method,
                    vertex_index=vertex_index,
                )
                deformer.train()
                deformer.apply(target_mesh_name)

        delete_list = []
        for lod_level in lod_levels:
            for part in select_parts:
                wrap_name = f"{part}_lod{lod_level}_mesh{wrap_suffix}"
                delete_list.append(wrap_name)
        LODGeneration.delete_objects(delete_list)

    @staticmethod
    def create_wrap_mesh(base_name, lod_level, wrap_suffix):
        original_name = f"{base_name}_lod{lod_level}_mesh"
        wrap_name = original_name + wrap_suffix
        if not cmds.objExists(wrap_name):
            cmds.duplicate(original_name, name=wrap_name)
        return wrap_name

    @staticmethod
    def delete_objects(obj_list):
        existing_objs = [obj for obj in obj_list if cmds.objExists(obj)]

        if existing_objs:
            cmds.delete(existing_objs)

    @staticmethod
    def standard_vertex_index(lod_vertex_index, select_parts, select_method, lod_levels):
        """
        根据当前的 LOD 和 wrap_parts、wrap_method 生成 vertex_index。
        :param lod_vertex_index: 包含顶点索引的字典
        :param select_parts: 部件名称的列表
        :param select_method: 对应的处理方法列表
        :return: 生成的 vertex_index 列表
        """
        result_lod_vertex_index = []
        for lod_level in lod_levels:
            vertex_index = []
            # 遍历 wrap_parts 和 wrap_method
            for part, method in zip(select_parts, select_method):
                if method == METHOD_MANUAL and (part not in lod_vertex_index):
                    raise ValueError(f"部件 '{part}' 不在 lod_vertex_index 中。")
                if method == METHOD_AUTO:
                    vertex_index.append([])  # 自动方法，置空
                elif method == METHOD_MANUAL:
                    if len(lod_vertex_index[part]) < lod_level:
                        raise ValueError(f"部件 '{part}' 的层级数量不足，LOD 级别 {lod_level} 不存在。")
                    lod_indices = lod_vertex_index[part][lod_level - 1]  # lod 从 1 开始，所以需要减去 1
                    vertex_index.append(lod_indices)  # 手动方法，添加顶点索引
                else:
                    raise ValueError(f"未知的 wrap_method '{method}'，应为 {METHOD_AUTO} 或 {METHOD_MANUAL}。")
            result_lod_vertex_index.append(vertex_index)
        return result_lod_vertex_index


class RBFDeformer:
    def __init__(
        self,
        source_mesh,
        function="linear",
    ):
        """
        RBF deformer constructor (topologically consistent version).

        Args:
            source_mesh (str): The name of the source mesh to be deformed.
            function (str): The type of RBF basis function ('gaussian', 'thin_plate', 'multiquadric', etc.).
        """
        # 输入验证
        if not cmds.objExists(source_mesh):
            raise ValueError(f"源网格 {source_mesh} 不存在")
        # 初始化参数
        self.source_mesh = source_mesh
        self.before_meshes = None
        self.after_meshes = None
        self.function = function
        # 获取顶点数据
        self.source_points = self._get_vertex_positions(source_mesh)
        self.before_points_list = []
        self.after_points_list = []
        # 初始化 RBF 模型
        self.rbf_x = None
        self.rbf_y = None
        self.rbf_z = None
        self.all_before_pos = []
        self.all_after_pos = []
        self.displacements = None

    def get_select_points(self, before_meshes, after_meshes, select_method, vertex_index):
        """
        Get vertices before and after deformation.

        Args:
            before_meshes (list): A list of base meshes before deformation (must be topologically consistent with after_meshes).
            after_meshes (list): A list of target meshes after deformation (must be topologically consistent with before_meshes).
        """
        if len(before_meshes) != len(after_meshes):
            raise ValueError("before_meshes 和 after_meshes 数量必须相同")
        for before, after in zip(before_meshes, after_meshes):
            if not cmds.objExists(before) or not cmds.objExists(after):
                raise ValueError(f"存在无效的 before/after 网格 {before} {after}")
            if len(cmds.ls(f"{before}.vtx[*]", fl=True)) != len(cmds.ls(f"{after}.vtx[*]", fl=True)):
                raise ValueError(f"{before} 和 {after} 顶点数不一致")
        self.before_meshes = before_meshes
        self.after_meshes = after_meshes
        for before_mesh, after_mesh, method, index in zip(before_meshes, after_meshes, select_method, vertex_index):
            if method == METHOD_AUTO:
                self.before_points_list.append(self._get_vertex_positions(before_mesh))
                self.after_points_list.append(self._get_vertex_positions(after_mesh))
            elif method == METHOD_MANUAL:
                before_pos = self._get_vertex_positions_with_index(before_mesh, index)
                after_pos = self._get_vertex_positions_with_index(after_mesh, index)
                self.all_before_pos.extend(before_pos)
                self.all_after_pos.extend(after_pos)
            else:
                raise ValueError(f"未知的 select_method '{method}'，应为 {METHOD_AUTO} 或 {METHOD_MANUAL}。")
        # 整合数据，计算displacements
        nearest_before_points, nearest_after_points = self.get_nearest_points()
        self.all_before_pos.extend(nearest_before_points)
        self.all_after_pos.extend(nearest_after_points)
        self.all_before_pos = np.array(self.all_before_pos)
        self.all_after_pos = np.array(self.all_after_pos)
        self.displacements = self.all_after_pos - self.all_before_pos

    def _get_vertex_positions(self, mesh):
        vtx = cmds.ls(f"{mesh}.vtx[*]", fl=True)
        return np.array([cmds.pointPosition(v) for v in vtx])

    def _get_vertex_positions_with_index(self, mesh, vertex_indices):
        positions = []
        for vertex_index in vertex_indices:
            vertex_name = f"{mesh}.vtx[{vertex_index}]"
            if not cmds.objExists(vertex_name):
                raise ValueError(f"顶点索引 '{vertex_index}' 在网格 '{mesh}' 中无效。")
            # 获取顶点坐标
            position = cmds.xform(vertex_name, query=True, translation=True, worldSpace=True)
            positions.append(position)
        return np.array(positions)

    def get_nearest_points(self):
        all_before_pos = []  # 用于存储所有的 before_pos 点
        all_after_pos = []  # 用于存储所有的 after_pos 点

        # 遍历每个 before-after 对
        for before_points, after_points in zip(self.before_points_list, self.after_points_list):
            # 在 before 网格中查找源顶点的最近邻
            tree = cKDTree(before_points)
            _, indices = tree.query(self.source_points, k=1)
            # 计算位移：after位置 - before位置
            before_pos = before_points[indices]
            after_pos = after_points[indices]  # 利用拓扑一致性直接索引
            all_before_pos.append(before_pos)
            all_after_pos.append(after_pos)

        all_before_pos = np.concatenate(all_before_pos, axis=0)
        all_after_pos = np.concatenate(all_after_pos, axis=0)
        all_before_pos, unique_indices = np.unique(all_before_pos, axis=0, return_index=True)
        unique_after_pos = all_after_pos[unique_indices]
        return all_before_pos, unique_after_pos

    def train(self):
        x, y, z = self.all_before_pos.T
        dx, dy, dz = self.displacements.T

        # 为每个坐标分量创建 RBF 插值器
        common_kwargs = {
            "function": self.function,
        }
        self.rbf_x = Rbf(x, y, z, dx, **common_kwargs)
        self.rbf_y = Rbf(x, y, z, dy, **common_kwargs)
        self.rbf_z = Rbf(x, y, z, dz, **common_kwargs)

    def apply(self, mesh):
        """
        Apply deformation to the target mesh.

        Args:
            mesh (str): The name of the mesh to be deformed.
        """
        if not cmds.objExists(mesh):
            raise ValueError(f"网格 {mesh} 不存在")
        # 获取顶点位置
        points = self._get_vertex_positions(mesh)
        x, y, z = points.T
        # 预测位移
        dx = self.rbf_x(x, y, z)
        dy = self.rbf_y(x, y, z)
        dz = self.rbf_z(x, y, z)
        # 应用变形
        vtx_list = cmds.ls(f"{mesh}.vtx[*]", fl=True)
        for i, vtx in enumerate(vtx_list):
            cmds.xform(vtx, t=[x[i] + dx[i], y[i] + dy[i], z[i] + dz[i]], ws=True)
