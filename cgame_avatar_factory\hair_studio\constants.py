"""Hair Studio Constants Module.

This module defines all constants used throughout the Hair Studio tool,
including UI text, magic numbers, and configuration values.
"""

# Hair type constants
HAIR_TYPE_CARD = "card"
HAIR_TYPE_XGEN = "xgen"
HAIR_TYPE_CURVE = "curve"

# UI text constants
UI_TEXT_HAIR_COMPONENT_LIST = "Hair Component List"
UI_TEXT_NO_COMPONENT_SELECTED = "No Component Selected"
UI_TEXT_CARD_TAB = "Card"
UI_TEXT_XGEN_TAB = "XGen"
UI_TEXT_CURVE_TAB = "Curve"
UI_TEXT_HAIR_ASSET_LIBRARY = "Hair Asset Library"
UI_TEXT_SEARCH_PLACEHOLDER = "Search assets..."
UI_TEXT_SETTINGS = "Settings"
UI_TEXT_ADD_COMPONENT = "Add Component"
UI_TEXT_REMOVE_COMPONENT = "Remove Component"

# Error messages
ERROR_MSG_FAILED_TO_SETUP_UI = "Failed to set up UI"
ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO = "Failed to initialize Hair Studio UI"
ERROR_MSG_ERROR_CHANGING_TABS = "Error changing tabs"
ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST = "Error updating component list"
ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION = "Error handling component selection"
ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT = "Error setting selected component"
ERROR_MSG_ERROR_CREATING_COMPONENT = "Error creating component"
ERROR_MSG_ERROR_SELECTING_COMPONENT = "Error selecting component"
ERROR_MSG_ERROR_DELETING_COMPONENT = "Error deleting component"
ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY = "Error refreshing asset library"
ERROR_MSG_FAILED_TO_CONNECT_SIGNALS = "Failed to connect signals"
ERROR_MSG_FAILED_TO_CREATE_COMPONENT = "Failed to create component"

# Layout constants
LAYOUT_MARGIN_ZERO = 0
LAYOUT_SPACING_SMALL = 2
LAYOUT_SPACING_MEDIUM = 5
LAYOUT_SPACING_LARGE = 10

# Widget stretch factors
STRETCH_FACTOR_EDITOR_AREA = 4  # 40% width
STRETCH_FACTOR_COMPONENT_LIST = 3  # 30% width
STRETCH_FACTOR_ASSET_LIBRARY = 3  # 30% width

# Default values
DEFAULT_TAB_INDEX = 0
DEFAULT_COMPONENT_WIDTH = 1.0
DEFAULT_COMPONENT_HEIGHT = 1.0
DEFAULT_XGEN_DENSITY = 1000
DEFAULT_XGEN_LENGTH = 1.0
DEFAULT_CURVE_THICKNESS = 0.1
DEFAULT_CURVE_SUBDIVISIONS = 3

# Range limits
MIN_COMPONENT_SIZE = 0.01
MAX_COMPONENT_SIZE = 100.0
MIN_XGEN_DENSITY = 1
MAX_XGEN_DENSITY = 10000
MIN_CURVE_THICKNESS = 0.001
MAX_CURVE_THICKNESS = 10.0
MIN_CURVE_SUBDIVISIONS = 1
MAX_CURVE_SUBDIVISIONS = 10

# Safety limits
MAX_CLEAR_ITERATIONS = 100
MAX_RECURSION_DEPTH = 10

# Object names
OBJECT_NAME_HAIR_STUDIO_TAB = "HairStudioTab"
OBJECT_NAME_COMPONENT_LIST_FORMAT = "{}_component_list"
OBJECT_NAME_ASSET_LIBRARY_FORMAT = "{}_asset_library"

# Icon names
ICON_ADD_LINE = "add_line.svg"
ICON_TRASH_LINE = "trash_line.svg"
ICON_SETTINGS_LINE = "settings_line.svg"
ICON_SEARCH_LINE = "search_line.svg"

# Sample data for testing
SAMPLE_ASSETS = [
    {
        "id": "card_1",
        "name": "Hair Card 1",
        "asset_type": HAIR_TYPE_CARD,
        "thumbnail": None,
        "metadata": {"category": "front_bangs"},
    },
    {
        "id": "card_2", 
        "name": "Hair Card 2",
        "asset_type": HAIR_TYPE_CARD,
        "thumbnail": None,
        "metadata": {"category": "side_hair"},
    },
    {
        "id": "xgen_1",
        "name": "XGen Hair 1",
        "asset_type": HAIR_TYPE_XGEN,
        "thumbnail": None,
        "metadata": {"strand_count": DEFAULT_XGEN_DENSITY},
    },
    {
        "id": "xgen_2",
        "name": "XGen Hair 2", 
        "asset_type": HAIR_TYPE_XGEN,
        "thumbnail": None,
        "metadata": {"strand_count": 1500},
    },
    {
        "id": "curve_1",
        "name": "Hair Curve 1",
        "asset_type": HAIR_TYPE_CURVE,
        "thumbnail": None,
        "metadata": {"curve_type": "bezier"},
    },
    {
        "id": "curve_2",
        "name": "Hair Curve 2",
        "asset_type": HAIR_TYPE_CURVE,
        "thumbnail": None,
        "metadata": {"curve_type": "nurbs"},
    },
]

# Property names
PROPERTY_NAME = "name"
PROPERTY_VISIBLE = "visible"
PROPERTY_WIDTH = "width"
PROPERTY_HEIGHT = "height"
PROPERTY_DENSITY = "density"
PROPERTY_LENGTH = "length"
PROPERTY_THICKNESS = "thickness"
PROPERTY_SUBDIVISIONS = "subdivisions"

# Form labels
FORM_LABEL_NAME = "Name:"
FORM_LABEL_VISIBLE = "Visible:"
FORM_LABEL_WIDTH = "Width:"
FORM_LABEL_HEIGHT = "Height:"
FORM_LABEL_DENSITY = "Density:"
FORM_LABEL_LENGTH = "Length:"
FORM_LABEL_THICKNESS = "Thickness:"
FORM_LABEL_SUBDIVISIONS = "Subdivisions:"
