"""
Makeup Details Widget Module

This widget displays and allows editing of detailed makeup settings.
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.components.layout import FramelessVLayout


class MakeupDetailsWidget(QtWidgets.QFrame):
    """
    Widget for displaying and editing detailed makeup settings
    """

    def __init__(self, parent=None):
        super(MakeupDetailsWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """Initialize UI components"""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.setLayout(self.main_layout)

        # Add a placeholder label
        placeholder = dayu_widgets.MLabel("Makeup Details - Coming Soon")
        placeholder.setAlignment(QtCore.Qt.AlignCenter)
        self.main_layout.addWidget(placeholder)
