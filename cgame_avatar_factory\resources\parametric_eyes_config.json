{"documentation": {"eyeball_left_anchors": "Anchor points for aligning the eye driver with the eyeball. Vertex indices in order: top, front, bottom, back, right, left", "eyeball_right_anchors": "Anchor points for aligning the eye driver with the eyeball. Vertex indices in order: top, front, bottom, back, right, left", "cornea_left_coordinate_anchors": "Anchor points for aligning the cornea driver with the cornea region. Vertex indices in order: top, bottom, left, right, front", "cornea_right_coordinate_anchors": "Anchor points for aligning the cornea driver with the cornea region. Vertex indices in order: top, bottom, left, right, front", "left_eyelid_align_anchors": "Anchor points for auto matching the eyeball with the eyelid. Vertex indices in order: top, bottom, left, right", "right_eyelid_align_anchors": "Anchor points for auto matching the eyeball with the eyelid. Vertex indices in order: top, bottom, left, right", "left_eyeball_align_anchors": "Anchor points for auto matching the eyeball with the eyelid. Vertex indices in order: top, bottom, left, right", "right_eyeball_align_anchors": "Anchor points for auto matching the eyeball with the eyelid. Vertex indices in order: top, bottom, left, right"}, "eye_anchor_info": {"eyeball_left_anchors": [192, 0, 176, 755, 168, 184], "eyeball_right_anchors": [192, 0, 176, 755, 168, 184], "cornea_left_coordinate_anchors": [402, 386, 378, 394, 0], "cornea_right_coordinate_anchors": [402, 386, 378, 394, 0], "left_eyelid_align_anchors": [7704, 7694, 7854, 7577], "right_eyelid_align_anchors": [13682, 901, 13561, 1005], "left_eyeball_align_anchors": [30, 387, 42, 88], "right_eyeball_align_anchors": [434, 384, 72, 487]}, "eye_vertices": {"cornea_left_verts": [".vtx[0:32]", ".vtx[243:338]", ".vtx[371:434]"], "cornea_right_verts": [".vtx[0:32]", ".vtx[243:338]", ".vtx[371:434]"]}}