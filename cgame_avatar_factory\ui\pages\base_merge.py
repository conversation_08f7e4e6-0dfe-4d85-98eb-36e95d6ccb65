# Import built-in modules
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import MTabWidget
from maya import cmds
from qtpy import QtCore
from qtpy import QtWidgets
from qtpy.QtWidgets import QComboBox

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory import core
from cgame_avatar_factory.api import collapse_utils as collapse
from cgame_avatar_factory.api import dna_utils as dna
from cgame_avatar_factory.api import export_utils
from cgame_avatar_factory.api import goz_utils
from cgame_avatar_factory.api.get_view_image_utils import get_viewport_screenshot
from cgame_avatar_factory.api.lod_generate import LODGeneration
from cgame_avatar_factory.build_scenes import setup_riglogic as riglogic
import cgame_avatar_factory.build_scenes.face_customization as customization
from cgame_avatar_factory.merge import mesh_util
from cgame_avatar_factory.merge import mesh_util as mesh
import cgame_avatar_factory.merge.scene_data_call as scene_call
from cgame_avatar_factory.ui.animation_theater import animation_theater_page
from cgame_avatar_factory.ui.animation_theater.face_anim_preview import reset_face_anim_attr
from cgame_avatar_factory.ui.components.content_panel import ContentPanel
from cgame_avatar_factory.ui.components.floating_panel import FloatingPanel
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.components.layout import FramelessVLayout
from cgame_avatar_factory.ui.components.overlay_widgets import create_overlay
from cgame_avatar_factory.ui.components.status_bar import ProgressWidget
from cgame_avatar_factory.ui.dna_lib.dna_export_widgets import CharacterExportDialog
from cgame_avatar_factory.ui.dna_lib.dna_lib_widgets import DNALibWidget
from cgame_avatar_factory.ui.dna_merge_ring.drag_point_ring import DragPointRingWidget
from cgame_avatar_factory.ui.face_sculpting_center.parametric_parts.parametric_parts_widget import ParametricPartsWidget
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.ui.pages.face_area_layout import FaceAreaWidget
from cgame_avatar_factory.ui.pivot_move import UIPivotMove


class BaseMergePage(QtWidgets.QFrame):
    # Add signals for state changes
    sig_weights_changed = QtCore.Signal(list)
    sig_components_changed = QtCore.Signal(list)
    sig_area_changed = QtCore.Signal(list)
    sig_pivot_move_ready = QtCore.Signal()

    def __init__(self, parent=None):
        """Initialize the BaseMergePage instance.

        Creates a new instance of the BaseMergePage class, setting up logging and scene call.

        Args:
            parent: The parent widget, defaults to None
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.use_new_area_select = True
        self.scene = scene_call.SceneCall()
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface for the BaseMergePage.

        Initializes the main layout, tab widget, and various UI components.
        Creates three tabs: work area, parametric parts, and AI assistance.
        Sets up the operation area, status bar, and floating panel.
        """
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create main tab widget
        # Use dayu_widgets styled tab widget
        self.tab_widget = MStyleMixin.instance_wrapper(MTabWidget())
        self.tab_widget.setTabPosition(QtWidgets.QTabWidget.North)
        self.main_layout.addWidget(self.tab_widget)

        # --- Tab 1: original content ---
        self.work_area_tab = QtWidgets.QWidget()
        self.work_area_layout = FramelessVLayout()
        self.work_area_tab.setLayout(self.work_area_layout)

        # Insert all original content into work_area_layout
        self.ring = None
        self.pivot_move = None
        self.dna_merge_container = None
        self.dna_merge_layout = None
        self.setup_operation_area(self.work_area_layout)
        self.work_area_layout.addSpacing(5)
        self.setup_status_bar(self.work_area_layout)
        self._setup_floating_panel()

        # --- Tab 2 and 3 are currently empty ---
        # Initialize ParametricAttachmentsWidget for tab 2
        self.param_tab = ParametricPartsWidget()
        self.ai_tab = QtWidgets.QWidget()
        self.ai_tab_layout = FramelessVLayout()
        self.ai_tab.setLayout(self.ai_tab_layout)

        # Add tabs to tab widget
        self.tab_widget.addTab(self.work_area_tab, "融合与捏脸工作区")
        self.tab_widget.addTab(self.param_tab, "参数化部件")
        self.tab_widget.addTab(self.ai_tab, "AI辅助")

    def setup_operation_area(self, parent_layout):
        """Set up the operation area of the UI.

        Creates the main operation container with horizontal layout and adds
        the DNA merge area and DNA library components.

        Args:
            parent_layout: The parent layout to add the operation area to
        """
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setHandleWidth(8)
        self.splitter.setSizes([100, 100, 100])
        self._style_splitter_handles()

        self.operation_container = QtWidgets.QFrame()
        self.operation_layout = FramelessHLayout()
        self.operation_container.setLayout(self.operation_layout)
        self.setup_face_operation_area(self.splitter)
        self.setup_dna_merge_area(self.splitter)
        self.operation_layout.addWidget(self.splitter)
        self.setup_dna_lib()

        parent_layout.addWidget(self.operation_container)

    def add_bottom_export_area(self, parent_layout):
        """Set up the bottom export area of the UI.

        Creates a container with buttons for LOD generation, model collapsing,
        and exporting functionality. Includes a dropdown for selecting export type
        and connects all buttons to their respective handlers.

        Args:
            parent_layout: The parent layout to add the export area to
        """
        self.bottom_export_area_container = MStyleMixin.cls_wrapper(QtWidgets.QFrame)()
        self.bottom_export_area_layout = FramelessHLayout()
        self.bottom_export_area_layout.setContentsMargins(5, 5, 5, 5)
        self.bottom_export_area_container.setLayout(self.bottom_export_area_layout)

        self.bottom_export_area_container.frameless().border_radius(5).background_color(
            const.DAYU_BG_IN_COLOR,
        )

        self.generate_lod_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.generate_lod_btn.text_beside_icon().svg("export.svg").small()
        self.generate_lod_btn.transparent_background(modifier=":disabled")
        self.generate_lod_btn.setText("LOD生成")

        self.collapse_model_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.collapse_model_btn.text_beside_icon().svg("collapse.svg").small()
        self.collapse_model_btn.setText("塌陷模型")
        self.collapse_model_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.collapse_model_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.PRIMARY_COLOR};
            }}
        """
        )
        self.collapse_model_btn.clicked.connect(self._on_collapse_model_clicked)

        self.generate_lod_btn.clicked.connect(self._on_generate_lod_clicked)
        self.bottom_export_area_layout.addWidget(self.generate_lod_btn)
        self.bottom_export_area_layout.addWidget(self.collapse_model_btn)

        self.bottom_export_area_layout.addStretch(1)

        export_layout = FramelessHLayout()

        self.export_type_combobox = MStyleMixin.instance_wrapper(dayu_widgets.MComboBox())
        self.export_type_combobox.frameless().border_radius(5).background_color(const.DAYU_BG_COLOR)
        self.export_type_combobox.addItems(["导出融合结果", "GOZ", "导出至库中"])
        self.export_type_combobox.setCurrentText("导出融合结果")
        self.export_type_combobox.setFixedWidth(180)

        self.export_dna_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.export_dna_btn.text_beside_icon().svg("export.svg").small()
        self.export_dna_btn.setText(" 导出")
        self.export_dna_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.export_dna_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.DAYU_PRIMARY_7};
            }}
        """
        )

        self.export_dna_btn.clicked.connect(self._on_export_clicked)

        export_layout.addWidget(self.export_type_combobox)
        export_layout.addWidget(self.export_dna_btn)
        self.bottom_export_area_layout.addLayout(export_layout)

        parent_layout.addWidget(self.bottom_export_area_container)

    def setup_dna_merge_area(self, parent_layout):
        """Set up the DNA merge area with ring widget and pivot move.

        Creates a container for the DNA merge functionality, including the functional area,
        DNA ring widget, and export controls. This is the main workspace for DNA merging operations.

        Args:
            parent_layout: The parent layout to add the DNA merge area to
        """
        self.dna_merge_container = QtWidgets.QFrame(parent=self)
        self.dna_merge_layout = FramelessVLayout()
        self.dna_merge_container.setLayout(self.dna_merge_layout)

        self.setup_functional_area(self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        self.setup_dna_ring(self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        self.add_bottom_export_area(self.dna_merge_layout)

        parent_layout.addWidget(self.dna_merge_container)

    def setup_face_operation_area(self, parent_layout):
        """
        Set up the face operation area with a tab widget containing face blend area selection,
        basic blend, and refine blend tabs.

        Args:
            parent_layout (QLayout): The parent layout to which the tab widget will be added.

        Raises:
            KeyError: If "shoulder" is not in the facearea_name_list when attempting to remove it.
        """
        # 创建水平布局容器作为主容器
        self.face_operation_container = QtWidgets.QWidget()
        self.face_operation_layout = QtWidgets.QHBoxLayout(self.face_operation_container)
        self.face_operation_layout.setContentsMargins(0, 0, 0, 0)
        self.face_operation_layout.setSpacing(0)
        parent_layout.addWidget(self.face_operation_container)

        # 创建内容堆叠部件
        self.face_content_stack = QtWidgets.QStackedWidget()

        # 创建右侧垂直标签栏容器
        self.face_tabs_container = QtWidgets.QWidget()
        self.face_tabs_layout = QtWidgets.QVBoxLayout(self.face_tabs_container)
        self.face_tabs_layout.setContentsMargins(0, 5, 5, 5)
        self.face_tabs_layout.setSpacing(2)
        self.face_tabs_layout.setAlignment(QtCore.Qt.AlignRight)

        # 设置内容区域和标签栏的比例为9:1
        self.face_operation_layout.addWidget(self.face_content_stack, 9)
        self.face_operation_layout.addWidget(self.face_tabs_container, 1)

        # 存储标签按钮的字典
        self.face_tab_buttons = {}
        self.current_face_tab_index = 0

        # --- Tab 1: Face blend area selection ---
        self.face_blend_area_tab = QtWidgets.QWidget()
        self.face_blend_area_layout = FramelessVLayout()
        self.face_blend_area_tab.setLayout(self.face_blend_area_layout)

        # Get all face area names from PINCH_CTRL_ATTR, remove "shoulder" if present
        facearea_name_list = [value for key, value in const.PINCH_CTRL_ATTR.items()]
        if "shoulder" in facearea_name_list:
            facearea_name_list.remove("shoulder")

        # Generate config for FaceAreaWidget
        facearea_config = FaceAreaWidget.generate_face_area_config(
            resource_root=const.FACEAREA_PATH,
            name_list=facearea_name_list,
            button_menu=[
                # Right-click menu actions for area selection widget
                (
                    "reset当前所选区域",
                    lambda name: (
                        self.scene.reset_merge(self.get_current_areas()),
                        self.reset_current_area_dna(self.get_current_areas()),
                    ),
                ),
                (
                    "reset全部区域",
                    lambda name: (
                        self.scene.reset_merge(facearea_name_list),
                        self.reset_current_area_dna(facearea_name_list),
                    ),
                ),
                ("全选", lambda name: self.area_select_widget.select_all()),
                ("随机融合", lambda name: self.area_select_widget.random_fuse()),
            ],
            enable_fuse_mode=False,
        )

        # Create the area selection widget and add to layout
        self.area_select_widget = FaceAreaWidget(facearea_config)
        self.face_blend_area_layout.addWidget(self.area_select_widget)

        # --- Tab 2: Basic blend (currently empty) ---
        self.basic_blend_tab = QtWidgets.QWidget()

        # --- Tab 3: Refine blend (currently empty) ---
        self.refine_blend_tab = QtWidgets.QWidget()
        self.refine_blend_tab_layout = FramelessVLayout()
        self.refine_blend_tab.setLayout(self.refine_blend_tab_layout)

        # 添加内容到堆叠部件
        self.face_content_stack.addWidget(self.face_blend_area_tab)  # 索引 0
        self.face_content_stack.addWidget(self.basic_blend_tab)  # 索引 1
        self.face_content_stack.addWidget(self.refine_blend_tab)  # 索引 2

        # 创建垂直标签按钮
        tab_titles = ["融合", "基础捏脸", "精细捏脸"]
        tab_style = """
            QPushButton {
                min-width: 18px;
                max-width: 24px;
                min-height: 120px;
                padding: 10px 5px;
                margin: 0px 0px;
                font-size: 17px;
                font-weight: normal;
                border-radius: 3px;
                background-color: rgba(65, 65, 65, 1);
                color: rgba(230, 230, 230, 1);
                border: 3px solid rgba(0, 0, 0, 0.1);
                text-align: center;
                letter-spacing: 2px;
                line-height: 1.4;
            }
            QPushButton:hover {
                color: rgba(255, 255, 255, 1);
                background-color: rgba(100, 100, 100, 1);
                border: 2px solid rgba(0, 0, 0, 0.3);
            }
            QPushButton:checked {
                background-color: rgba(50, 50, 50, 1);
                color: rgba(220, 220, 220, 1);
                font-weight: normal;
                border: 1px solid rgba(0, 0, 0, 0);
                border-right: 2px solid rgba(20, 160, 200, 1);
            }
        """

        for i, title in enumerate(tab_titles):
            vertical_title = "\n".join(title)
            tab_button = QtWidgets.QPushButton(vertical_title)
            tab_button.setCheckable(True)
            tab_button.setStyleSheet(tab_style)
            tab_button.clicked.connect(lambda checked=False, idx=i: self.switch_face_tab(idx))
            self.face_tabs_layout.addWidget(tab_button)
            self.face_tab_buttons[i] = tab_button

        self.face_tabs_layout.addStretch()

        self.switch_face_tab(0)
        self.disable_face_tabs()

    def disable_face_tabs(self):
        self.face_blend_area_tab.setEnabled(False)
        self.basic_blend_tab.setEnabled(False)
        self.refine_blend_tab.setEnabled(False)

    def enable_face_tabs(self):
        self.face_blend_area_tab.setEnabled(True)
        self.basic_blend_tab.setEnabled(True)
        self.refine_blend_tab.setEnabled(True)

    def switch_face_tab(self, index):
        self.current_face_tab_index = index
        self.face_content_stack.setCurrentIndex(index)

        for i, button in self.face_tab_buttons.items():
            button.setChecked(i == index)

    def _on_collapse_model_clicked(self):
        reply = QtWidgets.QMessageBox.question(
            self,
            "确认操作",
            "确定要执行模型塌陷操作吗？此操作不可撤销。",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )
        if reply == QtWidgets.QMessageBox.Yes:
            self.logger.info("Collapsing model")

            self._collapse_and_add_dna()

    def _collapse_and_add_dna(self):
        """Collapse the model and automatically load the specified DNA file.

        Performs the following operations:
        1. Sets a flag to indicate the model was collapsed
        2. Captures a screenshot of the viewport for texture
        3. Resets the DNA ring and adds the base DNA component
        4. Notifies the parent widget of the DNA addition

        Handles errors by displaying appropriate error messages to the user.
        """
        if hasattr(self.parent(), "is_from_collapse_model"):
            self.parent().is_from_collapse_model = True
        else:
            setattr(self.parent(), "is_from_collapse_model", True)

        dna_file_path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)

        try:
            mesh.focus_camera_on_head()
            texture_path = get_viewport_screenshot()

            if not os.path.exists(dna_file_path) or not os.path.exists(texture_path):
                self.logger.error(f"DNA文件或贴图不存在: {dna_file_path} 或 {texture_path}")
                QtWidgets.QMessageBox.warning(
                    self,
                    "文件不存在",
                    f"无法找到指定的DNA文件或贴图:\n{dna_file_path}\n{texture_path}",
                    QtWidgets.QMessageBox.Ok,
                )
                return

            self.reset_dna_ring()
            widget = self.ring.create_component(self._create_dna_info(dna_file_path, texture_path))
            self.ring.add_component_to_center(widget)

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self,
                "操作失败",
                f"塌陷模型过程中发生错误:\n{str(e)}",
                QtWidgets.QMessageBox.Ok,
            )
            return

        if hasattr(self.parent(), "slot_dna_added_to_ring"):
            self.parent().slot_dna_added_to_ring(self._create_dna_info(dna_file_path, texture_path))
        if hasattr(self, "ring") and self.ring:
            self.ring.setAcceptDrops(True)

    def _create_dna_info(self, dna_file_path, texture_path):
        """创建DNA信息字典

        Args:
            dna_file_path: DNA文件路径
            texture_path: 贴图路径

        Returns:
            dict: DNA信息字典
        """
        return {
            "dna_file_path": dna_file_path,
            "dna_texture_path": texture_path,
            "thumbnail_file_path": texture_path,
            "dna_name": "BB_AT_Jacket_1215",
            "dna_order": 1,
            "collapsed_model": True,
        }

    def setup_dna_ring(self, parent_layout):
        """Set up the DNA ring widget and initialize pivot move.

        Creates the drag point ring widget for DNA component manipulation and
        initializes the pivot move functionality. Sets up signal connections and
        emits the pivot move ready signal.

        Args:
            parent_layout: The parent layout to add the DNA ring to
        """
        self.logger.info("Setting up DNA ring")

        if not self.ring:
            self.ring = DragPointRingWidget(parent=self)
            self.pivot_move = UIPivotMove(ring_widget=self.ring, parent=self)
            self._setup_ring_connections()
            self.sig_pivot_move_ready.emit()

        parent_layout.addWidget(self.ring)
        self.logger.info("DNA ring setup completed")

    def _style_splitter_handles(self):
        """Style the splitter handles to make them more visible and user-friendly"""
        # Apply stylesheet to make handles more visible
        handle_style = f"""
            QSplitter::handle {{
                background-color: {const.BUTTON_BORDER};
                border-radius: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {const.BUTTON_BORDER};
            }}
            QSplitter::handle:pressed {{
                background-color: {const.BUTTON_BORDER};
            }}
        """
        self.splitter.setStyleSheet(handle_style)

        # Set cursor to horizontal resize cursor for better UX
        for i in range(self.splitter.count() - 1):  # For each handle
            handle = self.splitter.handle(i + 1)
            handle.setCursor(QtCore.Qt.SplitHCursor)

    def _setup_floating_panel(self):
        """Set up the floating panel for additional controls.

        Creates a floating panel with content panel inside it. The floating panel
        provides access to editing functionality that can be expanded or collapsed.
        Connects signals and positions the panel in the UI.
        """
        self.floating_panel = FloatingPanel(self.dna_merge_container)

        self.content_panel = ContentPanel(self)

        self.floating_panel.set_content_widget(self.content_panel)

        self.floating_panel.sig_expanded_changed.connect(self._on_floating_panel_state_changed)
        QtCore.QTimer.singleShot(200, lambda: self.floating_panel.move(5, 865))

    def _on_floating_panel_state_changed(self, expanded):
        """Handle changes in the floating panel's expanded state.

        When the panel is expanded (edit mode), disables certain UI elements and adjusts
        layer selectability to facilitate editing. When collapsed, restores normal operation
        mode and re-enables previously disabled UI elements.

        Args:
            expanded: Boolean indicating whether the panel is expanded (True) or collapsed (False)
        """
        if expanded:
            mesh.set_head_layer_unselectable(const.CONTROLLER_LAYER_NAME, 2)
            mesh.set_head_layer_unselectable(const.HEAD_LAYER_NAME, 0)
            cmds.select(clear=True)
            if self.pivot_move and self.pivot_move._ring_widget:
                self.pivot_move._ring_widget.setEnabled(False)
                self.undo_face_btn.setEnabled(False)

                self.disable_face_tabs()
        else:
            self.content_panel.blendshape_target_set_enabled(self.content_panel, in_or_out="out")
            self.content_panel.reset_target_editing(self.content_panel)
            self.content_panel.remove_selected_target(self.content_panel)

            mesh.set_head_layer_unselectable(const.CONTROLLER_LAYER_NAME, 0)
            mesh.set_head_layer_unselectable(const.HEAD_LAYER_NAME, 2)
            cmds.select(clear=True)

            if self.pivot_move and self.pivot_move._ring_widget:
                self.pivot_move._ring_widget.setEnabled(True)
                self.undo_face_btn.setEnabled(True)

                self.enable_face_tabs()

                self.logger.info("编辑模式已关闭：启用pivot_move中的ring_widget控制器")

    def _setup_ring_connections(self):
        """Set up signal connections for the DNA ring widget.

        Connects the DNA ring signals to their respective handlers to respond to
        component additions, removals, and changes. Also connects the pivot weight
        change signal from the pivot move widget.

        Logs an error if either the ring or pivot_move is not initialized.
        """
        if not self.ring or not self.pivot_move:
            self.logger.error("Cannot setup connections - ring or pivot_move not initialized")
            return
        self.ring.sig_dna_added.connect(self._on_component_changed)
        self.ring.sig_dna_removed.connect(self._on_component_changed)
        self.ring.sig_dna_changed.connect(self._on_component_changed)
        self.pivot_move.sig_pivot_weight_changed.connect(self._on_weights_changed)
        self.logger.info("Signal connections established")

    def _disconnect_ring_connections(self):
        """Safely disconnect all ring signal connections.

        Attempts to disconnect all signal connections between the ring widget,
        pivot move widget, and their respective handlers. Catches and logs any
        errors that occur during disconnection to prevent exceptions from
        propagating.
        """
        if self.ring:
            try:
                self.ring.sig_dna_added.disconnect(self._on_component_changed)
                self.ring.sig_dna_removed.disconnect(self._on_component_changed)
                self.ring.sig_dna_changed.disconnect(self._on_component_changed)
            except (TypeError, RuntimeError) as e:
                self.logger.warning("Error disconnecting ring signals: %s", str(e))

        if self.pivot_move:
            try:
                self.pivot_move.sig_pivot_weight_changed.disconnect(self._on_weights_changed)
            except (TypeError, RuntimeError) as e:
                self.logger.warning("Error disconnecting pivot_move signals: %s", str(e))

    def reset_dna_ring(self):
        """Clear DNA components from the ring without recreating the ring widget.

        Removes all DNA components from the ring and reinitializes the pivot.
        This is used when resetting the state without destroying the entire widget.

        Returns:
            bool: True if the operation was successful, False otherwise
        """
        try:
            self.logger.info("清除环中的DNA组件")

            if not hasattr(self, "ring") or not self.ring:
                self.logger.error("环组件不存在，无法清除DNA")
                return False

            self.ring.clear_components()
            collapse.delete_mesh_history()
            if hasattr(self, "pivot_move") and self.pivot_move:
                self.pivot_move.init_pivot()

            self.logger.info("DNA环中的组件已清除")
            collapse.delete_nodes()
            return True

        except Exception as e:
            self.logger.error("清除DNA环组件失败: %s", str(e))
            return False

    def _on_weights_changed(self, weights):
        """Handle weight changes from pivot move.

        Receives weight changes from the pivot move widget and emits a signal
        to notify other components about the weight changes. This is used to
        update the DNA merging based on the new weights.

        Args:
            weights: List of weight values for the DNA components
        """
        if not self.ring:
            self.logger.error("Ring widget not initialized")
            return

        self.sig_weights_changed.emit(weights)

    def _on_component_changed(self, *args):
        """Handle component changes in ring widget.

        Responds to DNA components being added, removed, or changed in the ring widget.
        Gets the current positions of all components and emits a signal with this information
        to update the DNA merging accordingly.

        Args:
            *args: Variable arguments passed from the signal
        """
        if not self.ring:
            self.logger.error("Ring widget not initialized")
            return

        positions = self.ring.get_component_positions()
        self.logger.debug("Component change detected")
        self.logger.debug("Number of components: %d", len(positions))
        self.logger.debug("Component positions: %s", positions)
        self.sig_components_changed.emit(positions)

    def setup_dna_lib(self):
        """Set up the DNA library widget.

        Creates and configures the DNA library widget, which provides access to
        available DNA components that can be used for merging. Sets the widget to
        be frameless and adds it to the operation layout.
        """
        self.dna_lib = DNALibWidget.instance()
        self.dna_lib.frameless()
        self.dna_lib.setMinimumSize(500, 300)
        self.splitter.addWidget(self.dna_lib)

    def remove_dna_lib(self):
        """Remove the DNA library widget from the layout.

        Removes the DNA library widget from the operation layout without destroying it.
        This is typically used when reconfiguring the UI or when the DNA library is
        temporarily not needed.
        """
        self.operation_layout.removeWidget(self.dna_lib)

    def setup_functional_area(self, parent_layout):
        """Set up the functional area of the UI.

        Creates a container for functional controls including top and bottom functional areas
        and random/reset controls. The functional area is initially disabled and will be
        enabled when appropriate conditions are met.

        Args:
            parent_layout: The parent layout to add the functional area to
        """
        self.functional_area_container = QtWidgets.QFrame()
        self.functional_area_layout = FramelessVLayout()
        self.functional_area_container.setLayout(self.functional_area_layout)
        self.functional_area_container.setEnabled(False)
        self.functional_area_container.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)

        self.setup_bottom_functional_area(self.functional_area_layout)

        separator = QtWidgets.QFrame(self)
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.functional_area_layout.addWidget(separator)

        parent_layout.addWidget(self.functional_area_container)

    def reset_current_area_dna(self, current_areas):
        self.scene.reset_merge(current_areas)

        if hasattr(self, "pivot_move") and self.pivot_move:
            weights = [1.0, 0.0, 0.0, 0.0]
            self.pivot_move.set_pos_by_weight(weights)

    def on_reset_face_clicked(self):
        reply = QtWidgets.QMessageBox.question(
            self,
            "确认重置",
            "确定要重置捏脸吗？",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )

        if reply == QtWidgets.QMessageBox.Yes:
            customization.clear_all_ctrls()

    def setup_bottom_functional_area(self, parent_layout):
        self.bottom_functional_area_container = MStyleMixin.cls_wrapper(QtWidgets.QFrame)()
        self.bottom_functional_area_layout = FramelessVLayout()
        self.bottom_functional_area_layout.setSpacing(0)
        self.bottom_functional_area_container.setLayout(self.bottom_functional_area_layout)

        self.function_buttons_container = QtWidgets.QFrame()
        self.function_buttons_layout = FramelessHLayout()
        self.function_buttons_layout.setSpacing(0)
        self.function_buttons_layout.setContentsMargins(20, 0, 20, 0)
        self.function_buttons_container.setLayout(self.function_buttons_layout)

        separator = QtWidgets.QFrame(self)
        separator.setFrameShape(QtWidgets.QFrame.VLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.function_buttons_layout.addWidget(separator)

        self.function_buttons_layout.addSpacing(20)
        face_control_container = QtWidgets.QFrame()
        face_control_layout = FramelessHLayout()
        face_control_layout.setSpacing(5)
        face_control_layout.addStretch(1)
        face_control_container.setLayout(face_control_layout)

        # 添加普通按钮在捏脸对称左边
        self.undo_face_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.undo_face_btn.transparent_background(modifier=":disabled")
        self.undo_face_btn.setText(" 重置捏脸")
        self.undo_face_btn.clicked.connect(self.on_reset_face_clicked)
        face_control_layout.addWidget(self.undo_face_btn)
        face_control_layout.addSpacing(10)

        self.face_symmetry_label = dayu_widgets.MLabel("捏脸对称:").secondary()
        self.face_symmetry_checkbox = dayu_widgets.MCheckBox()
        self.face_symmetry_checkbox = MStyleMixin.instance_wrapper(self.face_symmetry_checkbox)
        self.face_symmetry_checkbox.transparent_background()
        self.face_symmetry_checkbox.setToolTip("开启后，捏脸操作将同步到两侧")
        self.face_symmetry_checkbox.setChecked(True)
        self.face_symmetry_checkbox.stateChanged.connect(self._on_face_symmetry_changed)
        face_control_layout.addWidget(self.face_symmetry_label)
        face_control_layout.addWidget(self.face_symmetry_checkbox)
        self.mode_combo_box = QComboBox()
        self.mode_combo_box.addItems(["Basic", "Detailed"])
        self.mode_combo_box.currentIndexChanged.connect(self.switch_mode)
        face_control_layout.addWidget(self.mode_combo_box)

        self.function_buttons_layout.addWidget(face_control_container)
        self.bottom_functional_area_layout.addWidget(self.function_buttons_container)

        parent_layout.addWidget(self.bottom_functional_area_container, 6)

    def switch_mode(self, index):
        """Switch between Basic and Detailed face customization modes.

        Changes the face customization mode based on the selected index in the mode combo box.
        The Basic mode provides simplified controls, while the Detailed mode offers more
        precise control options.

        Args:
            index: The index of the selected item in the mode combo box
        """
        mode = self.mode_combo_box.itemText(index)
        if mode == "Basic":
            customization.change_to_basic()
        elif mode == "Detailed":
            customization.change_to_detailed()

    def _on_multi_select_changed(self, checked):
        """Handle changes to the multi-select checkbox state.

        When multi-select is enabled, allows multiple area buttons to be selected simultaneously.
        When disabled, enforces exclusive selection by unchecking all but the first selected button.

        Args:
            checked: Boolean indicating whether multi-select is enabled (True) or disabled (False)
        """
        if hasattr(self, "area_select_widget"):
            self.area_select_widget.set_exclusive(not checked)

            if not checked:
                checked_buttons = [btn for btn in self.area_select_widget.button_group.buttons() if btn.isChecked()]
                if len(checked_buttons) > 1:
                    for button in checked_buttons[1:]:
                        button.setChecked(False)
                        button.setStyleSheet("")

                    selected_areas = [checked_buttons[0].text()]
                    self.area_select_widget.selected_buttons_changed.emit(selected_areas)

    def setup_preview_animation_select_area(self, parent_layout):
        pass

    def setup_status_bar(self, parent_layout):
        self.status_bar = ProgressWidget()
        parent_layout.addWidget(self.status_bar)

    def get_merge_state(self):
        """Get current state of merge UI for debugging."""
        state = {
            "components_count": len(self.ring.components),
            "component_positions": self.ring.get_component_positions(),
            "point_position": self.ring.point_pos,
            "weights": self.pivot_move.weights if self.pivot_move else None,
        }
        return state

    def get_current_areas(self):
        if self.use_new_area_select:
            return self.area_select_widget.current_areas()
        else:
            return [self.merge_area_select_combobox.currentText()]

    def resizeEvent(self, event):
        super(BaseMergePage, self).resizeEvent(event)

        if hasattr(self, "floating_panel") and self.floating_panel:
            self.floating_panel.updatePosition()

        if hasattr(self, "random_and_reset_area_container"):
            parent_width = self.functional_area_container.width()
            self.random_and_reset_area_container.setFixedWidth(parent_width // 3)

    def _on_face_symmetry_changed(self, state):
        """Handle changes to face symmetry state.

        Updates the face customization symmetry setting based on checkbox state.
        When enabled, changes to one side of the face are mirrored to the other side.
        When disabled, each side can be edited independently.

        Args:
            state: The current state of the symmetry checkbox (True for checked, False for unchecked)
        """
        symmetrical = customization.Iosymmetrical()
        if state:
            symmetrical.set_symmetrical_true()
            customization.symmetry_mode()

        else:
            symmetrical.set_symmetrical_false()

            customization.remove_selection_changed_script_jobs()

    def _on_export_clicked(self):
        """Handle export button click event.

        Exports the current model based on the selected export type in the dropdown.
        Options include exporting the merged result or sending to GoZ (ZBrush).
        Reports the export action to the analytics system.
        """
        export_type = self.export_type_combobox.currentText()
        if export_type == "导出融合结果":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="export mesh",
                    tool_name=const.PACKAGE_NAME,
                )
            export_utils.duplicate_and_export_head_mesh()
        elif export_type == "GOZ":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="goz",
                    tool_name=const.PACKAGE_NAME,
                )
            goz_utils.send_to_goz()
        elif export_type == "导出至库中":
            with core.get_reporter_instance() as api:
                api.report_count(
                    event_name="export",
                    action="export wrap",
                    tool_name=const.PACKAGE_NAME,
                )
            export_dna_window = CharacterExportDialog(parent=self)
            if export_dna_window.exec_():
                progress_callback = lambda progress: (
                    self.status_bar.progress_bar.setValue(progress.get("value", 0)),
                    self.status_bar.success_label.setText(progress.get("text", "")) if "text" in progress else None,
                )
                export_utils.export_character_package_from_dna(export_dna_window.character_data, progress_callback)
                self.dna_lib.reload_dna_list()
                self.dna_lib.update_all_tags()

        else:
            cmds.warning(f"invalid export type{export_type}")

    def _on_generate_lod_clicked(self):
        """Handle LOD generation button click event.

        Creates an instance of the LODGeneration class and calls its generate_lod method
        to create lower level of detail models based on the current model.
        """
        lod_generation = LODGeneration()
        lod_generation.generate_lod()

    def create_overlay_delayed(self):
        self.overlay = create_overlay(
            "捏脸绑定转换",
            self.on_binding_button_clicked,
            self,
            self.tab_widget,
        )

    def on_binding_button_clicked(self):
        self.logger.debug("点击了捏脸绑定转换按钮")
        self.output_dna_path = mesh_util.create_workspace_dna_dir(const.PINCH_OUTPUT_DNA_NAME)
        reset_face_anim_attr()
        self.reset_mesh_configuration()
        self.reset_joint_position()
        riglogic.SetupRiglogic(self.output_dna_path, const.PINCE_CTRL_NAME, const.PINCE_NODE_NAME, connect=False)
        animation_theater_page.find_target_and_create_overlay(self, animation_theater_page.AnimationTheaterPage)

    def reset_joint_position(self):
        reader = dna.load_dna_calib(self.output_dna_path)
        joint_count = reader.getJointCount()
        mesh_util.disconnect_joint_attr(joint_count, reader)
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            mesh.set_joint_position(
                joint_name,
                reader.getNeutralJointTranslation(joint_index),
                reader.getNeutralJointRotation(joint_index),
            )

    def reset_mesh_configuration(self):
        for mesh_name in dna.TemplateDna.get_mesh_name():
            cmds.delete(mesh_name)
            now_name = mesh_name + f"_{const.PINCH_OUTPUT_DNA_NAME}"
            cmds.rename(now_name, mesh_name)
            cmds.setAttr(f"{mesh_name}.visibility", 1)
