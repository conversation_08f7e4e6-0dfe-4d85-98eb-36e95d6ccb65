# Import built-in modules


# Import third-party modules
from maya import cmds

# Import local modules
from cgame_avatar_factory import constants as const
import cgame_avatar_factory.api.dna_utils as dna
from cgame_avatar_factory.merge import mesh_util


class PinchFaceCreate:
    """Create and save DNA file with pinch face data

    Handles loading DNA calibration data, updating joint and vertex positions,
    and saving the modified DNA file.
    """

    def __init__(self):
        """Initialize PinchFaceCreate with DNA path"""
        self.reader = dna.TemplateDna.get_reader()
        self.output_path = mesh_util.create_workspace_dna_dir(const.PINCH_OUTPUT_DNA_NAME)
        self.writer = dna.init_writer_from_reader(self.output_path, self.reader)
        self.mesh_list = dna.TemplateDna.get_mesh_name()

        dna.run_joints_command(self.reader, self.writer)
        self.writer.write()

    def get_output_path(self) -> str:
        """Get the output path of the saved DNA file

        Returns:
            str: Path where DNA file was saved
        """
        return self.output_path

    def run_vertex_command(self, reader, mesh_list, writer):
        """Update vertex positions in DNA file

        Gets current vertex positions from Maya meshes
        and updates them in the DNA writer.

        Args:
            reader: DNA reader object
            mesh_list: List of mesh names
            writer: DNA writer object
        """
        mesh_count = reader.getMeshCount()
        for i in range(mesh_count):
            name = reader.getMeshName(i)
            for m in mesh_list:
                if name == m:
                    vertices_position = []
                    vertices = cmds.ls("{}.vtx[*]".format(name), fl=True)
                    for vertex in vertices:
                        position = cmds.pointPosition(vertex, world=True)
                        vertices_position.append(position)
                    writer.setVertexPositions(i, vertices_position)
