"""Create and setup riglogic node."""
# Import future modules
from __future__ import annotations

# Import built-in modules
import logging

# Import third-party modules
from maya import cmds

# Import local modules
import cgame_avatar_factory.api.dna_utils as dna
import cgame_avatar_factory.constants as const

# Import maya modules
from cgame_avatar_factory.merge import mesh_util


class SetupRiglogic:
    """Set up riglogic node and controls for DNA-based character

    Creates and configures a riglogic node that drives character joints
    based on DNA file data. Sets up control attributes and connections.

    Args:
        dna_path: Path to DNA file
    """

    def __init__(self, dna_path: str, ctrl_name: str, node_name: str, connect: bool = True):
        """Initialize riglogic setup

        Args:
            dna_path: Path to DNA file
        """
        self.ctrl = ctrl_name
        self.node_name = node_name
        self.dna_path = dna_path
        self.reader = dna.load_dna_calib(self.dna_path)
        self.is_connected = connect
        self.setup()

    def setup(self):
        """Set up riglogic node and controls

        Creates riglogic node, main control, and sets up attributes.

        Raises:
            RuntimeError: If riglogic node creation fails
        """
        if cmds.objExists(self.node_name):
            self.node_name = self.node_name
        else:
            self.node_name = self.create_node(self.node_name)
            if not self.node_name:
                raise RuntimeError("Failed to create riglogic node")
        if not cmds.objExists(self.ctrl):
            self.create_main_ctrl(self.ctrl)
            self.set_translation_multiplier()
        self.set_node_attributes()

    def set_node_attributes(self):
        """Set up riglogic node attributes

        Sets DNA path, translation multiplier, and connects joint attributes.
        """
        self.set_dna_path()
        self.connect_joints_attr()

    def set_dna_path(self):
        """Set DNA file path attribute on riglogic node"""
        cmds.setAttr(f"{self.node_name}.{const.NODE_DNA_FILE}", self.dna_path, type="string")

    def set_translation_multiplier(self):
        """Set translation multiplier attribute on riglogic node"""
        cmds.setAttr(f"{self.node_name}.{const.TRANMULTIPLIER}", 1.0)

    def create_node(self, node_name):
        """Create riglogic node

        Returns:
            str: Name of created node, or None if creation fails
        """

        if not cmds.pluginInfo(const.PLUGINS_NAME, query=True, loaded=True):
            try:
                cmds.loadPlugin(const.PLUGINS_NAME)
            except Exception as e:
                logging.error(f"Failed to load embeddedRL4 plugin: {str(e)}")
                return None

        node = cmds.createNode(const.RL_NODE_TYPE, name=node_name)
        return node

    def create_main_ctrl(self, ctrl):
        """Create main control and set up raw control attributes

        Creates main control if it doesn't exist and adds attributes
        for each raw control from the DNA file.
        """

        cmds.group(name=ctrl, empty=True)
        for attr in const.ATTR_LIST:
            cmds.setAttr(f"{ctrl}{attr}", lock=True, keyable=False, channelBox=False)
        cmds.setAttr(f"{ctrl}.v", 0, lock=True, keyable=False, channelBox=False)
        raw_control_count = self.reader.getRawControlCount()
        for raw_index in range(raw_control_count):
            raw_control_name = self.reader.getRawControlName(raw_index)
            ctrl_name, attr_name = raw_control_name.split(".")

            cmds.addAttr(
                ctrl_name,
                longName=attr_name,
                attributeType="float",
                minValue=0.0,
                maxValue=1.0,
                defaultValue=0.0,
                keyable=True,
            )
            dst_attr = f"{self.node_name}.inputs[{raw_index}]"
            cmds.connectAttr(raw_control_name, dst_attr)

    def clear_connected_array_attribute(self, node, array_attr_name):
        """Clear all elements from an array attribute

        Args:
            node: Node with array attribute
            array_attr_name: Name of array attribute to clear
        """
        attr_elements = cmds.getAttr(f"{node}.{array_attr_name}", multiIndices=True)
        if attr_elements:
            for i in attr_elements:
                attr_element = f"{node}.{array_attr_name}[{i}]"
                cmds.removeMultiInstance(attr_element, b=True)

    def _connect_joint_attribute(self, joint_name, attr_type, attr_index, output_index):
        """连接单个关节属性

        Args:
            joint_name: 关节名称
            attr_type: 属性类型(t/r/s)
            attr_index: 属性索引(x/y/z)
            output_index: 输出索引值

        Returns:
            int: 递增后的输出索引值
        """
        attr_map = {"t": 0, "r": 1, "s": 2}  # 属性类型到DNA_ATTR_LIST索引的映射
        axis = ["x", "y", "z"][attr_index]
        attr_list_index = attr_map[attr_type]

        jnt_attr = f"{joint_name}.{attr_type}{axis}"
        dna_attr = f"{self.node_name}.{const.DNA_ATTR_LIST[attr_list_index]}[{output_index}]"

        cmds.connectAttr(dna_attr, jnt_attr)
        return output_index + 1

    def connect_joints_attr(self):
        """Connect joint attributes to riglogic node

        Sets up connections between riglogic node outputs and joint
        translate, rotate and scale attributes based on DNA data.
        """
        self.clear_connected_array_attribute(self.node_name, const.DNA_ATTR_LIST[0])
        self.clear_connected_array_attribute(self.node_name, const.DNA_ATTR_LIST[1])
        self.clear_connected_array_attribute(self.node_name, const.DNA_ATTR_LIST[2])
        joint_count = self.reader.getJointCount()

        if self.is_connected:
            mesh_util.disconnect_joint_attr(joint_count, self.reader)

        translation_output_index = 0
        rotation_output_index = 0
        scale_output_index = 0

        for i in range(self.reader.getJointGroupCount()):
            out_ids_list = self.reader.getJointGroupOutputIndices(i)
            for out_id in out_ids_list:
                jnt_id = int(out_id / 9)
                joint_name = self.reader.getJointName(jnt_id)

                if not cmds.objExists(joint_name):
                    logging.warning(f"Joint {joint_name} does not exist, skipping connection")
                    continue

                remainder = out_id % 9
                if remainder < 3:
                    translation_output_index = self._connect_joint_attribute(
                        joint_name,
                        "t",
                        remainder,
                        translation_output_index,
                    )
                elif remainder < 6:
                    rotation_output_index = self._connect_joint_attribute(
                        joint_name,
                        "r",
                        remainder - 3,
                        rotation_output_index,
                    )
                else:
                    scale_output_index = self._connect_joint_attribute(
                        joint_name,
                        "s",
                        remainder - 6,
                        scale_output_index,
                    )
