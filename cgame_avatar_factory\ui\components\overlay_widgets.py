#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Import third-party modules
from qtpy import QtCore
from qtpy import QtWidgets
from qtpy.QtWidgets import QGraphicsBlurEffect

# Import local modules
from cgame_avatar_factory import constants as const


def create_overlay(button_text, button_clicked_callback, parent=None, target_widget=None):
    """创建带模糊背景的覆盖层和圆形按钮"""

    overlay = QtWidgets.QWidget(parent)
    overlay.target_widget = target_widget
    overlay.setStyleSheet("background-color: rgba(0, 0, 0, 0.2);")
    if parent:
        overlay.setGeometry(0, 0, parent.width(), parent.height())

    overlay.blur_effect = QGraphicsBlurEffect()
    overlay.blur_effect.setBlurRadius(3)

    layout = QtWidgets.QVBoxLayout(overlay)
    layout.setAlignment(QtCore.Qt.AlignCenter)
    btn = QtWidgets.QPushButton(button_text)
    btn.setFixedSize(240, 240)
    btn.setStyleSheet(
        f"""
        QPushButton {{
            background-color: rgba(33, 150, 243, 0.8);
            color: {const.BUTTON_CHECKED_TEXT};
            font-size: 30px;
            font-weight: bold;
            border-radius: 120px;
            border: 3px solid {const.OVERLAY_BORDER};
            text-align: center;
            letter-spacing: 3px;
            line-height: 160%;
        }}
        QPushButton:hover {{ background-color: rgba(25, 118, 210, 0.9); border: 2px solid {const.OVERLAY_HOVER_BG}; }}
        QPushButton:pressed {{ background-color: rgba(13, 71, 161, 1.0); border: 2px solid {const.OVERLAY_PRESSED_BG}; }}
    """
    )
    btn.clicked.connect(lambda: hide_overlay(overlay, button_clicked_callback))
    layout.addWidget(btn)

    def resize_handler(event):
        if overlay.parent():
            overlay.setGeometry(0, 0, overlay.parent().width(), overlay.parent().height())
        QtWidgets.QWidget.resizeEvent(overlay, event)

    overlay.resizeEvent = resize_handler

    show_overlay(overlay)
    return overlay


def show_overlay(overlay):
    """显示覆盖层并应用模糊效果"""
    if overlay.parent():
        overlay.setGeometry(0, 0, overlay.parent().width(), overlay.parent().height())
    if overlay.target_widget:
        overlay.target_widget.setGraphicsEffect(overlay.blur_effect)
    overlay.raise_()
    overlay.setVisible(True)


def hide_overlay(overlay, callback=None):
    """隐藏覆盖层并移除模糊效果"""
    overlay.setVisible(False)
    if overlay.target_widget:
        overlay.target_widget.setGraphicsEffect(None)
    if callback:
        callback()
