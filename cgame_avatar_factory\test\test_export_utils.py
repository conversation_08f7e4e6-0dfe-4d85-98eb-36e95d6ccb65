# -*- coding: utf-8 -*-
"""Test export_utils module."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import sys
import unittest
from unittest import mock

# 模拟所有需要的模块
sys.modules["maya"] = mock.MagicMock()
sys.modules["maya.cmds"] = mock.MagicMock()
sys.modules["dna"] = mock.MagicMock()
sys.modules["dna_viewer"] = mock.MagicMock()
sys.modules["dna_viewer.DNA"] = mock.MagicMock()
sys.modules["lightbox_config"] = mock.MagicMock()

# 模拟 Configuration 类
mock_config = mock.MagicMock()
sys.modules["lightbox_config"].Configuration = mock_config

# 模拟 cgame_avatar_factory.config 模块
mock_config_module = mock.MagicMock()
mock_config_module.get_config.return_value = {}
sys.modules["cgame_avatar_factory.config"] = mock_config_module

# 模拟 constants 模块
mock_constants = mock.MagicMock()
mock_constants.BASE_HEAD_MESH_NAME = "head_lod0_mesh"
mock_constants.HEAD_DIFFUSE_TEX_NAME = "head_diffuse_texture"
sys.modules["cgame_avatar_factory.constants"] = mock_constants

# Mock TemplateDna class
class MockTemplateDna:
    @staticmethod
    def get_mesh_name():
        return []

    @staticmethod
    def get_reader():
        mock_reader = mock.MagicMock()
        mock_reader.getMeshCount.return_value = 0
        return mock_reader

    @staticmethod
    def write_mesh(path, mesh_list):
        return True


# Create a mock for dna_utils module
mock_dna_utils = mock.MagicMock()
mock_dna_utils.TemplateDna = MockTemplateDna
mock_dna_utils.get_mesh_name = MockTemplateDna.get_mesh_name
mock_dna_utils.write_mesh = MockTemplateDna.write_mesh
sys.modules["cgame_avatar_factory.api.dna_utils"] = mock_dna_utils

# Import local modules
# Now import export_utils
from cgame_avatar_factory.api import export_utils


class TestExportUtils(unittest.TestCase):
    """Test cases for export_utils module."""

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    @mock.patch("cgame_avatar_factory.api.export_utils.dna.TemplateDna.get_mesh_name")
    def test_duplicate_and_export_head_mesh_success(self, mock_get_mesh_name, mock_cmds):
        """Test successful duplication and export of head mesh."""
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh", "eye_l_mesh", "eye_r_mesh"]
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_lod0_mesh1", "eye_l_mesh1", "eye_r_mesh1"]
        mock_cmds.parent.side_effect = [["head_lod0_mesh1"], ["eye_l_mesh1"], ["eye_r_mesh1"]]
        mock_cmds.rename.side_effect = ["head_lod0_mesh_output", "eye_l_mesh_output", "eye_r_mesh_output"]
        mock_cmds.fileDialog2.return_value = ["/path/to/export.fbx"]

        # 调用被测试的函数
        export_utils.duplicate_and_export_head_mesh()

        # 验证函数调用
        mock_get_mesh_name.assert_called_once()
        mock_cmds.objExists.assert_called()
        mock_cmds.select.assert_called()
        mock_cmds.duplicate.assert_called_once_with(returnRootsOnly=False)
        mock_cmds.parent.assert_called()
        mock_cmds.rename.assert_called()
        mock_cmds.fileDialog2.assert_called_once_with(
            fileFilter="FBX Files (*.fbx)",
            dialogStyle=2,
            fileMode=0,
            caption="Export FBX",
        )
        mock_cmds.file.assert_called_once_with(
            "/path/to/export.fbx",
            force=True,
            exportSelected=True,
            type="FBX export",
        )
        mock_cmds.delete.assert_called_once_with(
            ["head_lod0_mesh_output", "eye_l_mesh_output", "eye_r_mesh_output"],
        )

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    @mock.patch("cgame_avatar_factory.api.export_utils.dna.TemplateDna.get_mesh_name")
    def test_duplicate_and_export_head_mesh_cancel(self, mock_get_mesh_name, mock_cmds):
        """Test cancellation of export dialog."""
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh", "eye_l_mesh", "eye_r_mesh"]
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_lod0_mesh1", "eye_l_mesh1", "eye_r_mesh1"]
        mock_cmds.parent.side_effect = [["head_lod0_mesh1"], ["eye_l_mesh1"], ["eye_r_mesh1"]]
        mock_cmds.rename.side_effect = ["head_lod0_mesh_output", "eye_l_mesh_output", "eye_r_mesh_output"]
        mock_cmds.fileDialog2.return_value = None  # 用户取消对话框

        # 调用被测试的函数
        export_utils.duplicate_and_export_head_mesh()

        # 验证函数调用
        mock_cmds.file.assert_not_called()  # 不应调用导出函数
        mock_cmds.delete.assert_called_once_with(
            ["head_lod0_mesh_output", "eye_l_mesh_output", "eye_r_mesh_output"],
        )

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    @mock.patch("cgame_avatar_factory.api.export_utils.dna.TemplateDna.get_mesh_name")
    def test_duplicate_and_export_head_mesh_add_extension(self, mock_get_mesh_name, mock_cmds):
        """Test adding .fbx extension if missing."""
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh"]
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_lod0_mesh1"]
        mock_cmds.parent.return_value = ["head_lod0_mesh1"]
        mock_cmds.rename.return_value = "head_lod0_mesh_output"
        mock_cmds.fileDialog2.return_value = ["/path/to/export"]  # 没有扩展名

        # 调用被测试的函数
        export_utils.duplicate_and_export_head_mesh()

        # 验证函数调用
        mock_cmds.file.assert_called_once_with(
            "/path/to/export.fbx",  # 应该添加了.fbx扩展名
            force=True,
            exportSelected=True,
            type="FBX export",
        )

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    @mock.patch("cgame_avatar_factory.api.export_utils.dna.TemplateDna.get_mesh_name")
    def test_duplicate_and_export_head_mesh_mesh_not_found(self, mock_get_mesh_name, mock_cmds):
        """Test error when mesh is not found."""
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh", "missing_mesh"]
        mock_cmds.objExists.side_effect = lambda mesh: mesh != "missing_mesh"

        # 验证函数抛出异常
        with self.assertRaises(RuntimeError) as context:
            export_utils.duplicate_and_export_head_mesh()

        self.assertIn("not found", str(context.exception))
        mock_cmds.duplicate.assert_not_called()  # 不应调用复制函数

    @mock.patch("cgame_avatar_factory.api.export_utils.cmds")
    @mock.patch("cgame_avatar_factory.api.export_utils.dna.TemplateDna.get_mesh_name")
    def test_duplicate_and_export_head_mesh_digit_suffix(self, mock_get_mesh_name, mock_cmds):
        """Test handling of mesh names with digit suffixes."""
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh2"]  # 名称以数字结尾
        mock_cmds.objExists.return_value = True
        mock_cmds.duplicate.return_value = ["head_lod0_mesh21"]
        mock_cmds.parent.return_value = ["head_lod0_mesh21"]
        mock_cmds.rename.return_value = "head_lod0_mesh_output"
        mock_cmds.fileDialog2.return_value = ["/path/to/export.fbx"]

        # 调用被测试的函数
        export_utils.duplicate_and_export_head_mesh()

        # 验证函数调用
        mock_cmds.rename.assert_called_once_with("head_lod0_mesh21", "head_lod0_mesh_output")


if __name__ == "__main__":
    unittest.main()
