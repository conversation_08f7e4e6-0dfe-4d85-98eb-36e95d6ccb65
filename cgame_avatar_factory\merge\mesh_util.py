# Import built-in modules
import logging
import os

# Import third-party modules
import maya.OpenMaya as om
import maya.cmds as cmds
import pymel.core as pm

# Import local modules
from cgame_avatar_factory import utils
from cgame_avatar_factory.api import import_utils
import cgame_avatar_factory.constants as const


def get_mesh_points(input_mesh, object_space=True):
    """Get vertex positions from mesh

    Args:
        input_mesh: Name of mesh to get points from
        object_space: Whether to get points in object space (default: True)

    Returns:
        list: List of vertex positions
    """
    vertex_count = cmds.polyEvaluate(input_mesh, vertex=True)
    points = [
        cmds.xform(
            "{0}.vtx[{1}]".format(input_mesh, i),
            q=1,
            translation=1,
            relative=True,
            objectSpace=object_space,
        )
        for i in range(vertex_count)
    ]
    return points


def set_mesh_points(input_mesh, pnts):
    """Set vertex positions for mesh

    Args:
        input_mesh: Name of mesh to set points on
        pnts: List of new vertex positions
    """
    sel = om.MSelectionList()
    sel.add(input_mesh)

    dag_path = om.MDagPath()
    sel.getDagPath(0, dag_path)
    mf_mesh = om.MFnMesh(dag_path)

    positions = om.MPointArray()
    positions.setLength(len(pnts))

    for index in range(len(pnts)):
        positions.set(om.MPoint(pnts[index][0], pnts[index][1], pnts[index][2]), index)

    mf_mesh.setPoints(positions, om.MSpace.kObject)


def get_dna_mesh_pnts(calibrated, mesh_index):
    """Get vertex positions from DNA mesh

    Args:
        calibrated: Calibrated DNA reader
        mesh_index: Index of mesh to get points from

    Returns:
        list: List of vertex positions
    """
    xs_a = calibrated.getVertexPositionXs(mesh_index)
    ys_a = calibrated.getVertexPositionYs(mesh_index)
    zs_a = calibrated.getVertexPositionZs(mesh_index)

    pos_a = [[xs_a[idx], ys_a[idx], zs_a[idx]] for idx in range(len(xs_a))]

    return pos_a


def update_deltas_for_dna(calibrated, mesh_index, mesh_name):
    """Update DNA mesh deltas

    Args:
        calibrated: Calibrated DNA reader
        mesh_index: Index of mesh to update
        mesh_name: Name of mesh to update
    """
    src_pnts = get_dna_mesh_pnts(calibrated, mesh_index)

    merge_points = get_mesh_points(mesh_name)

    deltas_final = [[tar[0] - src[0], tar[1] - src[1], tar[2] - src[2]] for src, tar in zip(src_pnts, merge_points)]

    return deltas_final


def getSkinCluster(obj):
    """Get skinCluster node for object

    Args:
        obj: Object to get skinCluster for

    Returns:
        pyNode: SkinCluster node if found, None otherwise
    """
    skinCluster = None

    if isinstance(obj, str):
        obj = pm.PyNode(obj)
    try:
        if pm.nodeType(obj.getShape()) in ["mesh", "nurbsSurface", "nurbsCurve"]:
            for shape in obj.getShapes():
                try:
                    for skC in pm.listHistory(shape, type="skinCluster"):
                        try:
                            if skC.getGeometry()[0] == shape:
                                skinCluster = skC
                        except Exception:
                            pass
                except Exception:
                    pass
    except Exception:
        pm.displayWarning("%s: is not supported." % obj.name())

    return skinCluster


def get_skin_weights_and_joints(mesh, alpha=None):
    """Get skin weights and joints for mesh

    Args:
        mesh: Mesh to get weights from
        alpha: Alpha value for weight filtering

    Returns:
        tuple: Dictionary of weights and list of joints
    """
    logger = logging.getLogger(__name__)
    alpha = alpha or 0.9
    if not cmds.objExists(mesh):
        cmds.error("there is no mesh = {}".format(mesh))

    skin_cluster = getSkinCluster(mesh)

    skin_cluster = str(skin_cluster)
    if not cmds.objExists(skin_cluster):
        cmds.error("there is no skin_cluster = {0}, mesh={1}".format(skin_cluster, mesh))

    logger.info(skin_cluster)

    num_vertices = cmds.polyEvaluate(mesh, vertex=True)

    num_vertices = int(num_vertices)
    logger.info(f"num_vertices = {num_vertices}")
    jnt_vertex_map = {}
    for i in range(num_vertices):
        vertex_name = "{}.vtx[{}]".format(mesh, i)
        weights = cmds.skinPercent(skin_cluster, vertex_name, query=True, value=True)
        joints = cmds.skinPercent(skin_cluster, vertex_name, query=True, transform=None)

        for joint, weight in zip(joints, weights):
            if weight > alpha:
                if joint not in jnt_vertex_map.keys():
                    jnt_vertex_map[joint] = set()
                jnt_vertex_map[joint].add(i)

    return jnt_vertex_map


def rivet_constraint_jnt(skin_mesh, rivet_mesh, jnt_ns="proxy"):
    """Create rivet constraints for joints

    Args:
        skin_mesh: Source skinned mesh
        rivet_mesh: Target mesh for rivets
        jnt_ns: Joint namespace (default: "proxy")
    """
    jnt_vertex = get_skin_weights_and_joints(skin_mesh, 0.5)

    for jnt, vertex_list in jnt_vertex.items():
        # NOTE: only constraint facial jnt
        if "FACIAL_" in jnt:
            vert_datas = ["{0}.vtx[{1}]".format(rivet_mesh, vert) for vert in vertex_list]
            cmds.select(cl=1)
            # NOTE: select face points

            cmds.select(vert_datas)

            # NOTE: create rivet
            pm.mel.eval("Rivet;")
            uvPinOut = cmds.ls(sl=True)
            uvPinOut.pop(0)

            ns_jnt = "{0}:{1}".format(jnt_ns, jnt)
            cmds.parentConstraint(uvPinOut, ns_jnt, sr=["x", "y", "z"], mo=1)

    locators = cmds.ls(type="locator")
    # grp = cmds.group(locators)
    cmds.hide(locators)


def duplicate_jnt(root="root", proxy_ns="proxy"):
    """Duplicate joint hierarchy

    Args:
        root: Root joint name (default: "root")
        proxy_ns: Namespace for duplicates (default: "proxy")
    """
    all_jnts = cmds.listRelatives(root, ad=1, type="joint")
    all_jnts.append(root)

    tmp = "tmp_ns"
    if cmds.namespace(exists=tmp):
        try:
            cmds.namespace(rm=tmp)
        except:
            pm.displayError("already exsits old= {} namespace".format(tmp))
    cmds.namespace(add=tmp)
    for jnt in all_jnts:
        cmds.rename(jnt, ":{0}:{1}".format(tmp, str(jnt)))

    root = "{0}:{1}".format(tmp, root)
    all_jnts = cmds.listRelatives(root, ad=1, type="joint")
    all_jnts.append(root)

    dupli_jnts = cmds.duplicate(root)
    # NOTE: if proxy_ns exists, delete it first
    if cmds.namespace(exists=proxy_ns):
        try:
            cmds.namespace(rm=proxy_ns, f=1)
        except Exception as err:
            logger = logging.getLogger(__name__)
            logger.error(err)
            pm.displayError("delete namespace failed!")

    cmds.namespace(add=proxy_ns)
    for jnt in dupli_jnts:
        cmds.rename(jnt, ":{0}:{1}".format(proxy_ns, jnt))

    for jnt in all_jnts:
        cmds.rename(jnt, jnt.split(":")[-1])
    cmds.namespace(rm=tmp)


def get_uv_name_by_id(mesh_name, uv_id):
    """Get UV name from ID

    Args:
        mesh_name: Name of mesh
        uv_id: UV index

    Returns:
        str: UV name
    """
    return "{}.map[{}]".format(mesh_name, uv_id)


def get_vtx_name_by_id(mesh_name, vertex_id):
    """Get vertex name from ID

    Args:
        mesh_name: Name of mesh
        vertex_id: Vertex index

    Returns:
        str: Vertex name
    """
    return "{}.vtx[{}]".format(mesh_name, vertex_id)


def get_vtx_id_by_uv_id(mesh_name, uv_id):
    """Get vertex ID from UV ID

    Args:
        mesh_name: Name of mesh
        uv_id: UV index

    Returns:
        int: Vertex index
    """
    uv_name = get_uv_name_by_id(mesh_name, uv_id)
    vtx_names = cmds.polyListComponentConversion(uv_name, fromUV=True, toVertex=True)
    vtx_name = cmds.ls(vtx_names, flatten=True)[0]
    vtx_id = utils.get_number_after_pattern(vtx_name, r"\[")
    return vtx_id


def get_all_meshes():
    """Get all mesh nodes in scene

    Returns:
        list: List of mesh names
    """
    mesh_list = cmds.ls(type="mesh")
    mesh_name_list = [cmds.listRelatives(mesh, parent=True)[0] for mesh in mesh_list]
    return mesh_name_list


def find_mesh_lod_grp():
    """Find LOD groups for meshes

    Returns:
        dict: Dictionary mapping LOD levels to meshes
    """
    lod_grps = cmds.ls(type="lodGroup")
    if (not lod_grps) or len(lod_grps) == 0:
        cmds.error("find none lod group!")
    grp_node = lod_grps[0]
    groups = cmds.listRelatives(grp_node, type="transform")

    lod_idx = 0
    lod_mesh_map = {}
    for grp in groups:
        if not cmds.objExists(grp):
            cmds.warning(f"grp = {grp} not exist!")
            continue
        meshes = cmds.listRelatives(grp, type="transform", ad=1)

        lod_mesh_map[lod_idx] = meshes
        lod_idx += 1

    return lod_mesh_map


def get_mesh_uv_count(mesh_name):
    """Get UV count for mesh

    Args:
        mesh_name: Name of mesh

    Returns:
        int: Number of UVs
    """
    uv_count = cmds.polyEvaluate(mesh_name, uv=True)
    return uv_count


def create_mesh_fn(mesh_name):
    """Create MFnMesh function set for mesh

    Args:
        mesh_name: Name of mesh

    Returns:
        MFnMesh: Function set for mesh
    """
    sel_list = om.MSelectionList()
    sel_list.add(mesh_name)

    dag_path = om.MDagPath()
    sel_list.getDagPath(0, dag_path)

    mesh_fn = om.MFnMesh(dag_path)
    return mesh_fn, dag_path


def get_mesh_normals(mesh_name):
    """Get mesh normal vectors

    Args:
        mesh_name: Name of mesh

    Returns:
        list: List of normal vectors [x,y,z]
    """
    mesh_fn, _ = create_mesh_fn(mesh_name)
    point_array = om.MPointArray()
    normal = om.MVector()
    normals = [[mesh_fn.getVertexNormal(i, normal, om.MSpace.kObject)] for i in range(point_array.length())]
    return normals


def get_mesh_uvs(mesh_name):
    """Get mesh UV coordinates

    Args:
        mesh_name: Name of mesh

    Returns:
        list: List of UV coordinates [u,v]
    """
    mesh_fn, _ = create_mesh_fn(mesh_name)
    u_array = om.MFloatArray()
    v_array = om.MFloatArray()
    mesh_fn.getUVs(u_array, v_array)
    uvs = [[u_array[i], v_array[i]] for i in range(u_array.length())]
    return uvs


def build_face_layout(mesh_name):
    """Build face-to-UV mapping

    Args:
        mesh_name: Name of mesh

    Returns:
        list: List of UV indices per face
    """
    _, dag_path = create_mesh_fn(mesh_name)

    uv_ids_list = []
    face_itr = om.MItMeshPolygon(dag_path)
    while not face_itr.isDone():
        face_uv_ids = []
        for i in range(face_itr.polygonVertexCount()):
            uv_id = om.MScriptUtil()
            uv_id.createFromInt(0)
            uv_id_ptr = uv_id.asIntPtr()
            face_itr.getUVIndex(i, uv_id_ptr)
            face_uv_ids.append(om.MScriptUtil(uv_id_ptr).asInt())
        uv_ids_list.append(face_uv_ids)
        face_itr.next()

    return uv_ids_list


def build_vertex_layout(mesh_name):
    """Build vertex layout data

    Args:
        mesh_name: Name of mesh

    Returns:
        list: List of [position_id, uv_id, normal_id] per vertex
    """
    # TODO: in original vertex layout, each vertex has only one normal, but a vertex may have multiple normals.
    position_id_list = []
    normal_id_list = []

    uv_count = get_mesh_uv_count(mesh_name)
    for uv_id in range(uv_count):
        vtx_id = get_vtx_id_by_uv_id(mesh_name, uv_id)
        position_id_list.append(vtx_id)
        normal_id_list.append(vtx_id)

    layouts = [[position_id_list[uv_id], uv_id, normal_id_list[uv_id]] for uv_id in range(uv_count)]
    return layouts


def move_objects_to_namespace_from_root(namespace_name):
    """Move objects from root to namespace

    Args:
        namespace_name: Target namespace
    """
    cmds.namespace(add=namespace_name)
    objects_without_namespace = sorted(
        [obj for obj in cmds.ls(long=True, transforms=True) if ":" not in obj],
        key=lambda x: x.count("|"),
        reverse=True,
    )
    # Skip cameras
    default_cameras = ["persp", "top", "front", "side"]
    for obj in objects_without_namespace:
        if obj.split("|")[-1] not in default_cameras:
            cmds.rename(obj, "{}:{}".format(namespace_name, obj))


def create_name_space(namespace_name):
    """Create new namespace

    Args:
        namespace_name: Name of namespace to create
    """
    if not cmds.namespace(exists=namespace_name):
        cmds.namespace(add=namespace_name)


def set_namespace(namespace_name):
    """Set current namespace

    Args:
        namespace_name: Namespace to set as current
    """
    if cmds.namespace(exists=namespace_name):
        cmds.namespace(setNamespace=namespace_name)


def separate_objects_in_x(distance=40):
    """Separate objects along X axis

    Args:
        distance: Distance between objects (default: 40)
    """
    root_objects = cmds.ls(assemblies=True)
    for i, obj in enumerate(root_objects):
        cmds.move(i * distance, 0, 0, obj, relative=True)


def collect_objects_to_origin():
    """Move all objects to origin"""
    root_objects = cmds.ls(assemblies=True)
    for obj in root_objects:
        cmds.move(0, 0, 0, obj, absolute=True)


def select_objects_by_namespace(namespace_name):
    """Select all objects in namespace

    Args:
        namespace_name: Namespace to select objects from
    """
    all_objects_in_namespace = cmds.ls(namespace_name + ":*", long=True, recursive=True)
    if all_objects_in_namespace:
        cmds.select(all_objects_in_namespace, replace=True)


def focus_from_front():
    """Focus view on all meshes from front view"""
    cmds.viewSet(front=True)
    meshes = cmds.ls(type="mesh")
    if meshes:
        cmds.select(meshes)
        cmds.viewFit()
        cmds.select(clear=True)
        cmds.refresh()


def save_maya_scene(file_name):
    """Save Maya scene to file

    Args:
        file_name: Path to save scene to
    """
    current_file_path = cmds.file(query=True, sceneName=True)
    folder_path = os.path.dirname(current_file_path)

    new_file_path = os.path.join(folder_path, file_name + ".mb")

    cmds.file(rename=new_file_path)
    cmds.file(save=True)


def new_maya_scene():
    """Create new empty Maya scene"""
    cmds.file(force=True, new=True)


def create_light(intensity, rotate_Y):
    """Create directional light

    Args:
        intensity: Light intensity
        rotate_Y: Y-axis rotation
    """
    light = cmds.directionalLight(intensity=intensity)
    cmds.rotate(0, rotate_Y, 0, light)


def set_head_layer_unselectable(layout, index):
    if cmds.objExists(layout):
        cmds.setAttr(f"{layout}.displayType", index)


def create_layout(selected_groups):
    cmds.select(clear=True)
    if selected_groups:
        for group in selected_groups:
            cmds.select(group, add=True)

        if cmds.objExists(const.CONTROLLER_LAYER_NAME):
            cmds.editDisplayLayerMembers(const.CONTROLLER_LAYER_NAME, cmds.ls(selection=True))
        else:
            cmds.createDisplayLayer(name=const.CONTROLLER_LAYER_NAME, number=1, nr=True)

        cmds.select(clear=True)


def focus_camera_on_head():
    """Frame camera view on head mesh."""
    if cmds.objExists(const.BASE_HEAD_MESH_NAME):
        cmds.select(const.BASE_HEAD_MESH_NAME, replace=True)
        cmds.viewFit(animate=True)


def backup_head_mesh():
    """Create a backup of the head model and set it to invisible"""
    cmds.duplicate(const.BASE_HEAD_MESH_NAME, name=const.BAK_HEAD_MESH_NAME)
    cmds.setAttr(f"{const.BAK_HEAD_MESH_NAME}.visibility", 0)


def get_maya_workspace():
    maya_workspace = cmds.workspace(query=True, rootDirectory=True)
    return maya_workspace


def create_workspace_dna_dir(dna_name) -> str:
    """Create output path for DNA file

    Creates data directory if needed and returns path
    for saving DNA file.

    Returns:
        str: Output path for DNA file
    """
    maya_workspace = get_maya_workspace()
    data_dir = os.path.join(maya_workspace, "data")

    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    output_path = os.path.join(data_dir, dna_name + ".dna")
    return output_path


def load_lods_model():
    # Try to load lods.fbx with priority:
    # 1. PROJECT_AVATAR_FACTORY_PATH\config directory
    # 2. PUBLIC_AVATAR_FACTORY_PATH\config directory (if IS_METAHUMAN_SPEC is True)
    # 3. RESOURCE_ROOT directory (if IS_METAHUMAN_SPEC is True and public path doesn't exist)

    project_lods_path = os.path.join(const.CONFIG_PATH, "lods.fbx")
    public_lods_path = os.path.join(const.PUBLIC_AVATAR_FACTORY_PATH, "config", "lods.fbx")

    lods_path = None
    if os.path.exists(project_lods_path):
        lods_path = project_lods_path
    elif const.IS_METAHUMAN_SPEC and os.path.exists(public_lods_path):
        lods_path = public_lods_path
    elif const.IS_METAHUMAN_SPEC:
        lods_path = os.path.join(const.RESOURCE_ROOT, "lods.fbx")

    if lods_path:
        import_utils.import_fbx(lods_path)

    cmds.makeIdentity(const.HEAD_GRP, apply=True, translate=True, rotate=True, scale=True, jointOrient=True)


def delete_node(node_name):
    if cmds.objExists(node_name):
        cmds.delete(node_name)


def delete_skin_cluster(mesh_name):
    if not cmds.objExists(mesh_name):
        return False
    history = cmds.listHistory(mesh_name, pruneDagObjects=True) or []
    skin_clusters = cmds.ls(history, type="skinCluster") or []

    if not skin_clusters:
        return False

    for skin_cluster in skin_clusters:
        try:
            cmds.delete(skin_cluster)
        except Exception as e:
            return False
    return True


def get_joint_world_position(joint_name: str):
    """Get world space position of a joint.

    Args:
        joint_name: Name of joint to query

    Returns:
        list: World space position [x,y,z]
    """
    return cmds.xform(joint_name, query=True, worldSpace=True, translation=True)


def get_mesh_data(mesh_name):
    if not cmds.objExists(mesh_name):
        return

    try:
        selection_list = om.MSelectionList()
        selection_list.add(mesh_name)

        dag_path = om.MDagPath()
        selection_list.getDagPath(0, dag_path)

        mesh_fn = om.MFnMesh(dag_path)

        points_array = om.MPointArray()
        mesh_fn.getPoints(points_array, om.MSpace.kWorld)

        vertex_positions = []
        for i in range(points_array.length()):
            point = points_array[i]
            vertex_positions.append([point.x, point.y, point.z])

        return vertex_positions
    except Exception as e:
        logging.error(
            f"获取网格数据失败 - {mesh_name}: {str(e)}"
            if utils.is_cn()
            else f"Failed to get mesh data - {mesh_name}: {str(e)}",
        )
        return None


def set_joint_position(joint_name, translate, rotate):
    x, y, z = translate
    cmds.setAttr(f"{joint_name}{const.ATTR_LIST[0]}", x)
    cmds.setAttr(f"{joint_name}{const.ATTR_LIST[1]}", y)
    cmds.setAttr(f"{joint_name}{const.ATTR_LIST[2]}", z)
    rx, ry, rz = rotate
    cmds.setAttr(f"{joint_name}{const.ORIENT_ATTR_LIST[0]}", rx)
    cmds.setAttr(f"{joint_name}{const.ORIENT_ATTR_LIST[1]}", ry)
    cmds.setAttr(f"{joint_name}{const.ORIENT_ATTR_LIST[2]}", rz)


def disconnect_joint_attr(joint_count, reader):
    for joint_index in range(joint_count):
        joint_name = reader.getJointName(joint_index)
        if not cmds.objExists(joint_name):
            logging.warning(f"Joint {joint_name} does not exist, skipping connection")
            continue

        for attr in const.ATTR_LIST_S_ZOOM:
            attr_path = f"{joint_name}{attr}"
            if cmds.connectionInfo(attr_path, isDestination=True):
                source_attr = cmds.connectionInfo(attr_path, sourceFromDestination=True)
                cmds.disconnectAttr(source_attr, attr_path)
