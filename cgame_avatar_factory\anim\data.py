# Import built-in modules
import logging

# Import third-party modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.constants as const


class PreviewAnimConfig:
    def __init__(self) -> None:
        self.logger = logging.getLogger(__name__)

        self.data = {}

        anim = AnimData("Base", [-1, 0])
        self.data[anim.show_text] = anim

        anim = AnimData("Smile", [100, 150])
        self.data[anim.show_text] = anim

        anim = AnimData("Angry", [150, 200])
        self.data[anim.show_text] = anim

        anim = AnimData("Disgust", [200, 250])
        self.data[anim.show_text] = anim


class AnimData:
    def __init__(self, text="Smile", time=[0, 100]):
        # TODO: convert to chinese
        self.show_text = text

        # TODO: support user anim file
        self.local_path = ""

        self._time_range = time
        self.logger = logging.getLogger(__name__)

    def __repr__(self):
        return "AnimData(show_text={}, time_range={}, , local_path={})".format(
            self.show_text,
            self.time_range,
            self.local_path,
        )

    @property
    def time_range(self):
        return self._time_range

    @time_range.setter
    def time_range(self, data):
        assert data and len(data) == 2
        self._time_range = data

    @time_range.getter
    def time_range(self):
        return self._time_range

    def get_head_name(self):
        return const.LOD_MESH_MAP[const.CURRENT_LOD][0]

    def get_perspective_panel(self):
        all_panels = cmds.getPanel(type="modelPanel")
        for panel in all_panels:
            if cmds.modelPanel(panel, query=True, camera=True) == "persp":
                return panel
        return None

    def play(self, panel="persp", fit_view_mesh="eyesSetup_grp"):
        if fit_view_mesh is None:
            fit_view_mesh = self.get_head_name()
        if not cmds.objExists(fit_view_mesh):
            self.logger.warning("mesh = {} is not exist!".format(fit_view_mesh))
            return
        # NOTE: set joint in visiable.
        _panel = self.get_perspective_panel()
        cmds.modelEditor(_panel, edit=True, joints=False)

        # NOTE: fit model view.
        cmds.select(fit_view_mesh, replace=True)
        cmds.viewFit(panel, animate=True)
        cmds.select(cl=1)

        # NOTE: stop old animation
        cmds.play(state=False)

        if self._time_range[0] == -1:
            cmds.currentTime(-1)
            return

        cmds.playbackOptions(minTime=self._time_range[0], maxTime=self._time_range[1])

        cmds.play()
