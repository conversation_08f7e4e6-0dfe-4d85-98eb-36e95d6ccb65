# Import third-party modules
from dayu_widgets import MAvatar
from dayu_widgets import <PERSON><PERSON>abe<PERSON>
from dayu_widgets import <PERSON>rog<PERSON>Bar
from dayu_widgets.qt import MPixmap
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.ui.mixin.mouse_hover_mixin import hover_background_color_mixin


class DNAProgressBar(MProgressBar):
    sig_progress_bar_clicked = QtCore.Signal()

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.sig_progress_bar_clicked.emit()
        else:
            super(DNAProgressBar, self).mousePressEvent(event)


@hover_background_color_mixin
class ProgressWidget(QtWidgets.QFrame):
    sig_progress_bar_clicked = QtCore.Signal()

    def __init__(self, parent=None):
        super(ProgressWidget, self).__init__(parent=parent)
        self.setObjectName("ProgressWidget")
        self.setup_ui()

    def setup_ui(self):
        self.setup_layout()
        self.setup_widgets(self.main_layout)

    def setup_layout(self):
        self.main_layout = QtWidgets.QHBoxLayout()
        self.main_layout.setMargin(0)
        self.setLayout(self.main_layout)

    def setup_widgets(self, parent_layout):
        self.setup_progress_bar(parent_layout, 100)
        self.setup_success_avatar(parent_layout)
        self.setup_success_label(parent_layout)

    def setup_progress_bar(self, parent_layout, stretch=0):
        self.progress_bar = DNAProgressBar().success()
        self.progress_bar.setValue(0)
        self.progress_bar.sig_progress_bar_clicked.connect(self.sig_progress_bar_clicked)
        parent_layout.addWidget(self.progress_bar, stretch=stretch)
        parent_layout.setStretchFactor(self.progress_bar, 100)

    def setup_success_avatar(self, parent_layout, stretch=0):
        self.success_avatar = MAvatar.tiny(MPixmap("success.svg", const.DAYU_SUCCESS_COLOR))
        parent_layout.addWidget(self.success_avatar, stretch=stretch)

    def setup_success_label(self, parent_layout, stretch=0):
        self.success_label = MLabel("0")
        parent_layout.addWidget(self.success_label, stretch=stretch)
