"""Editor Area Module.

This module provides the editor area widget for the Hair Studio tool.
It allows users to view and edit the properties of the selected hair component.
"""

# Import standard library
import os
import logging

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import dayu widgets
from dayu_widgets import (
    MLabel,
    MLineEdit,
    MSpinBox,
    MDoubleSpinBox,
    MComboBox,
    MCheckBox,
)

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.constants import (
    UI_TEXT_NO_COMPONENT_SELECTED,
    HAIR_TYPE_CARD,
    HAIR_TYPE_XGEN,
    HAIR_TYPE_CURVE,
    LAYOUT_MARGIN_ZERO,
    LAYOUT_SPACING_SMALL,
    DEFAULT_COMPONENT_WIDTH,
    DEFAULT_COMPONENT_HEIGHT,
    DEFAULT_XGEN_DENSITY,
    DEFAULT_XGEN_LENGTH,
    DEFAULT_CURVE_THICKNESS,
    DEFAULT_CURVE_SUBDIVISIONS,
    MIN_COMPONENT_SIZE,
    MAX_COMPONENT_SIZE,
    MIN_XGEN_DENSITY,
    MAX_XGEN_DENSITY,
    MIN_CURVE_THICKNESS,
    MAX_CURVE_THICKNESS,
    MIN_CURVE_SUBDIVISIONS,
    MAX_CURVE_SUBDIVISIONS,
    MAX_CLEAR_ITERATIONS,
    MAX_RECURSION_DEPTH,
    PROPERTY_NAME,
    PROPERTY_VISIBLE,
    PROPERTY_WIDTH,
    PROPERTY_HEIGHT,
    PROPERTY_DENSITY,
    PROPERTY_LENGTH,
    PROPERTY_THICKNESS,
    PROPERTY_SUBDIVISIONS,
    FORM_LABEL_NAME,
    FORM_LABEL_VISIBLE,
    FORM_LABEL_WIDTH,
    FORM_LABEL_HEIGHT,
    FORM_LABEL_DENSITY,
    FORM_LABEL_LENGTH,
    FORM_LABEL_THICKNESS,
    FORM_LABEL_SUBDIVISIONS,
)


class EditorArea(QtWidgets.QScrollArea):
    """Editor Area Widget.

    This widget displays and allows editing of the properties of the selected hair component.
    It shows different properties based on the type of hair component selected.
    """

    def __init__(self, hair_type, hair_manager=None, parent=None, logger=None):
        """Initialize the EditorArea.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(EditorArea, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}EditorArea".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Initialize manager
        self.manager = (
            hair_manager
            if hair_manager is not None
            else HairManager(logger=self._logger)
        )

        # Current component data
        self.current_component = None

        # Initialize UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface components."""
        # Create container widget
        container = QtWidgets.QWidget()
        self.setWidget(container)
        self.setWidgetResizable(True)

        # Main layout
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
        )
        main_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Title
        self.title_label = MLabel("毛发编辑区")
        self.title_label.setProperty("h2", True)
        main_layout.addWidget(self.title_label)

        # Add separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        main_layout.addWidget(separator)

        # Form layout for properties
        self.form_layout = QtWidgets.QFormLayout()
        self.form_layout.setLabelAlignment(
            QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter
        )
        self.form_layout.setFormAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignTop)
        self.form_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        self.form_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Add form layout to scroll area
        main_layout.addLayout(self.form_layout)

        # Add stretch to push content to the top
        main_layout.addStretch()

        # Set initial state
        self.set_component(None)

    def set_component(self, component_data):
        """Set the current component to edit.

        Args:
            component_data (dict): Dictionary containing component data
        """
        self.current_component = component_data

        # Clear existing widgets
        self._clear_form()

        if not component_data:
            self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)
            return

        # Update title
        self.title_label.setText(component_data.get("name", "Unnamed Component"))

        # Add common properties
        self._add_common_properties(component_data)

        # Add type-specific properties
        if self.hair_type == HAIR_TYPE_CARD:
            self._add_card_properties(component_data)
        elif self.hair_type == HAIR_TYPE_XGEN:
            self._add_xgen_properties(component_data)
        elif self.hair_type == HAIR_TYPE_CURVE:
            self._add_curve_properties(component_data)

    def clear_component(self):
        """Clear the current component and reset the editor area."""
        self.current_component = None

        # Clear existing widgets
        self._clear_form()

        # Update title
        self.title_label.setText(UI_TEXT_NO_COMPONENT_SELECTED)

    def _clear_form(self):
        """Clear all widgets from the form layout."""
        try:
            # Add safety counter to prevent infinite loops
            max_iterations = MAX_CLEAR_ITERATIONS
            iteration = 0

            while self.form_layout.count() > 0 and iteration < max_iterations:
                item = self.form_layout.takeAt(0)
                if item is None:
                    break

                if item.widget():
                    item.widget().deleteLater()
                elif item.layout():
                    self._clear_layout(item.layout())

                iteration += 1

            if iteration >= max_iterations:
                print(
                    f"Warning: _clear_form reached maximum iterations ({max_iterations})"
                )

        except Exception as e:
            print(f"Error in _clear_form: {e}")

    def _clear_layout(self, layout):
        """Recursively clear a layout and its widgets."""
        try:
            if layout is None:
                return

            # Add safety counter to prevent infinite loops
            max_iterations = MAX_CLEAR_ITERATIONS
            iteration = 0

            while layout.count() > 0 and iteration < max_iterations:
                item = layout.takeAt(0)
                if item is None:
                    break

                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                elif item.layout():
                    # Prevent deep recursion
                    if iteration < MAX_RECURSION_DEPTH:  # Limit recursion depth
                        self._clear_layout(item.layout())

                iteration += 1

            if iteration >= max_iterations:
                print(
                    f"Warning: _clear_layout reached maximum iterations ({max_iterations})"
                )

        except Exception as e:
            print(f"Error in _clear_layout: {e}")

    def _add_common_properties(self, component_data):
        """Add common properties to the form."""
        # Name field
        name_edit = MLineEdit()
        name_edit.setText(component_data.get(PROPERTY_NAME, ""))
        name_edit.textChanged.connect(
            lambda name: self._on_property_changed(PROPERTY_NAME, name)
        )
        self.form_layout.addRow(FORM_LABEL_NAME, name_edit)

        # Visible checkbox
        visible_check = MCheckBox()
        visible_check.setChecked(component_data.get(PROPERTY_VISIBLE, True))
        visible_check.stateChanged.connect(
            lambda state: self._on_property_changed(PROPERTY_VISIBLE, state > 0)
        )
        self.form_layout.addRow(FORM_LABEL_VISIBLE, visible_check)

        # Add separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.form_layout.addRow(separator)

    def _add_card_properties(self, component_data):
        """Add card-specific properties to the form."""
        # Width
        width_spin = MDoubleSpinBox()
        width_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        width_spin.setValue(component_data.get(PROPERTY_WIDTH, DEFAULT_COMPONENT_WIDTH))
        width_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_WIDTH, value)
        )
        self.form_layout.addRow(FORM_LABEL_WIDTH, width_spin)

        # Height
        height_spin = MDoubleSpinBox()
        height_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        height_spin.setValue(
            component_data.get(PROPERTY_HEIGHT, DEFAULT_COMPONENT_HEIGHT)
        )
        height_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_HEIGHT, value)
        )
        self.form_layout.addRow(FORM_LABEL_HEIGHT, height_spin)

    def _add_xgen_properties(self, component_data):
        """Add XGen-specific properties to the form."""
        # Density
        density_spin = MSpinBox()
        density_spin.setRange(MIN_XGEN_DENSITY, MAX_XGEN_DENSITY)
        density_spin.setValue(
            component_data.get(PROPERTY_DENSITY, DEFAULT_XGEN_DENSITY)
        )
        density_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_DENSITY, value)
        )
        self.form_layout.addRow(FORM_LABEL_DENSITY, density_spin)

        # Length
        length_spin = MDoubleSpinBox()
        length_spin.setRange(MIN_COMPONENT_SIZE, MAX_COMPONENT_SIZE)
        length_spin.setValue(component_data.get(PROPERTY_LENGTH, DEFAULT_XGEN_LENGTH))
        length_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_LENGTH, value)
        )
        self.form_layout.addRow(FORM_LABEL_LENGTH, length_spin)

    def _add_curve_properties(self, component_data):
        """Add curve-specific properties to the form."""
        # Thickness
        thickness_spin = MDoubleSpinBox()
        thickness_spin.setRange(MIN_CURVE_THICKNESS, MAX_CURVE_THICKNESS)
        thickness_spin.setValue(
            component_data.get(PROPERTY_THICKNESS, DEFAULT_CURVE_THICKNESS)
        )
        thickness_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_THICKNESS, value)
        )
        self.form_layout.addRow(FORM_LABEL_THICKNESS, thickness_spin)

        # Subdivisions
        subdiv_spin = MSpinBox()
        subdiv_spin.setRange(MIN_CURVE_SUBDIVISIONS, MAX_CURVE_SUBDIVISIONS)
        subdiv_spin.setValue(
            component_data.get(PROPERTY_SUBDIVISIONS, DEFAULT_CURVE_SUBDIVISIONS)
        )
        subdiv_spin.valueChanged.connect(
            lambda value: self._on_property_changed(PROPERTY_SUBDIVISIONS, value)
        )
        self.form_layout.addRow(FORM_LABEL_SUBDIVISIONS, subdiv_spin)

    def _on_property_changed(self, prop_name, prop_value):
        """Handle property changes.

        Args:
            prop_name (str): Name of the property that changed
            prop_value: New value of the property
        """
        if not self.current_component:
            return

        # Update the component data
        self.current_component[prop_name] = prop_value

        # Notify the manager about the change (if component has an ID)
        if isinstance(self.current_component, dict) and "id" in self.current_component:
            # For dictionary-based components
            component_id = self.current_component["id"]
            kwargs = {prop_name: prop_value}
            self.manager.update_component(component_id, **kwargs)
        elif hasattr(self.current_component, "id"):
            # For object-based components
            component_id = self.current_component.id
            kwargs = {prop_name: prop_value}
            self.manager.update_component(component_id, **kwargs)
