"""Hair Studio Module.

This module provides the Hair Studio tool for creating and managing hair assets.
"""


# Lazy import to avoid circular dependencies and UI dependencies
def get_hair_studio_tab():
    """Get HairStudioTab class with lazy import."""
    from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab

    return HairStudioTab


def get_hair_manager():
    """Get HairManager class with lazy import."""
    from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager

    return HairManager


# For backward compatibility
def __getattr__(name):
    if name == "HairStudioTab":
        return get_hair_studio_tab()
    elif name == "HairManager":
        return get_hair_manager()
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
