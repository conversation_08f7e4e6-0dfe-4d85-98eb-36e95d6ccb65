# Import built-in modules
import json
import logging
import os

# Import third-party modules
from qtpy import QtCore

# Import local modules
import cgame_avatar_factory.constants as const

PRESET_TAGS = ["风格/写实", "风格/美型", "风格/BJD", "风格/卡通", "风格/二次元", "风格/其他", "性别/女性", "性别/男性"]
USER_TAGS_KEY = "user_tags"


def set_user_extra_tags(tags):
    """Store the user-defined tag list in local settings.

    Args:
        tags (list[str]): List of user-defined tags.
    """
    settings = QtCore.QSettings(
        QtCore.QSettings.IniFormat,
        QtCore.QSettings.UserScope,
        const.ORGANIZATION_NAME,
        const.PACKAGE_NAME,
    )
    settings.setValue(USER_TAGS_KEY, ",".join(tags))


def get_user_extra_tags():
    """Read the user-defined tag list.

    Returns:
        list[str]: List of user-defined tags.
    """
    settings = QtCore.QSettings(
        QtCore.QSettings.IniFormat,
        QtCore.QSettings.UserScope,
        const.ORGANIZATION_NAME,
        const.PACKAGE_NAME,
    )
    tags_str = settings.value(USER_TAGS_KEY, "")
    if tags_str:
        return tags_str.split(",")
    else:
        return []


def add_user_extra_tag(tag):
    """Add a user-defined tag to local settings (do not add if already exists).

    Args:
        tag (str): The tag to add.
    """
    tags = get_user_extra_tags()
    if tag not in tags:
        tags.append(tag)
        set_user_extra_tags(tags)


def remove_user_extra_tag(tag):
    """Remove a user-defined tag from local settings.

    Args:
        tag (str): The tag to remove.
    """
    tags = get_user_extra_tags()
    if tag in tags:
        tags.remove(tag)
        set_user_extra_tags(tags)


def get_all_tags():
    """Return the merged list of preset and user-defined tags (deduplicated).

    Returns:
        list[str]: Deduplicated merged list of all tags.
    """
    all_tags = PRESET_TAGS + get_user_extra_tags()
    seen = set()
    unique_tags = []
    for t in all_tags:
        if t and t not in seen:
            unique_tags.append(t)
            seen.add(t)
    return unique_tags


def read_role_tags(role_dir_path, character_name):
    """Read the tag list for the specified character.

    Args:
        role_dir_path (str): Path to the character folder.
        character_name (str): Character name.

    Returns:
        list[str]: List of character tags, or an empty list if reading fails.
    """
    json_path = os.path.join(role_dir_path, f"{character_name}.json")
    if os.path.exists(json_path):
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                return data.get("tags", [])
        except Exception as e:
            logging.info(f"Error reading tags for {character_name}: {e}")
            return []
    return []


def write_role_tags(role_dir_path, character_name, tags):
    """Write the tags field to the target json file. Create the file if it does not exist, or update the tags field if it does.

    Args:
        role_dir_path (str): Path to the character folder.
        character_name (str): Character name.
        tags (list[str]): List of tags to write.
    """
    json_path = os.path.join(role_dir_path, f"{character_name}.json")
    data = {}
    if os.path.exists(json_path):
        try:
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)
        except Exception as e:
            logging.info(f"Error reading {json_path}: {e}")
            data = {}
    data["tags"] = tags
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


def get_preset_tags():
    """Get the list of preset tags.

    Returns:
        list[str]: List of preset tags.
    """
    return PRESET_TAGS
