# -*- coding: utf-8 -*-
"""Face area selection and layout components.

This module provides UI components for face area selection and manipulation,
including mask-based clickable buttons, area views, and control panels.
"""

# Import built-in modules
import logging
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.merge.scene_data_call import SceneCall
from cgame_avatar_factory.ui.components.factor_slider import FactorSlider
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class MaskButton(QtWidgets.QPushButton):
    """
    MaskButton is a QPushButton whose clickable area is defined by the alpha channel of a mask image.
    It supports checked/unchecked state, and can display a darkened image when checked.

    Args:
        name (str): Button name.
        mask_pixmap (QPixmap): The mask image.
        on_click (Callable, optional): Click callback.
        right_click_menu (list, optional): Right-click menu config.
        mirror_name (str, optional): Name of the mirror button.
        parent (QWidget, optional): Parent widget.
        mask_only_mode (bool): Whether to draw in mask-only mode.
    """

    _last_random_fuse_factor = 0.5
    _main_window = None

    state_changed = QtCore.Signal(str, bool)

    @classmethod
    def set_main_window(cls, main_window):
        cls._main_window = main_window

    def __init__(
        self,
        name: str,
        mask_pixmap: QtGui.QPixmap,
        on_click: Optional[Callable] = None,
        right_click_menu: Optional[List[Tuple[str, Callable]]] = None,
        mirror_name: Optional[str] = None,
        parent: Optional[QtWidgets.QWidget] = None,
        mask_only_mode: bool = True,
    ):
        super(MaskButton, self).__init__(parent)
        self.name = name
        self.original_mask = mask_pixmap
        self.on_click = on_click
        self.right_click_menu_cfg = right_click_menu
        self.mirror_name = mirror_name
        self._checked = False
        self.mask_only_mode = mask_only_mode
        self._background_size = None
        self._current_scaled_pixmap = self.original_mask
        self._current_icon_pixmap = self._current_scaled_pixmap

        self.setFlat(True)
        self.setStyleSheet("background:transparent;")
        self.setToolTip(name)

        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._on_right_click)
        self.clicked.connect(self._on_clicked)

        self._pressed = False
        self._hovered = False
        self.hover_color = QtGui.QColor(136, 198, 255, 50)
        self.checked_color = QtGui.QColor(55, 124, 187, 100)
        self.pressed_color = QtGui.QColor(36, 89, 138, 100)

    def set_background_size(self, bg_size: QtCore.QSize):
        """Set the background size and scale the mask accordingly.

        Args:
            bg_size (QSize): The size of the background image.
        """
        if self._background_size == bg_size:
            return
        self._background_size = bg_size
        self._fit_mask_to_background()
        if not self.mask_only_mode:
            self._update_icon()
            self.setIcon(QtGui.QIcon(self._current_icon_pixmap))
            self.setIconSize(self._current_icon_pixmap.size())
        else:
            self.setIcon(QtGui.QIcon())
        self.setFixedSize(self._current_scaled_pixmap.size())
        self._update_mask(self._current_scaled_pixmap)

    def _fit_mask_to_background(self):
        """Scale the mask proportionally to fit the background size."""
        if self._background_size is None:
            return
        bg_w, bg_h = self._background_size.width(), self._background_size.height()
        mask_w, mask_h = self.original_mask.width(), self.original_mask.height()
        scale = min(bg_w / mask_w, bg_h / mask_h)
        new_w = int(mask_w * scale)
        new_h = int(mask_h * scale)
        scaled_mask = self.original_mask.scaled(new_w, new_h, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        # Center the mask in the background
        final_pixmap = QtGui.QPixmap(bg_w, bg_h)
        final_pixmap.fill(QtCore.Qt.transparent)
        painter = QtGui.QPainter(final_pixmap)
        x = (bg_w - new_w) // 2
        y = (bg_h - new_h) // 2
        painter.drawPixmap(x, y, scaled_mask)
        painter.end()
        self._current_scaled_pixmap = final_pixmap

    def enterEvent(self, event: QtCore.QEvent):
        self._hovered = True
        self.update()
        super().enterEvent(event)

    def leaveEvent(self, event: QtCore.QEvent):
        self._hovered = False
        self.update()
        super().leaveEvent(event)

    def mousePressEvent(self, event: QtGui.QMouseEvent):
        if self.mask_only_mode and event.button() == QtCore.Qt.LeftButton:
            self._pressed = True
            self.update()
        super(MaskButton, self).mousePressEvent(event)

    def mouseReleaseEvent(self, event: QtGui.QMouseEvent):
        if self.mask_only_mode and self._pressed:
            self._pressed = False
            self.update()
        super(MaskButton, self).mouseReleaseEvent(event)

    def resize_with_scale(self, scale_factor: float):
        """Resize the mask button according to the given scale factor.

        Args:
            scale_factor (float): The scaling factor relative to the original background size.
        """
        if self._background_size is None:
            return
        bg_w, bg_h = self._background_size.width(), self._background_size.height()
        scaled_bg_w = int(bg_w * scale_factor)
        scaled_bg_h = int(bg_h * scale_factor)
        mask_w, mask_h = self.original_mask.width(), self.original_mask.height()
        scale = min(scaled_bg_w / mask_w, scaled_bg_h / mask_h)
        new_w = int(mask_w * scale)
        new_h = int(mask_h * scale)
        scaled_mask = self.original_mask.scaled(new_w, new_h, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation)
        final_pixmap = QtGui.QPixmap(scaled_bg_w, scaled_bg_h)
        final_pixmap.fill(QtCore.Qt.transparent)
        painter = QtGui.QPainter(final_pixmap)
        x = (scaled_bg_w - new_w) // 2
        y = (scaled_bg_h - new_h) // 2
        painter.drawPixmap(x, y, scaled_mask)
        painter.end()
        self._current_scaled_pixmap = final_pixmap
        if not self.mask_only_mode:
            self._update_icon()
            self.setIconSize(final_pixmap.size())
        else:
            self.setIcon(QtGui.QIcon())
        self.setFixedSize(final_pixmap.size())
        self._update_mask(final_pixmap)

    def _update_mask(self, pixmap: QtGui.QPixmap):
        """Generate mask from alpha channel, making transparent areas unclickable."""
        image = pixmap.toImage()
        mask = image.createAlphaMask()
        self.setMask(QtGui.QRegion(QtGui.QBitmap.fromImage(mask)))

    def _update_icon(self):
        """Update the icon according to checked state."""
        if self.mask_only_mode:
            self.setIcon(QtGui.QIcon())
            return
        if self._checked:
            self._current_icon_pixmap = self._darken_pixmap(self._current_scaled_pixmap, factor=0.5)
        else:
            self._current_icon_pixmap = self._current_scaled_pixmap
        self.setIcon(QtGui.QIcon(self._current_icon_pixmap))

    @staticmethod
    def _darken_pixmap(pixmap: QtGui.QPixmap, factor: float = 0.5) -> QtGui.QPixmap:
        """Return a darkened version of the pixmap.

        Args:
            pixmap (QPixmap): The source pixmap.
            factor (float): 0~1, smaller is darker.

        Returns:
            QPixmap: The darkened pixmap.
        """
        image = pixmap.toImage().convertToFormat(QtGui.QImage.Format_ARGB32)
        painter = QtGui.QPainter()
        painter.begin(image)
        painter.setCompositionMode(QtGui.QPainter.CompositionMode_SourceAtop)
        darken_color = QtGui.QColor(0, 0, 0, int(255 * (1 - factor)))
        painter.fillRect(image.rect(), darken_color)
        painter.end()
        return QtGui.QPixmap.fromImage(image)

    def set_checked(self, checked: bool):
        """Set checked state and update icon.

        Args:
            checked (bool): Checked state.
        """
        if self._checked == checked:
            return
        self._checked = checked
        self._update_icon()
        self.state_changed.emit(self.name, self._checked)

    def is_checked(self) -> bool:
        """Return checked state."""
        return self._checked

    def toggle_checked(self):
        """Toggle checked state."""
        self.set_checked(not self._checked)

    def _on_clicked(self):
        """Handle button click event."""
        self.toggle_checked()
        if self.on_click and callable(self.on_click):
            self.on_click(self.name)
        else:
            self.default_on_click()

    def default_on_click(self):
        """Default click handler (prints button name)."""
        logging.info("Button clicked: %s", self.name)

    def _on_right_click(self, pos: QtCore.QPoint):
        """Show right-click menu if configured."""
        if not self.right_click_menu_cfg:
            return
        menu = QtWidgets.QMenu(self)
        for action_name, callback in self.right_click_menu_cfg:
            if action_name == "随机融合":
                random_menu = QtWidgets.QMenu("随机融合", menu)
                random_menu.setIcon(QtGui.QIcon(":/icons/arrow.png"))

                slider_label = MStyleMixin.instance_wrapper(dayu_widgets.MLabel("平均融合概率："))
                slider_label.foreground_color(const.DAYU_SECONDARY_TEXT_COLOR).transparent_background()
                slider_label_action = QtWidgets.QWidgetAction(menu)
                slider_label_action.setDefaultWidget(slider_label)
                slider_label.setToolTip("该值越大，融合后的脸越平均")

                slider = FactorSlider(0.01)
                slider.setRange(0, 1)
                slider.setValue(MaskButton._last_random_fuse_factor)
                slider_action = QtWidgets.QWidgetAction(menu)
                slider_action.setDefaultWidget(slider)

                button = dayu_widgets.MPushButton("开始随机融合").small()
                button_action = QtWidgets.QWidgetAction(menu)
                button_action.setDefaultWidget(button)

                slider.valueChanged.connect(self._update_random_merge_value)
                button.clicked.connect(lambda menu_ref=menu: self._execute_random_fuse(menu_ref))

                random_menu.addAction(slider_label_action)
                random_menu.addAction(slider_action)
                random_menu.addSeparator()
                random_menu.addAction(button_action)

                menu.addMenu(random_menu)
            else:
                action = menu.addAction(action_name)
                if callable(callback):
                    action.triggered.connect(lambda cb=callback, btn_name=self.name: cb(btn_name))
        menu.exec_(self.mapToGlobal(pos))

    def _update_random_merge_value(self, value: float) -> None:
        """更新随机融合值

        Args:
            value: 滑块值，范围0-1
        """
        MaskButton._last_random_fuse_factor = value
        SceneCall().set_random_merge_values(value / 100)

    def _execute_random_fuse(self, menu: Optional[QtWidgets.QMenu] = None) -> None:
        """执行随机融合操作

        执行随机融合，并更新pivot位置以反映新的融合权重

        Args:
            menu: 右键菜单引用，如果提供则会关闭菜单
        """
        if menu:
            menu.close()

        scene = SceneCall()
        scene.random_set_merge()

        self._update_pivot_position(scene)

    def _update_pivot_position(self, scene: SceneCall) -> None:
        """更新pivot位置

        根据当前区域的融合权重更新pivot位置

        Args:
            scene: SceneCall实例，用于获取当前范围和权重
        """
        if not MaskButton._main_window:
            return

        if not hasattr(MaskButton._main_window, "base_merge_page"):
            return

        base_merge_page = MaskButton._main_window.base_merge_page
        if not hasattr(base_merge_page, "pivot_move"):
            return

        current_range = scene.return_range()
        if not current_range or len(current_range) == 0:
            return

        weights = scene.return_dict()[current_range[0]]
        base_merge_page.pivot_move.set_pos_by_weight(weights)

    def paintEvent(self, event: QtGui.QPaintEvent):
        """Custom paint event for mask-only mode overlays."""
        if self.mask_only_mode and (self._checked or self._pressed or self._hovered):
            painter = QtGui.QPainter(self)
            painter.setRenderHint(QtGui.QPainter.Antialiasing)
            overlay = QtGui.QPixmap(self._current_scaled_pixmap.size())
            overlay.fill(QtCore.Qt.transparent)
            overlay_painter = QtGui.QPainter(overlay)
            if self._pressed:
                color = self.pressed_color
            elif self._checked:
                color = self.checked_color
            elif self._hovered:
                color = self.hover_color
            else:
                color = QtCore.Qt.transparent
            overlay_painter.fillRect(overlay.rect(), color)
            overlay_painter.setCompositionMode(QtGui.QPainter.CompositionMode_DestinationIn)
            overlay_painter.drawPixmap(0, 0, self._current_scaled_pixmap)
            overlay_painter.end()
            painter.drawPixmap(0, 0, overlay)
            painter.end()


class FaceAreaView(QtWidgets.QWidget):
    """
    FaceAreaView displays a background image and a set of MaskButtons on top.

    This component visualizes face areas as clickable regions on a background image,
    enabling selection of areas for merging and other operations.

    Args:
        config (dict): Configuration dictionary with keys:
            - background (str): Path to background image
            - fuse_mask_path (str, optional): Path to fusion mask image
            - buttons (list): List of button configurations
        parent (QWidget, optional): Parent widget.
        ring_widget (QWidget, optional): Ring widget for pivot_move.
    """

    button_state_changed = QtCore.Signal(str, bool)
    background_width_changed = QtCore.Signal(int)

    def __init__(
        self,
        config: dict,
        parent: Optional[QtWidgets.QWidget] = None,
        ring_widget: Optional[QtWidgets.QWidget] = None,
    ):
        super(FaceAreaView, self).__init__(parent)
        self.config = config
        self.background_pixmap_original = QtGui.QPixmap(self.config.get("background", ""))
        if self.background_pixmap_original.isNull():
            raise RuntimeError("Background image not found or invalid: {}".format(self.config.get("background")))
        self.background_original_size = self.background_pixmap_original.size()

        self.background_label = QtWidgets.QLabel(self)
        self.background_label.setScaledContents(False)
        self.background_label.setSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Ignored)
        self.background_label.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.background_label.setParent(self)

        self.fuse_mask_pixmap = None
        self.fuse_mask_label = None
        fuse_mask_path = self.config.get("fuse_mask_path", "")
        if fuse_mask_path:
            self.fuse_mask_pixmap = QtGui.QPixmap(fuse_mask_path)
            if not self.fuse_mask_pixmap.isNull():
                self.fuse_mask_label = QtWidgets.QLabel(self)
                self.fuse_mask_label.setPixmap(self.fuse_mask_pixmap)
                self.fuse_mask_label.setScaledContents(False)
                self.fuse_mask_label.setVisible(False)
                self.fuse_mask_label.setAttribute(QtCore.Qt.WA_TransparentForMouseEvents, True)

        self.buttons: List[MaskButton] = []
        self.button_dict: Dict[str, MaskButton] = {}
        self._create_buttons()

        self.mirror_mode = False
        self.fuse_mode = False

    def _create_buttons(self):
        """Create MaskButton instances from config."""
        for btn_cfg in self.config.get("buttons", []):
            mask_pixmap = QtGui.QPixmap(btn_cfg.get("mask_path", ""))
            if mask_pixmap.isNull():
                logging.warning("Warning: mask image not found for button '{}'".format(btn_cfg.get("name")))
                continue
            mask_only_mode = btn_cfg.get("mask_only_mode", True)
            btn = MaskButton(
                name=btn_cfg.get("name"),
                mask_pixmap=mask_pixmap,
                on_click=btn_cfg.get("on_click"),
                right_click_menu=btn_cfg.get("right_click_menu"),
                mirror_name=btn_cfg.get("mirror_name"),
                parent=self.background_label,
                mask_only_mode=mask_only_mode,
            )
            btn.set_background_size(self.background_original_size)
            btn.move(0, 0)
            btn.state_changed.connect(self.button_state_changed)
            self.buttons.append(btn)
            self.button_dict[btn.name] = btn

    def resizeEvent(self, event: QtGui.QResizeEvent):
        """Handle widget resize event, scale background and all buttons.

        Args:
            event (QResizeEvent): The resize event.
        """
        new_size = event.size()
        bg_w = self.background_original_size.width()
        bg_h = self.background_original_size.height()
        scale_w = new_size.width() / bg_w
        scale_h = new_size.height() / bg_h
        scale = min(scale_w, scale_h)
        new_bg_w = int(bg_w * scale)
        new_bg_h = int(bg_h * scale)
        scaled_bg = self.background_pixmap_original.scaled(
            new_bg_w,
            new_bg_h,
            QtCore.Qt.KeepAspectRatio,
            QtCore.Qt.SmoothTransformation,
        )
        self.background_label.setPixmap(scaled_bg)
        self.background_label.resize(new_bg_w, new_bg_h)
        x_offset = (new_size.width() - new_bg_w) // 2
        y_offset = (new_size.height() - new_bg_h) // 2 - 30
        self.background_label.move(x_offset, y_offset)
        for btn in self.buttons:
            btn.resize_with_scale(scale)

        if self.fuse_mask_label and self.fuse_mask_pixmap:
            scaled_fuse = self.fuse_mask_pixmap.scaled(
                new_bg_w,
                new_bg_h,
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.fuse_mask_label.setPixmap(scaled_fuse)
            self.fuse_mask_label.resize(new_bg_w, new_bg_h)
            self.fuse_mask_label.move(x_offset, y_offset)
        self.background_width_changed.emit(new_bg_w)
        super(FaceAreaView, self).resizeEvent(event)

    def set_mirror_mode(self, enabled: bool):
        """Set mirror mode.

        Args:
            enabled (bool): Whether to enable mirror mode.
        """
        self.mirror_mode = enabled

    def set_fuse_mode(self, enabled: bool):
        """Enable or disable fuse mode.

        Args:
            enabled (bool): Whether to enable fuse mode.
        """
        if self.fuse_mode == enabled:
            return
        self.fuse_mode = enabled
        if enabled:
            for btn in self.buttons:
                btn.set_checked(False)
                btn.setEnabled(False)
                btn.setVisible(False)
            if self.fuse_mask_label:
                self.fuse_mask_label.setVisible(True)
        else:
            for btn in self.buttons:
                btn.setEnabled(True)
                btn.setVisible(True)
            if self.fuse_mask_label:
                self.fuse_mask_label.setVisible(False)

    def select_all(self):
        """Select all buttons (if not in fuse mode)."""
        if self.fuse_mode:
            return
        for btn in self.buttons:
            btn.set_checked(True)

    def deselect_all(self):
        """Deselect all buttons (if not in fuse mode)."""
        if self.fuse_mode:
            return
        for btn in self.buttons:
            btn.set_checked(False)

    def get_selected_buttons(self) -> List[str]:
        """Get names of all selected buttons."""
        if self.fuse_mode:
            return []
        return [btn.name for btn in self.buttons if btn.is_checked()]

    def set_selected_buttons(self, names: List[str]):
        """Set selected buttons by name."""
        if self.fuse_mode:
            return
        for btn in self.buttons:
            btn.set_checked(btn.name in names)

    def get_button(self, name: str) -> Optional[MaskButton]:
        """Get button by name."""
        return self.button_dict.get(name)


class FaceAreaButtonPanel(QtWidgets.QWidget):
    """
    FaceAreaButtonPanel provides controls for multi-select, mirror, select all, and fuse mode.

    This panel contains UI controls for face area selection modes and operations:
    - Multi-select toggle: Enable selection of multiple face areas
    - Mirror toggle: Enable symmetrical selection of face areas
    - Select all button: Quick selection of all areas
    - Fuse mode toggle: Switch to area fusion mode

    Args:
        parent (QWidget, optional): Parent widget.
    """

    mirror_state_changed = QtCore.Signal(bool)
    fuse_mode_changed = QtCore.Signal(bool)
    select_all_clicked = QtCore.Signal()
    deselect_all_clicked = QtCore.Signal()
    multi_select_changed = QtCore.Signal(bool)

    def __init__(self, parent: Optional[QtWidgets.QWidget] = None):
        super().__init__(parent)
        self.setObjectName("FaceAreaButtonPanel")
        self._fuse_mode = False

        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        self.setLayout(main_layout)

        row1 = QtWidgets.QHBoxLayout()
        row1.setContentsMargins(160, 0, 160, 10)
        row1.setSpacing(6)
        self.btn_multi_select = QtWidgets.QCheckBox()
        self.btn_multi_select.setChecked(True)
        self.btn_multi_select.stateChanged.connect(self._on_multi_select_changed)
        multi_label = QtWidgets.QLabel("多选")
        row1.addWidget(self.btn_multi_select, 0, QtCore.Qt.AlignLeft)
        row1.addWidget(multi_label, 0, QtCore.Qt.AlignLeft)
        self.btn_mirror = QtWidgets.QCheckBox()
        self.btn_mirror.stateChanged.connect(self._on_mirror_changed)
        mirror_label = QtWidgets.QLabel("对称")
        row1.addSpacing(32)
        row1.addWidget(self.btn_mirror, 0, QtCore.Qt.AlignRight)
        row1.addWidget(mirror_label, 0, QtCore.Qt.AlignRight)
        row1.addStretch(1)
        main_layout.addLayout(row1)
        main_layout.addSpacing(8)

        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFixedHeight(2)
        line.setStyleSheet("background:#373737;")
        main_layout.addWidget(line)
        main_layout.addSpacing(8)

        row2 = QtWidgets.QHBoxLayout()
        row2.setContentsMargins(24, 10, 24, 0)
        row2.setSpacing(16)
        self.btn_select_all = QtWidgets.QPushButton("全选")
        self.btn_select_all.setFixedSize(225, 40)
        self.btn_select_all.setCursor(QtCore.Qt.PointingHandCursor)
        self.btn_select_all.clicked.connect(self._on_select_all_clicked)
        row2.addWidget(self.btn_select_all)
        self.btn_fuse_mode = QtWidgets.QPushButton("融合模式")
        self.btn_fuse_mode.setFixedSize(225, 40)
        self.btn_fuse_mode.setCursor(QtCore.Qt.PointingHandCursor)
        self.btn_fuse_mode.setCheckable(True)
        self.btn_fuse_mode.clicked.connect(self._on_fuse_clicked)
        row2.addWidget(self.btn_fuse_mode)
        row2.addStretch(1)
        main_layout.addLayout(row2)
        main_layout.addStretch(1)

        self.setStyleSheet(
            f"""
        QCheckBox {{
            spacing: 8px;
        }}
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 8px;
            border: 3px solid {const.BUTTON_BORDER};
            background: {const.BUTTON_BORDER_DARK};
        }}
        QCheckBox::indicator:checked {{
            background: {const.PRIMARY_COLOR};
            border: 3px solid {const.PRIMARY_COLOR};
        }}
        QCheckBox::indicator:unchecked {{
            background: {const.BUTTON_BORDER_DARK};
            border: 3px solid {const.BUTTON_BORDER};
        }}
        QPushButton {{
            border: 3px solid {const.BUTTON_BORDER};
            border-radius: 8px;
            background-color: {const.BUTTON_BORDER_DARK};
            color: {const.DAYU_PRIMARY_TEXT_COLOR};
            padding: 3px;
        }}
        QPushButton:checked{{
            background-color: {const.PRIMARY_COLOR};
            color: {const.DAYU_PRIMARY_TEXT_COLOR};
            font-weight: bold;
        }}
        QPushButton:disabled{{
            border: 1px solid {const.BUTTON_BORDER};
            color: {const.DAYU_PRIMARY_TEXT_COLOR};
        }}
        QPushButton:hover {{
            border-color: {const.BUTTON_BORDER};
            background-color: {const.BUTTON_BORDER_DARK};
        }}
        QPushButton:pressed {{
            background-color: {const.BUTTON_CHECKED_BG};
            color: white;
        }}
        """
        )
        self._update_fuse_button_style()
        self.set_select_all_enabled(self.btn_multi_select.isChecked())

    def _on_multi_select_changed(self, state: int):
        checked = bool(state)
        self.multi_select_changed.emit(checked)
        self.set_select_all_enabled(checked)

    def _on_mirror_changed(self, state: int):
        self.mirror_state_changed.emit(bool(state))

    def _on_select_all_clicked(self):
        if self.btn_select_all.text() == "全选":
            self.select_all_clicked.emit()
        else:
            self.deselect_all_clicked.emit()

    def _on_fuse_clicked(self):
        if not self.btn_fuse_mode.isEnabled():
            return
        self._fuse_mode = not self._fuse_mode
        self.btn_fuse_mode.setChecked(self._fuse_mode)
        self._update_fuse_button_style()
        self.fuse_mode_changed.emit(self._fuse_mode)

    def set_select_all_enabled(self, enabled: bool):
        """Enable or disable the select all button."""
        self.btn_select_all.setEnabled(enabled)

    def set_select_all_text(self, selected_count: int, total_count: int = None):
        """Set the text of the select all button.

        Args:
            selected_count (int): Number of selected buttons.
            total_count (int, optional): Total number of buttons.
        """
        if total_count is not None and selected_count == total_count:
            self.btn_select_all.setText("全不选")
        else:
            self.btn_select_all.setText("全选")

    def set_mirror_checked(self, checked: bool):
        self.btn_mirror.blockSignals(True)
        self.btn_mirror.setChecked(checked)
        self.btn_mirror.blockSignals(False)

    def set_multi_select_checked(self, checked: bool):
        self.btn_multi_select.blockSignals(True)
        self.btn_multi_select.setChecked(checked)
        self.btn_multi_select.blockSignals(False)
        self.set_select_all_enabled(checked)

    def set_fuse_checked(self, checked: bool):
        self._fuse_mode = checked
        self.btn_fuse_mode.blockSignals(True)
        self.btn_fuse_mode.setChecked(checked)
        self.btn_fuse_mode.blockSignals(False)
        self._update_fuse_button_style()

    def set_fuse_enabled(self, enabled: bool):
        self.btn_fuse_mode.setEnabled(enabled)

    def _update_fuse_button_style(self):
        if self._fuse_mode:
            self.btn_fuse_mode.setStyleSheet(
                """
                QPushButton {
                    border-radius: 8px;
                    background: {const.BUTTON_CHECKED_BG};
                    color: white;
                    border: 2px solid {const.BUTTON_CHECKED_BG};
                }
            """
            )
        else:
            self.btn_fuse_mode.setStyleSheet(
                f"""
                QPushButton {{
                    border: 3px solid {const.BUTTON_BORDER};
                    border-radius: 8px;
                    background-color: {const.BUTTON_BORDER_DARK};
                    color: {const.DAYU_PRIMARY_TEXT_COLOR};
                    padding: 3px;
                }}
                QPushButton:disabled {{
                    border: 1px solid {const.BUTTON_BORDER};
                    color: {const.DAYU_PRIMARY_TEXT_COLOR};
                }}
                QPushButton:hover {{
                    border-color: {const.BUTTON_BORDER};
                    background-color: {const.BUTTON_BORDER_DARK};
                }}
            """
            )


class FaceAreaWidget(QtWidgets.QWidget):
    """
    FaceAreaWidget is a composite widget for face area selection and manipulation.

    This widget combines a FaceAreaView and FaceAreaButtonPanel to provide
    a complete interface for selecting and manipulating face areas. It manages
    the state and interactions between the view and button panel components.

    Args:
        config (dict): Configuration dictionary containing view settings.
        parent (QWidget, optional): Parent widget.
        ring_widget (QWidget, optional): Ring widget for pivot manipulation.
    """

    button_state_changed = QtCore.Signal(str, bool)
    mirror_state_changed = QtCore.Signal(bool)
    fuse_mode_changed = QtCore.Signal(bool)
    selected_count_changed = QtCore.Signal(int, int)  # (selected_count, total_count)
    selected_buttons_changed = QtCore.Signal(list)

    def __init__(
        self,
        config: dict,
        parent: Optional[QtWidgets.QWidget] = None,
        ring_widget: Optional[QtWidgets.QWidget] = None,
    ):
        super(FaceAreaWidget, self).__init__(parent)
        self.config = config
        self.ring_widget = ring_widget
        self.main_layout = QtWidgets.QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.main_layout)

        # Face area view
        self.face_area_view = FaceAreaView(config, self, ring_widget=self.ring_widget)
        self.face_area_view.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self.main_layout.addWidget(self.face_area_view, stretch=10)

        # Button area container
        self.button_area_container = QtWidgets.QWidget(self)
        h_layout = QtWidgets.QHBoxLayout(self.button_area_container)
        h_layout.setContentsMargins(0, 0, 0, 0)
        h_layout.setSpacing(0)
        h_layout.addStretch(1)
        self.button_panel = FaceAreaButtonPanel(self)
        self.button_panel.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        h_layout.addWidget(self.button_panel)
        h_layout.addStretch(1)
        self.main_layout.addWidget(self.button_area_container, stretch=2)

        self.face_area_view.background_width_changed.connect(self._on_background_width_changed)

        # State
        self.mirror_mode = False
        self.fuse_mode = False
        self.multi_select_mode = True

        enable_fuse = self.config.get("enable_fuse_mode", True)
        self.button_panel.set_fuse_enabled(enable_fuse)

        # Signal connections
        self.face_area_view.button_state_changed.connect(self._on_button_state_changed)
        self.button_panel.mirror_state_changed.connect(self.set_mirror_mode)
        self.button_panel.fuse_mode_changed.connect(self.set_fuse_mode)
        self.button_panel.select_all_clicked.connect(self.select_all)
        self.button_panel.deselect_all_clicked.connect(self.deselect_all)
        self.button_panel.multi_select_changed.connect(self.set_multi_select_mode)
        self.selected_count_changed.connect(self.button_panel.set_select_all_text)
        self.button_panel.set_select_all_enabled(self.multi_select_mode)

    def _on_button_state_changed(self, name: str, checked: bool):
        """Handle button state change, including mirror and single-select logic."""
        btn = self.face_area_view.get_button(name)
        # Mirror mode: auto sync mirror button
        if self.mirror_mode and btn and btn.mirror_name:
            mirror_btn = self.face_area_view.get_button(btn.mirror_name)
            if mirror_btn and mirror_btn.is_checked() != checked:
                mirror_btn.set_checked(checked)

        # Single-select mode: only keep current (and mirror) checked
        if not self.multi_select_mode and checked:
            keep = [name]
            if self.mirror_mode and btn and btn.mirror_name:
                keep.append(btn.mirror_name)
            self.set_selected_buttons(keep)
        else:
            self.button_state_changed.emit(name, checked)

        selected_buttons = self.face_area_view.get_selected_buttons()
        total_count = len(self.face_area_view.buttons)
        self.selected_count_changed.emit(len(selected_buttons), total_count)
        self.selected_buttons_changed.emit(selected_buttons)

    def _on_background_width_changed(self, width: int):
        """Set button panel width to match background width."""
        self.button_panel.setMinimumWidth(width)
        self.button_panel.setMaximumWidth(width)
        self.button_panel.updateGeometry()

    def set_multi_select_mode(self, enabled: bool):
        """Set whether multi-select mode is enabled.

        Args:
            enabled (bool): If True, allow multiple buttons to be selected.
        """
        self.multi_select_mode = enabled
        self.button_panel.set_multi_select_checked(enabled)
        if not enabled:
            selected = self.face_area_view.get_selected_buttons()
            if selected:
                keep = [selected[0]]
                if self.mirror_mode:
                    btn = self.face_area_view.get_button(selected[0])
                    if btn and btn.mirror_name:
                        keep.append(btn.mirror_name)
                self.set_selected_buttons(keep)
            else:
                self.deselect_all()

    def set_mirror_mode(self, enabled: bool):
        """Set whether mirror mode is enabled.

        Args:
            enabled (bool): If True, selecting a button will also select its mirror.
        """
        self.mirror_mode = enabled
        self.face_area_view.set_mirror_mode(enabled)
        self.button_panel.set_mirror_checked(enabled)
        self.mirror_state_changed.emit(enabled)

        if enabled:
            selected = set(self.face_area_view.get_selected_buttons())
            to_select = set(selected)
            for name in selected:
                btn = self.face_area_view.get_button(name)
                if btn and btn.mirror_name:
                    to_select.add(btn.mirror_name)
            self.set_selected_buttons(list(to_select))

    def set_fuse_mode(self, enabled: bool):
        """Enable or disable fuse mode.

        Args:
            enabled (bool): Whether to enable fuse mode.
        """
        if self.fuse_mode == enabled:
            return
        self.fuse_mode = enabled
        self.face_area_view.set_fuse_mode(enabled)
        self.button_panel.set_fuse_checked(enabled)
        self.fuse_mode_changed.emit(enabled)

    def select_all(self):
        """Select all buttons (if not in fuse or single-select mode)."""
        if self.fuse_mode or not self.multi_select_mode:
            return
        self.face_area_view.select_all()
        total_count = len(self.face_area_view.buttons)
        self.selected_count_changed.emit(len(self.face_area_view.get_selected_buttons()), total_count)

    def deselect_all(self):
        """Deselect all buttons (if not in fuse mode)."""
        if self.fuse_mode:
            return
        self.face_area_view.deselect_all()
        total_count = len(self.face_area_view.buttons)
        self.selected_count_changed.emit(len(self.face_area_view.get_selected_buttons()), total_count)

    def get_selected_buttons(self) -> List[str]:
        """Get names of all selected buttons."""
        return self.face_area_view.get_selected_buttons()

    def current_areas(self) -> List[str]:
        """Alias for get_selected_buttons."""
        return self.face_area_view.get_selected_buttons()

    def set_selected_buttons(self, names: List[str]):
        """Set selected buttons by name."""
        if self.fuse_mode:
            return
        self.face_area_view.set_selected_buttons(names)
        total_count = len(self.face_area_view.buttons)
        self.selected_count_changed.emit(len(self.face_area_view.get_selected_buttons()), total_count)

    def get_button(self, name: str) -> Optional[MaskButton]:
        """Get button by name."""
        return self.face_area_view.get_button(name)

    def set_button_panel_visible(self, visible: bool):
        """Show or hide the button panel."""
        self.button_panel.setVisible(visible)

    @staticmethod
    def generate_face_area_config(
        resource_root: str,
        name_list: List[str],
        background_menu: Optional[List[Tuple[str, Callable]]] = None,
        button_click: Optional[Callable[[str], None]] = None,
        button_menu: Optional[List[Tuple[str, Callable]]] = None,
        enable_fuse_mode: bool = False,
    ) -> Dict:
        """Generate a config dictionary for FaceAreaWidget.

        Creates a complete configuration dictionary for initializing a FaceAreaWidget
        with proper resources and callbacks.

        Args:
            resource_root (str): Root path for resources (images).
            name_list (list): List of button names to create.
            background_menu (list, optional): Background right-click menu config.
            button_click (Callable, optional): Button click callback function.
            button_menu (list, optional): Button right-click menu config.
            enable_fuse_mode (bool, optional): Whether to enable fuse mode.

        Returns:
            dict: The complete configuration dictionary for FaceAreaWidget.
        """

        def default_button_click(name):
            logging.info("Button clicked: %s", name)

        if background_menu is None:
            background_menu = []
        if button_click is None:
            button_click = default_button_click
        if button_menu is None:
            button_menu = []
        config = {
            "background": f"{resource_root}/face_area.png",
            "fuse_mask_path": f"{resource_root}/face_area_fuse.png",
            "background_right_click_menu": background_menu,
            "buttons": [],
            "enable_fuse_mode": enable_fuse_mode,
        }

        def get_mirror_name(name: str) -> Optional[str]:
            if "_l" in name:
                return name.replace("_l", "_r")
            elif "_r" in name:
                return name.replace("_r", "_l")
            else:
                return None

        for name in name_list:
            button = {
                "name": name,
                "mask_path": f"{resource_root}/face_area_{name}.png",
                "on_click": button_click,
                "right_click_menu": button_menu,
                "mirror_name": get_mirror_name(name),
                "mask_only_mode": True,
            }
            config["buttons"].append(button)

        return config
