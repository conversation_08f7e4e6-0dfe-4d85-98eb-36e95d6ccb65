# lightbox开发包指南

## 背景
是基于rez的打包方式；

### thm命令
thm是一个命令行base的工具，可能有人会疑惑为什么是命令行而不是带界面的，
thm作为最底层的工具，通过命令行可以很容易的把我们的各种工具插件等方便集成到任何环境

thm +p可以串联各个包，开启需要的环境
thm +p maya-2022 +p cgame_avatar_factory run maya 就可以开启一个包含cgame_avatar_factory的maya环境；但是这种情况下只是开启Maya，如何自动在这个环境中执行脚本，还需要想办法；

### tox命令
开发环境是通过TOX去管理的
tox -a能看到当前包中所支持的命令

### package.py

requires中包含了依赖库

如对于这个包的文件C:\TAHub\bs_merge\cgame_avatar_factory\package.py：
thm dev --ignore-standard-cmd tox -e lm 就可以执行包含这个包的Maya2022

## 开发实践心得

### 独立UI开发环境搭建

在开发过程中，我们成功创建了一个不依赖Maya GUI的独立开发环境，以下是关键经验：

#### 1. 环境配置策略

**最佳实践**: 使用mayapy而不是maya命令
- `mayapy`: Maya的Python解释器，包含所有Maya Python API但不启动GUI
- `maya`: 启动完整的Maya GUI，不适合自动化开发

**package.py配置示例**:
```python
"hair_dev": {
    "command": "mayapy",  # 关键：使用mayapy而不是maya
    "requires": ["maya-2022", "python-3.7", "pydantic-2.5"],
    "env": {
        "PYTHONPATH": {"action": "prepend", "value": "{this.root};"},
        "CGAME_AVATAR_FACTORY_RESOURCE": {"action": "prepend", "value": "{this.root}/cgame_avatar_factory/resources"},
        "THM_LOG_LEVEL": {"action": "set", "value": "DEBUG"},
    },
}
```

#### 2. 依赖管理经验

**发现**: dev_requires中的pyside2是关键
- 标准requires中包含qtpy-1.11和dayu_widgets-0
- 但Qt后端(PySide2)需要通过Maya环境提供
- 直接使用thm dev环境无法获得Qt库

**解决方案**: 使用完整的Maya环境依赖链
```bash
thm +p python-3.7..3.11 +p maya-2022..2024 +p lightbox_config-1 +p lightbox_paths-0 +p lightbox_log-0 +p dayu_widgets-0 +p qtpy-1.11 +p setuptools-41..71 +p blinker-1.4 +p lightbox_ui-0 +p dcc_menu_api-0 +p metahuman_dnaclib-1.2 +p maya_metahuman_dna_viewer-1.2 +p pydantic-2..2.8 +p scipy-1 +p blade_client_reporter-0 +p photoshop_python_api-0.13 +p maya-2022 +p python-3.7 +p pydantic-2.5 run mayapy
```

#### 3. 启动脚本最佳实践

**成功的启动模式**:
```bash
# 直接使用thm +p命令组装所有依赖
.\start_hair_dev_direct.cmd
```

**失败的尝试**:
- 使用tox环境但传递参数困难
- 直接使用thm dev环境缺少Qt库
- 尝试Mock所有Maya依赖过于复杂

#### 4. 调试技巧

**环境检查顺序**:
1. 检查thm命令可用性
2. 检查Maya环境(maya.cmds导入)
3. 检查Qt库(qtpy或PySide2)
4. 检查dayu_widgets
5. 检查项目模块导入

**错误处理策略**:
- 提供详细的错误信息和堆栈跟踪
- 分步骤验证每个依赖
- 提供降级方案(基本Qt vs dayu_widgets)

### 常见问题解决

#### 问题1: qtpy模块不可用
**原因**: 直接使用thm dev环境没有Qt后端
**解决**: 使用Maya环境提供的PySide2

#### 问题2: tox环境无法传递脚本参数
**原因**: tox配置中的command字段固定
**解决**: 使用thm +p直接组装依赖，绕过tox

#### 问题3: 编码问题导致批处理文件失败
**原因**: 中文注释在某些环境下编码错误
**解决**: 使用英文注释，避免特殊字符

### 开发工作流建议

1. **环境准备**: 确保lightbox和Maya正确安装
2. **依赖测试**: 先运行`thm dev --ignore-standard-cmd tox -a`检查可用环境
3. **逐步调试**: 从简单的Python导入开始，逐步添加复杂功能
4. **日志记录**: 使用详细的日志记录跟踪问题
5. **文档维护**: 及时更新使用指南和已知问题


