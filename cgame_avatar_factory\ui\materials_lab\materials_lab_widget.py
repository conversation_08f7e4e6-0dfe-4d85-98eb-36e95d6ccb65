"""
Materials Lab Widget Module

Provides the main interface for the Materials Lab with three tabs:
- Style Lab: For experimenting with different material styles
- Natural Canvas: For basic material editing
- Makeup Workbench: For makeup material editing
"""

# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from dayu_widgets.tab_widget import MTabWidget
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.components.layout import FramelessVLayout
from cgame_avatar_factory.ui.materials_lab.makeup_vanity import MakeupVanityWidget
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


class MaterialsLabWidget(QtWidgets.QFrame):
    """
    Main widget for Materials Lab

    Provides three tabs:
    - Style Lab (风格实验舱): For experimenting with different material styles
    - Natural Canvas (素颜画布): For basic material editing
    - Makeup Workbench (彩妆工作台): For makeup material editing
    """

    # Signals
    sig_style_changed = QtCore.Signal(str)
    sig_texture_changed = QtCore.Signal(str)
    sig_makeup_changed = QtCore.Signal(str)

    _instance = None

    @staticmethod
    def instance():
        """Singleton pattern to ensure only one instance exists globally"""
        if MaterialsLabWidget._instance is None:
            MaterialsLabWidget._instance = MaterialsLabWidget()
        return MaterialsLabWidget._instance

    def __init__(self, parent=None):
        super(MaterialsLabWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """Initialize UI components"""
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create main tab widget
        self.tab_widget = MStyleMixin.instance_wrapper(MTabWidget())
        self.tab_widget.setTabPosition(QtWidgets.QTabWidget.North)
        self.main_layout.addWidget(self.tab_widget)

        # Create the three tabs
        self.style_lab_tab = self.create_style_lab_tab()
        self.basic_texture_tab = self.create_basic_texture_tab()
        self.makeup_vanity_tab = self.create_makeup_vanity_tab()

        # Add tabs to the tab widget
        self.tab_widget.addTab(self.style_lab_tab, "风格实验舱")
        self.tab_widget.addTab(self.basic_texture_tab, "素颜画布")
        self.tab_widget.addTab(self.makeup_vanity_tab, "彩妆工作台")

    def create_style_lab_tab(self):
        """Create the Style Lab tab"""
        tab = QtWidgets.QWidget()
        layout = FramelessVLayout()
        tab.setLayout(layout)

        placeholder = dayu_widgets.MLabel("风格实验舱 - 功能开发中")
        placeholder.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(placeholder)

        return tab

    def create_basic_texture_tab(self):
        """Create the Natural Canvas tab"""
        tab = QtWidgets.QWidget()
        layout = FramelessVLayout()
        tab.setLayout(layout)

        placeholder = dayu_widgets.MLabel("素颜画布 - 功能开发中")
        placeholder.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(placeholder)

        return tab

    def create_makeup_vanity_tab(self):
        """Create the Makeup Workbench tab"""
        tab = QtWidgets.QWidget()
        layout = FramelessVLayout()
        tab.setLayout(layout)

        # Create and add the MakeupVanityWidget
        makeup_vanity = MakeupVanityWidget()
        layout.addWidget(makeup_vanity)

        return tab
