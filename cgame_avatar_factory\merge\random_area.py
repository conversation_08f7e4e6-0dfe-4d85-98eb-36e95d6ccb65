# Import built-in modules
import random

# Import local modules
from cgame_avatar_factory.constants import DECIMAL_PLACES
from cgame_avatar_factory.constants import DEFAULT_DNA_NUM


class RandomSumToOne:
    """Generate random values that sum to 1

    Class to generate a list of random values that sum to 1,
    with control over the number of values and their distribution.

    Args:
        n: Number of random values to generate
        decimal_places: Number of decimal places to round to
        normal: Controls distribution shape (default: 0.5/n)
    """

    def __init__(self, n, decimal_places, normal=1):
        assert n > 0
        self.n = n
        self.decimal_places = decimal_places
        # NOTE: if number is too low, extreme value will be not obvious.
        self.normal = 0.5 / n if normal <= 0 else normal

    def generate(self):
        """Generate random values

        Returns:
            list: List of n random values that sum to 1
        """
        random_values = sorted(
            [random.random() ** self.normal for _ in range(self.n - 1)] + [0, 1],
        )
        random_values = [round(random_values[i + 1] - random_values[i], self.decimal_places) for i in range(self.n)]

        random.shuffle(random_values)

        return random_values


def random_area_weights(area_names, weight_len=DEFAULT_DNA_NUM, NORMAL=0, decimal=DECIMAL_PLACES):
    """Generate random weights for multiple areas

    Args:
        area_names (list): List of area names to generate weights for
        weight_len (int): Number of random values per area (default: DEFAULT_DNA_NUM)
        NORMAL (float): Controls distribution shape (default: 0)
        decimal (int): Decimal places to round to (default: DECIMAL_PLACES)

    Returns:
        dict: Dictionary mapping area names to lists of random weights
    """
    weights = {}
    for area in area_names:
        random_sum_to_one = RandomSumToOne(weight_len, decimal, NORMAL)
        random_values = random_sum_to_one.generate()
        weights[area] = random_values

    return weights
