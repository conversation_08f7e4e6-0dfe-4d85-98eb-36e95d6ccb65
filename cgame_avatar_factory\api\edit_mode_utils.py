# Import built-in modules
import logging

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory import constants as const


def create_edit_layer():
    """Create a new edit layer for blendshape manipulation

    Creates a new blendshape target for the base head mesh, allowing for
    non-destructive editing of facial features. Sets up the necessary
    connections and prepares the target for sculpting.

    Returns:
        str: Name of the newly created target
    """
    logger = logging.getLogger(__name__)

    if not cmds.objExists(const.BASE_HEAD_MESH_NAME):
        logger.error(f"找不到基础头部模型: {const.BASE_HEAD_MESH_NAME}")
        return None

    blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"

    blendshape_exists = cmds.objExists(blendshape_name)

    if not blendshape_exists:
        cmds.blendShape(const.BASE_HEAD_MESH_NAME, name=blendshape_name, parallel=True)
        target_index = 0
    else:
        existing_targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []
        target_index = len(existing_targets)

    target_name = f"{const.GOZ_MESH_NAME}_edit_layout_{target_index}"
    # Duplicate backup model because the model being edited has blendshape and skin, direct copy won't work correctly. The initial model is backed up before editing in backup_head_mesh()
    temp_target = cmds.duplicate(const.BAK_HEAD_MESH_NAME, name=f"{target_name}_temp")[0]

    cmds.blendShape(blendshape_name, edit=True, target=(const.BASE_HEAD_MESH_NAME, target_index, temp_target, 1.0))
    updated_targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []
    if len(updated_targets) > target_index:
        cmds.aliasAttr(target_name, f"{blendshape_name}.{updated_targets[target_index]}")
        logger.info(f"创建了新的形变目标: {target_name} (索引: {target_index})")

    cmds.delete(temp_target)

    cmds.select(const.BASE_HEAD_MESH_NAME, replace=True)

    cmds.setAttr(f"{blendshape_name}.{target_name}", 1)

    cmds.select(blendshape_name, replace=True)
    cmds.sculptTarget(edit=False, target=target_index)

    return target_name


def select_blendshape_target(target_name):
    """Select a specific blendshape target for editing

    Activates the specified blendshape target for sculpting in Maya,
    making it the current active target for editing operations.

    Args:
        target_name: Name of the target to select

    Returns:
        bool: Whether the target was successfully selected
    """
    logger = logging.getLogger(__name__)

    try:
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"

        if not cmds.objExists(blendshape_name):
            logger.error(f"Blendshape节点不存在: {blendshape_name}")
            return False

        targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []

        if target_name not in targets:
            logger.error(f"目标不存在: {target_name}")
            return False

        cmds.select(blendshape_name, replace=True)

        target_index = targets.index(target_name)

        cmds.sculptTarget(edit=True, target=target_index)

        logger.info(f"选中了目标: {target_name}")
        return True
    except Exception as e:
        logger.exception(f"选择目标时发生错误: {e}")
        return False


def get_blendshape_targets():
    """Get all blendshape targets sorted by index in descending order

    Retrieves the current list of blendshape targets from the Maya scene
    and sorts them by index in descending order, ensuring newly created
    targets appear at the top of the list.

    Returns:
        list: List of target names sorted by index in descending order
    """
    logger = logging.getLogger(__name__)

    try:
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"

        if not cmds.objExists(blendshape_name):
            return []

        targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []

        sorted_targets = sorted(
            targets,
            key=lambda x: int(x.split("_")[-1]) if x.split("_")[-1].isdigit() else 0,
            reverse=True,
        )
        return sorted_targets
    except Exception as e:
        logger.exception(f"获取blendshape目标时发生错误: {e}")
        return []


def delete_blendshape_target(target_name):
    """Delete a specific blendshape target

    Removes the specified blendshape target from the Maya scene,
    including all related deformation data and connections.

    Args:
        target_name: Name of the target to delete

    Returns:
        bool: Whether the target was successfully deleted
    """
    logger = logging.getLogger(__name__)

    try:
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"

        if not cmds.objExists(blendshape_name):
            logger.error(f"Blendshape节点不存在: {blendshape_name}")
            return False

        targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []
        if target_name not in targets:
            logger.error(f"目标不存在: {target_name}")
            return False

        target_index = targets.index(target_name)

        cmds.removeMultiInstance(f"{blendshape_name}.{target_name}", b=True)
        cmds.aliasAttr(f"{blendshape_name}.{target_name}", remove=True)

        logger.info(f"已删除目标: {target_name} (索引: {target_index})")
        return True
    except Exception as e:
        logger.exception(f"删除blendshape目标时发生错误: {e}")
        return False


def set_blendshape_weight(target_name, weight):
    """Set the weight of a blendshape target

    Adjusts the weight value of the specified blendshape target, controlling
    its influence on the final model. Weight ranges from 0.0 (no influence)
    to 1.0 (full influence).

    Args:
        target_name: Name of the target
        weight: Weight value, range 0.0-1.0

    Returns:
        bool: Whether the weight was successfully set
    """
    logger = logging.getLogger(__name__)

    try:
        blendshape_name = f"{const.GOZ_MESH_NAME}_{const.GOZ_BLEND_SHAPE_SUFFIX}"

        # Check if blendshape node exists
        if not cmds.objExists(blendshape_name):
            logger.error(f"Blendshape节点不存在: {blendshape_name}")
            return False

        targets = cmds.listAttr(f"{blendshape_name}.w", multi=True) or []
        if target_name not in targets:
            logger.error(f"目标不存在: {target_name}")
            return False

        cmds.setAttr(f"{blendshape_name}.{target_name}", weight)
        return True
    except Exception as e:
        logger.exception(f"设置blendshape权重时发生错误: {e}")
        return False
