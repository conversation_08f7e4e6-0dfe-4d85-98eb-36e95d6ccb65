# Import built-in modules
import os

# Import third-party modules
import maya.cmds as cmds
import numpy as np

# Import local modules
from cgame_avatar_factory import utils
import cgame_avatar_factory.constants as const


def compute_rigid_transform(src_points, tgt_points):
    """Compute rigid transformation between source and target point sets

    Args:
        src_points: Source point coordinates. Vertices of the stationary model
        tgt_points: Target point coordinates. Vertices of the model to be aligned

    Returns:
        ndarray: 4x4 transformation matrix
    """
    src_centroid = np.mean(src_points, axis=0)
    tgt_centroid = np.mean(tgt_points, axis=0)

    src_centered = src_points - src_centroid
    tgt_centered = tgt_points - tgt_centroid

    src_scale = np.linalg.norm(src_centered)
    tgt_scale = np.linalg.norm(tgt_centered)
    scale = src_scale / tgt_scale if tgt_scale != 0 else 1.0

    H = np.dot(tgt_centered.T, src_centered)
    U, _, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)
    translation = src_centroid - scale * np.dot(R, tgt_centroid)
    transform = np.eye(4)
    transform[:3, :3] = scale * R
    transform[:3, 3] = translation

    return transform


def apply_transform(mesh_data, transform_matrix):
    """Apply transformation matrix to mesh vertices

    Args:
        mesh_data: Dictionary of mesh vertices
        transform_matrix: 4x4 transformation matrix

    Returns:
        dict: Transformed mesh data
    """
    for mesh_name, vertices in mesh_data.items():
        transformed_vertices = []
        for vertex in vertices:
            point = np.array([vertex[0], vertex[1], vertex[2], 1.0])
            transformed_point = np.dot(transform_matrix, point)
            transformed_vertices.append(
                [
                    transformed_point[0],
                    transformed_point[1],
                    transformed_point[2],
                ],
            )
        mesh_data[mesh_name] = transformed_vertices

    return mesh_data


def apply_transform_to_vertices(mesh_data, transform_matrix, vertex_indices):
    """Apply transformation matrix only to specific vertices in a mesh

    Args:
        mesh_data: Dictionary of mesh vertices
        transform_matrix: 4x4 transformation matrix
        vertex_indices: List of vertex indices to transform

    Returns:
        dict: Mesh data with transformed vertices
    """
    result = {}
    vertex_indices = np.array(vertex_indices)

    for mesh_name, vertices in mesh_data.items():
        transformed_vertices = vertices.copy()

        valid_indices = vertex_indices[vertex_indices < len(vertices)]

        if len(valid_indices) > 0:
            vertices_array = np.asarray(vertices)[valid_indices]

            homogeneous_vertices = np.hstack((vertices_array, np.ones((len(vertices_array), 1))))

            transformed_points = np.dot(homogeneous_vertices, transform_matrix.T)

            for i, vid in enumerate(valid_indices):
                transformed_vertices[vid] = transformed_points[i, :3].tolist()

        result[mesh_name] = transformed_vertices

    return result


def start_align(mesh_data):
    """Align meshes using rigid transformation

    Args:
        mesh_data: Dictionary containing mesh vertex data

    Returns:
        list: Aligned mesh data
    """
    # Calculate position, rotation and scaling alignment for 4 models using Procrustes analysis
    all_head_data = []
    all_mesh_data = mesh_data
    for data in mesh_data:
        _data = data[const.BASE_HEAD_MESH_NAME]
        all_head_data.append(_data)

    a_points = get_point_positions(all_head_data[0], const.CORRESPONDENCE_INDICES)
    b_points = get_point_positions(all_head_data[1], const.CORRESPONDENCE_INDICES)
    c_points = get_point_positions(all_head_data[2], const.CORRESPONDENCE_INDICES)
    d_points = get_point_positions(all_head_data[3], const.CORRESPONDENCE_INDICES)

    b_transform = compute_rigid_transform(np.array(a_points), np.array(b_points))
    c_transform = compute_rigid_transform(np.array(a_points), np.array(c_points))
    d_transform = compute_rigid_transform(np.array(a_points), np.array(d_points))

    mesh_b_data = apply_transform(all_mesh_data[1], b_transform)
    mesh_c_data = apply_transform(all_mesh_data[2], c_transform)
    mesh_d_data = apply_transform(all_mesh_data[3], d_transform)

    mesh_data[1] = mesh_b_data
    mesh_data[2] = mesh_c_data
    mesh_data[3] = mesh_d_data
    # Adjust vertices based on specific point displacement differences combined with region model point weights (rotation matching to be added in the future)
    mesh_data = region_face_vertex_amend(mesh_data)

    return mesh_data


def get_point_positions(source, indices):
    """Generic function to get vertex positions

    Args:
        source: Data source (Maya object name, or list of vertex positions)
        indices: List of vertex indices to retrieve

    Returns:
        list: List of coordinates as numpy arrays

    Examples:
        # Get from Maya object
        get_point_positions("pCube1", [0,1,2])

        # Get from list of vertex positions
        get_point_positions(vertex_positions_list, [0,1,2])
    """
    if isinstance(source, str):
        # Maya object mode
        return [
            np.array(
                cmds.xform(
                    f"{source}.vtx[{i}]",
                    q=True,
                    t=True,
                    ws=True,
                ),
            )
            for i in indices
        ]
    elif isinstance(source, list):
        # List of vertex positions mode
        return [np.array(source[i]) for i in indices]
    else:
        raise ValueError(
            "Invalid source type, supported types are string (Maya object), dictionary (pre-stored data), or list"
            " (vertex positions)",
        )


def region_face_vertex_amend(all_mesh_data):
    """Amend region face vertices in the mesh data using rigid transformation.

    Args:
        all_mesh_data: Dictionary containing mesh vertex data

    Returns:
        dict: Amended mesh data
    """
    weights_path = os.path.join(const.CONFIG_PATH, const.WEIGHT_JSON_PATH)
    if not os.path.exists(weights_path):
        weights_path = os.path.join(const.RESOURCE_ROOT, const.WEIGHT_JSON_PATH)
    vertex_weights = utils.read_json(weights_path)

    central_vertex_path = os.path.join(const.CONFIG_PATH, const.REGION_CENTRAL_VERTEX_PATH)
    if not os.path.exists(central_vertex_path):
        central_vertex_path = os.path.join(const.RESOURCE_ROOT, const.REGION_CENTRAL_VERTEX_PATH)
    region_central_vertex = utils.read_json(central_vertex_path)

    for region, weights in vertex_weights[const.BASE_HEAD_MESH_NAME].items():
        if region not in region_central_vertex:
            continue

        vertex_list = []
        weight_list = []
        for vertex_str, weight in weights.items():
            vertex_list.append(int(vertex_str))
            weight_list.append(float(weight))

        base_vertices = [all_mesh_data[0][const.BASE_HEAD_MESH_NAME][vid] for vid in vertex_list]
        for model_index in range(1, const.REQUIRED_DNA_COUNT):
            target_vertices = [all_mesh_data[model_index][const.BASE_HEAD_MESH_NAME][vid] for vid in vertex_list]
            transform_matrix = compute_rigid_transform(np.array(base_vertices), np.array(target_vertices))

            if const.REGION_TO_MODEL_MAP.get(region):
                region_model = const.REGION_TO_MODEL_MAP[region]
                for mesh in region_model:
                    if mesh in all_mesh_data[model_index] and cmds.objExists(mesh):
                        region_vertex_indices = []
                        if mesh in vertex_weights and region in vertex_weights[mesh]:
                            region_vertex_indices = [int(vid) for vid in vertex_weights[mesh][region].keys()]
                        elif len(vertex_list) > 0:
                            region_vertex_indices = vertex_list

                        if region_vertex_indices:
                            region_mesh_data = {mesh: all_mesh_data[model_index][mesh]}
                            transformed_region = apply_transform_to_vertices(
                                region_mesh_data,
                                transform_matrix,
                                region_vertex_indices,
                            )
                            all_mesh_data[model_index][mesh] = transformed_region[mesh]

            if vertex_list:
                weights_array = np.array(weight_list)

                all_base_vertices = all_mesh_data[model_index][const.BASE_HEAD_MESH_NAME]

                for i, vid in enumerate(vertex_list):
                    original_vertex = np.array(all_base_vertices[vid])

                    point = np.array([original_vertex[0], original_vertex[1], original_vertex[2], 1.0])
                    transformed_point = np.dot(transform_matrix, point)
                    transformed_vertex = np.array([transformed_point[0], transformed_point[1], transformed_point[2]])

                    weight = weights_array[i]
                    weighted_vertex = original_vertex * (1 - weight) + transformed_vertex * weight

                    all_mesh_data[model_index][const.BASE_HEAD_MESH_NAME][vid] = weighted_vertex.tolist()

    return all_mesh_data
