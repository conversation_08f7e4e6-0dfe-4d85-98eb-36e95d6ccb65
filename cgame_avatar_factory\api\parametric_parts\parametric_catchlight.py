# Import built-in modules
import logging
import math
import os

# Import third-party modules
import maya.api.OpenMaya as om
import maya.cmds as cmds
import numpy as np
from scipy.optimize import minimize

# Import local modules
from cgame_avatar_factory.api import export_utils
from cgame_avatar_factory.api import import_utils
from cgame_avatar_factory.api.get_view_image_utils import get_images_dir
from cgame_avatar_factory.api.parametric_parts.parametric_eyes import ParametricEyes
import cgame_avatar_factory.constants as const


class Singleton(type):
    """Metaclass for implementing the Singleton pattern.

    This metaclass ensures that only one instance of a class is created.
    When a class uses this metaclass, calling the class directly returns the singleton instance.
    """

    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class ParametricCatchlight(metaclass=Singleton):
    """Parametric catchlight controller implemented as a singleton.

    This class manages catchlight-related parameters and operations.
    It uses the Singleton metaclass to ensure only one instance exists.
    """

    def __init__(self):
        # Initialize constants
        self.PRESET_PATH = f"|{const.HEAD_GRP}|{const.GEOMETRY_GRP}|{const.CATCHLIGHT_PRESETS_GRP}|"
        self.CATCHLIGHT_PATH = f"{const.HEAD_GRP}|{const.GEOMETRY_GRP}|{const.CATCHLIGHT_GRP}|"
        self.CATCHLIGHT_LEFT_SUFFIX = "_L"
        self.CATCHLIGHT_RIGHT_SUFFIX = "_R"
        self.CATCHLIGHT_OFFSET = 0.1
        self._init_catchlight_distance_info()

        self.catchlight_z_axis_left = om.MVector(0, 0, 1)
        self.catchlight_z_axis_right = om.MVector(0, 0, 1)

    @classmethod
    def get_instance(cls):
        """Legacy method to get the singleton instance (for backward compatibility).

        Returns:
            ParametricCatchlight: The singleton instance
        """
        return cls()

    def get_catchlight_presets(self):
        """Get catchlight presets with full path

        Returns:
            list: List of catchlight names
        """
        if cmds.objExists(const.CATCHLIGHT_PRESETS_GRP):
            return cmds.listRelatives(const.CATCHLIGHT_PRESETS_GRP, children=True, fullPath=True) or []
        return []

    def get_existing_catchlights(self):
        """Get existing catchlights with full path

        Returns:
            list: List of catchlight names
        """
        if cmds.objExists(const.CATCHLIGHT_GRP):
            all_catchlights = cmds.listRelatives(const.CATCHLIGHT_GRP, children=True, fullPath=True) or []
            unique_catchlights = set()
            for catchlight in all_catchlights:
                base_name = self._remove_catchlight_suffix(catchlight)
                unique_catchlights.add(base_name)
            return list(unique_catchlights)
        return []

    def get_catchlight_short_name(self, catchlight_full_name):
        """Get short name from full catchlight path."""
        return catchlight_full_name.split("|")[-1] if "|" in catchlight_full_name else catchlight_full_name

    def init_catchlight_presets(self):
        """Initialize catchlight presets by importing FBX and organizing hierarchy."""
        if cmds.objExists(const.CATCHLIGHT_PRESETS_GRP):
            cmds.delete(const.CATCHLIGHT_PRESETS_GRP)
        if cmds.objExists(const.CATCHLIGHT_MATERIAL_NAME):
            cmds.delete(const.CATCHLIGHT_MATERIAL_NAME)

        import_utils.import_fbx(const.CATCHLIGHT_PATH)

        if cmds.objExists(const.GEOMETRY_GRP):
            cmds.parent(const.CATCHLIGHT_PRESETS_GRP, const.GEOMETRY_GRP)
            self._ensure_catchlight_group_parented()
            cmds.setAttr(f"{const.CATCHLIGHT_PRESETS_GRP}.visibility", 0)

    def _ensure_catchlight_group_parented(self):
        """Ensure CATCHLIGHT_GRP is parented under GEOMETRY_GRP if it exists."""
        if cmds.objExists(const.CATCHLIGHT_GRP):
            current_parent = cmds.listRelatives(const.CATCHLIGHT_GRP, parent=True)
            if not current_parent or current_parent[0] != const.GEOMETRY_GRP:
                cmds.parent(const.CATCHLIGHT_GRP, const.GEOMETRY_GRP)

    def capture_catchlight_thumbnail(self, catchlight, icon_size=64):
        """Capture thumbnail image for a catchlight."""
        # Move catchlight presets group to a very far away position to avoid it being occluded by other objects
        cmds.setAttr(f"{const.CATCHLIGHT_PRESETS_GRP}.tz", -999999)
        # Set catchlight to white and opaque for thumbnail
        original_catchlight_color = cmds.getAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.color")[0]
        original_catchlight_transparency = cmds.getAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.transparency")[0]
        cmds.setAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.color", 1, 1, 1, type="double3")
        cmds.setAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.transparency", 0, 0, 0, type="double3")

        icon_path = os.path.join(get_images_dir(), f"{catchlight}.png")
        icon_path = export_utils.export_mesh_thumbnail(catchlight, icon_path, icon_size, icon_size, 35)

        # Restore original color and transparency
        cmds.setAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.color", *original_catchlight_color, type="double3")
        cmds.setAttr(
            f"{const.CATCHLIGHT_MATERIAL_NAME}.transparency", *original_catchlight_transparency, type="double3"
        )

        # Restore original position and settings
        cmds.setAttr(f"{const.CATCHLIGHT_PRESETS_GRP}.tz", 0)

        return icon_path

    def hide_catchlights(self, catchlights):
        """Hide specified catchlights and show presets group."""
        self._set_presets_visibility(True)
        self._set_catchlights_visibility(catchlights, False)

    def reset_catchlights_visibility(self, catchlights):
        """Reset catchlights visibility and hide presets group."""
        self._set_presets_visibility(False)
        self._set_catchlights_visibility(catchlights, True)

    def _set_presets_visibility(self, visible):
        """Set visibility of presets group."""
        if cmds.objExists(const.CATCHLIGHT_PRESETS_GRP):
            cmds.setAttr(f"{const.CATCHLIGHT_PRESETS_GRP}.visibility", int(visible))

    def _set_catchlights_visibility(self, catchlights, visible):
        """Set visibility of multiple catchlights."""
        for catchlight in catchlights:
            if cmds.objExists(catchlight):
                cmds.setAttr(f"{catchlight}.visibility", int(visible))

    def get_catchlight_full_name(self, catchlight_name):
        return self.CATCHLIGHT_PATH + catchlight_name

    def get_preset_full_name(self, catchlight_name):
        return self.PRESET_PATH + catchlight_name

    def _init_catchlight_distance_info(self):
        eyeball_anchors_pos_dict = ParametricEyes().get_eyeball_anchor_pos()
        if not eyeball_anchors_pos_dict:
            self.init_catchlight_dist_left = 1.0
            self.init_catchlight_dist_right = 1.0
            self.current_catchlight_dist_left = 1.0
            self.current_catchlight_dist_right = 1.0
            return None

        left_eye_anchors = eyeball_anchors_pos_dict["left"]
        right_eye_anchors = eyeball_anchors_pos_dict["right"]

        # Calculate distance from eye front to center and add offset
        self.init_catchlight_dist_left = (
            np.linalg.norm(
                left_eye_anchors["front"] - left_eye_anchors["center"],
            )
            + self.CATCHLIGHT_OFFSET
        )
        self.init_catchlight_dist_right = (
            np.linalg.norm(
                right_eye_anchors["front"] - right_eye_anchors["center"],
            )
            + self.CATCHLIGHT_OFFSET
        )

        self.current_catchlight_dist_left = self.init_catchlight_dist_left
        self.current_catchlight_dist_right = self.init_catchlight_dist_right

        return eyeball_anchors_pos_dict

    def _get_local_axes(self, object_name):
        """Get the world space vector of the local axes of an object.

        Args:
            object_name (str): Name of the object

        Returns:
            dict: World space vectors of the local axes
        """
        matrix = cmds.xform(object_name, q=True, matrix=True, worldSpace=True)

        # Extract and normalize the local axes from the matrix
        axes = {
            "x": np.array(matrix[0:3]),  # Index 0-2
            "y": np.array(matrix[4:7]),  # Index 4-6
            "z": np.array(matrix[8:11]),  # Index 8-10
        }

        return {axis: vector / np.linalg.norm(vector) for axis, vector in axes.items()}

    def _orient_catchlight_y_to_eye_center(self, catchlight_name, eye_center_pos, z_axis):
        """Orient catchlight so its Y-axis points toward the eye center.

        Args:
            catchlight_name (str): Full name of the catchlight object
            eye_center_pos (tuple): Position of the eye center [x, y, z]
        """
        if not cmds.objExists(catchlight_name):
            return

        # Get current catchlight position
        catchlight_pos = cmds.xform(catchlight_name, query=True, translation=True, worldSpace=True)

        # Create vectors for the calculation
        catchlight_vector = om.MVector(*catchlight_pos)
        eye_center_vector = om.MVector(*eye_center_pos)

        # Calculate the direction vector (aim vector) - Y axis should point toward eye center
        aim_vector = (eye_center_vector - catchlight_vector).normalize()

        # Define the up vector (world up)
        # world_up = om.MVector(0, 0, 1) if cmds.upAxis(q=True, axis=True).lower() == "z" else om.MVector(0, 1, 0)
        up_vector = z_axis

        # Calculate the right vector (perpendicular to aim and up)
        right_vector = (aim_vector ^ up_vector).normalize()  # Cross product

        # Recalculate a perpendicular up vector (Z axis)
        up_vector = (right_vector ^ aim_vector).normalize()  # Cross product

        # Create rotation matrix with Y-axis pointing toward eye center
        # Matrix layout: [right_x, right_y, right_z, 0, aim_x, aim_y, aim_z, 0, up_x, up_y, up_z, 0, 0, 0, 0, 1]
        rotation_matrix = [
            right_vector.x,
            right_vector.y,
            right_vector.z,
            0,
            aim_vector.x,
            aim_vector.y,
            aim_vector.z,
            0,
            up_vector.x,
            up_vector.y,
            up_vector.z,
            0,
            0,
            0,
            0,
            1,
        ]

        # Store the current position and scale before applying rotation matrix
        current_position = catchlight_pos
        current_scale = cmds.xform(catchlight_name, query=True, scale=True, worldSpace=True)

        # Apply the rotation matrix to the catchlight (this will reset position and scale)
        cmds.xform(catchlight_name, matrix=rotation_matrix, worldSpace=True)

        # Re-apply the position and scale after setting rotation
        cmds.xform(catchlight_name, translation=current_position, worldSpace=True)
        cmds.xform(catchlight_name, scale=current_scale, worldSpace=True)

    def _align_single_catchlight_to_eye(self, catchlight_name, eye_driver, eye_front_pos, eye_center_pos, z_axis):
        """Align a single catchlight to an eye driver.

        Args:
            catchlight_name (str): Full name of the catchlight object
            eye_driver (str): Name of the eye shape driver
            eye_front_pos (tuple): Position of the eye front
            eye_center_pos (tuple): Position of the eye center
        """
        if not cmds.objExists(catchlight_name):
            return

        # Set position
        cmds.xform(catchlight_name, translation=eye_front_pos, worldSpace=True)

        # Orient catchlight Y-axis toward eye center
        self._orient_catchlight_y_to_eye_center(catchlight_name, eye_center_pos, z_axis)

        # Apply final offset
        cmds.move(
            0,
            -self.CATCHLIGHT_OFFSET,
            0,
            catchlight_name,
            relative=True,
            objectSpace=True,
            worldSpaceDistance=True,
        )

    def align_catchlights_to_eyes(self, catchlight_base_names):
        """Align catchlights to eyes.

        Args:
            catchlight_base_names (list): List of catchlight base names with path
        """
        for base_name_with_path in catchlight_base_names:
            full_name_left = base_name_with_path + self.CATCHLIGHT_LEFT_SUFFIX
            full_name_right = base_name_with_path + self.CATCHLIGHT_RIGHT_SUFFIX

            eyeball_anchors_pos_dict = self._init_catchlight_distance_info()
            left_eye_anchors = eyeball_anchors_pos_dict["left"]
            right_eye_anchors = eyeball_anchors_pos_dict["right"]

            self._align_single_catchlight_to_eye(
                full_name_left,
                const.EYE_SHAPE_DRIVER_L,
                left_eye_anchors["front"],
                left_eye_anchors["center"],
                self.catchlight_z_axis_left,
            )
            self._align_single_catchlight_to_eye(
                full_name_right,
                const.EYE_SHAPE_DRIVER_R,
                right_eye_anchors["front"],
                right_eye_anchors["center"],
                self.catchlight_z_axis_right,
            )

    def _remove_catchlight_suffix(self, catchlight_name):
        # Remove only the last occurrence of left suffix
        if self.CATCHLIGHT_LEFT_SUFFIX in catchlight_name:
            last_left_index = catchlight_name.rfind(self.CATCHLIGHT_LEFT_SUFFIX)
            catchlight_name = (
                catchlight_name[:last_left_index]
                + catchlight_name[last_left_index + len(self.CATCHLIGHT_LEFT_SUFFIX) :]
            )

        # Remove only the last occurrence of right suffix
        if self.CATCHLIGHT_RIGHT_SUFFIX in catchlight_name:
            last_right_index = catchlight_name.rfind(self.CATCHLIGHT_RIGHT_SUFFIX)
            catchlight_name = (
                catchlight_name[:last_right_index]
                + catchlight_name[last_right_index + len(self.CATCHLIGHT_RIGHT_SUFFIX) :]
            )

        return catchlight_name

    def create_catchlight(self, catchlight_name):
        """Create a new catchlight from preset."""
        self._ensure_catchlight_group_exists()

        full_name = self.PRESET_PATH + catchlight_name
        if not cmds.objExists(full_name):
            return None

        # Duplicate catchlight for left eye
        new_catchlight = cmds.duplicate(full_name, name=catchlight_name)[0]
        new_catchlight_left = cmds.rename(new_catchlight, new_catchlight + self.CATCHLIGHT_LEFT_SUFFIX)

        # Duplicate instance for right eye
        new_catchlight_right = cmds.duplicate(
            new_catchlight_left,
            name=new_catchlight_left.replace(self.CATCHLIGHT_LEFT_SUFFIX, self.CATCHLIGHT_RIGHT_SUFFIX),
            instanceLeaf=True,
            returnRootsOnly=True,
            renameChildren=False,
            smartTransform=False,
        )[0]

        # Parent and setup visibility
        for catchlight in [new_catchlight_left, new_catchlight_right]:
            cmds.parent(catchlight, const.CATCHLIGHT_GRP)
            cmds.setAttr(f"{catchlight}.visibility", 1)

        # Align catchlights to eyes
        self.align_catchlights_to_eyes([self._remove_catchlight_suffix(new_catchlight_left)])
        cmds.select(clear=True)

        return new_catchlight

    def _ensure_catchlight_group_exists(self):
        """Ensure CATCHLIGHT_GRP exists and is properly parented."""
        if not cmds.objExists(const.CATCHLIGHT_GRP):
            cmds.group(empty=True, name=const.CATCHLIGHT_GRP)
            if cmds.objExists(const.GEOMETRY_GRP):
                cmds.parent(const.CATCHLIGHT_GRP, const.GEOMETRY_GRP)

    def _rotation_matrix(self, axis, theta):
        """Create rotation matrix using Rodrigues' rotation formula."""
        axis = axis / np.linalg.norm(axis)  # Ensure axis vector is normalized
        a = np.cos(theta)
        b = np.sin(theta)
        c = 1 - np.cos(theta)
        ux, uy, uz = axis

        return np.array(
            [
                [a + c * ux**2, c * ux * uy - b * uz, c * ux * uz + b * uy],
                [c * uy * ux + b * uz, a + c * uy**2, c * uy * uz - b * ux],
                [c * uz * ux - b * uy, c * uz * uy + b * ux, a + c * uz**2],
            ],
        )

    def _rotate_point(self, input_pos, rotate_center_pos, right_axis, up_axis, degree_right, degree_up):
        """Rotate a point around a center using two rotation axes."""
        # Translate point to origin
        p_translated = input_pos - rotate_center_pos

        # Normalize axes and create rotation matrices
        up_axis = up_axis / np.linalg.norm(up_axis)
        right_axis = right_axis / np.linalg.norm(right_axis)

        rotation_up = self._rotation_matrix(up_axis, degree_up)
        rotation_right = self._rotation_matrix(right_axis, degree_right)
        combined_rotation = rotation_right @ rotation_up

        # Apply rotation and translate back
        return combined_rotation @ p_translated + rotate_center_pos

    def delete_catchlight(self, catchlight_name):
        """Delete catchlight for both eyes."""
        catchlight_names = [
            self.CATCHLIGHT_PATH + catchlight_name + self.CATCHLIGHT_LEFT_SUFFIX,
            self.CATCHLIGHT_PATH + catchlight_name + self.CATCHLIGHT_RIGHT_SUFFIX,
        ]

        for full_name in catchlight_names:
            if cmds.objExists(full_name):
                cmds.delete(full_name)

    def _calculate_catchlight_position(self, eye_anchors, degree_up, degree_right, catchlight_distance):
        """Calculate the rotated position for a single catchlight.

        Args:
            eye_anchors (dict): Eye anchor positions containing front, center, top, bottom, right, left
            degree_up (float): Rotation degree around up axis
            degree_right (float): Rotation degree around right axis
            catchlight_distance (float): Distance from the center to the catchlight

        Returns:
            tuple: Final position as (x, y, z)
        """
        # Extract positions
        eye_front_pos = eye_anchors["front"]
        eye_center_pos = eye_anchors["center"]

        # Calculate axes
        up_axis = eye_anchors["top"] - eye_anchors["bottom"]
        right_axis = eye_anchors["right"] - eye_anchors["left"]

        # Calculate normalized front axis and starting position
        normalized_front_axis = (eye_front_pos - eye_center_pos) / np.linalg.norm(eye_front_pos - eye_center_pos)
        start_pose_offset = catchlight_distance * normalized_front_axis
        start_pos = eye_center_pos + start_pose_offset

        # Calculate final rotated position
        end_pos = self._rotate_point(
            start_pos,
            eye_center_pos,
            right_axis,
            up_axis,
            degree_right,
            degree_up,
        )

        return end_pos

    def revolve_catchlights(self, catchlight_full_name, degree_up, degree_right):
        """Revolve catchlights for both eyes based on given rotation degrees."""
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
        catchlight_right = catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX

        eyeball_anchors_dict = ParametricEyes().get_eyeball_anchor_pos()
        if not eyeball_anchors_dict:
            return

        # Calculate positions for both eyes
        end_pos_left = self._calculate_catchlight_position(
            eyeball_anchors_dict["left"],
            degree_up,
            degree_right,
            self.current_catchlight_dist_left,
        )
        end_pos_right = self._calculate_catchlight_position(
            eyeball_anchors_dict["right"],
            degree_up,
            degree_right,
            self.current_catchlight_dist_right,
        )

        # Apply transformations
        cmds.xform(catchlight_left, translation=end_pos_left, worldSpace=True)
        cmds.xform(catchlight_right, translation=end_pos_right, worldSpace=True)

        # Orient catchlights to eye center
        self._orient_catchlight_y_to_eye_center(
            catchlight_left,
            eyeball_anchors_dict["left"]["center"],
            self.catchlight_z_axis_left,
        )
        self._orient_catchlight_y_to_eye_center(
            catchlight_right,
            eyeball_anchors_dict["right"]["center"],
            self.catchlight_z_axis_right,
        )

    def resize_catchlights(self, catchlight_full_name, scale_factor):
        """Resize catchlights for both eyes based on given scale factor."""
        catchlights = [
            catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX,
            catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX,
        ]

        for catchlight in catchlights:
            for axis in ["X", "Y", "Z"]:
                cmds.setAttr(f"{catchlight}.scale{axis}", scale_factor)

    def _process_single_eye_offset(self, catchlight_name, eye_anchors, distance_offset):
        """Process offset for a single eye catchlight.

        Args:
            catchlight_name (str): Name of the catchlight object
            eye_anchors (dict): Eye anchor positions containing front and center
            distance_offset (float): Distance offset to apply

        Returns:
            tuple: (init_catchlight_dist, current_catchlight_dist)
        """
        eye_front_pos = eye_anchors["front"]
        eye_center_pos = eye_anchors["center"]

        # Calculate initial distance and current direction
        init_catchlight_dist = np.linalg.norm(eye_front_pos - eye_center_pos) + self.CATCHLIGHT_OFFSET
        current_catchlight_pos = np.array(cmds.xform(catchlight_name, query=True, translation=True, worldSpace=True))
        current_catchlight_dir = current_catchlight_pos - np.array(eye_center_pos)
        current_catchlight_dir = current_catchlight_dir / np.linalg.norm(current_catchlight_dir)

        # Apply offset and update position
        current_catchlight_dist = init_catchlight_dist + distance_offset
        new_catchlight_pos = np.array(eye_center_pos) + current_catchlight_dir * current_catchlight_dist
        cmds.xform(catchlight_name, translation=new_catchlight_pos, worldSpace=True)

        return init_catchlight_dist, current_catchlight_dist

    def offset_catchlights(self, catchlight_full_name, distance_offset):
        """Offset catchlights for both eyes based on given distance."""
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
        catchlight_right = catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX

        eyeball_anchors_dict = ParametricEyes().get_eyeball_anchor_pos()
        if not eyeball_anchors_dict:
            return

        # Process both eyes
        self.init_catchlight_dist_left, self.current_catchlight_dist_left = self._process_single_eye_offset(
            catchlight_left,
            eyeball_anchors_dict["left"],
            distance_offset,
        )
        self.init_catchlight_dist_right, self.current_catchlight_dist_right = self._process_single_eye_offset(
            catchlight_right,
            eyeball_anchors_dict["right"],
            distance_offset,
        )

    def rotate_catchlights(self, catchlight_full_name, angle_degree):
        """Rotate catchlights for both eyes based on given angle."""
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
        catchlight_right = catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX

        # Convert angle (0-360) to radians and map to xz plane normalized vector
        # value=0 -> (0, 0, 1), value=90 -> (1, 0, 0), value=180 -> (0, 0, -1), value=270 -> (-1, 0, 0)
        angle_rad = math.radians(angle_degree)
        up_vector = om.MVector(math.sin(angle_rad), 0, math.cos(angle_rad))

        # Update z-axis for both catchlights
        self.catchlight_z_axis_left = up_vector
        self.catchlight_z_axis_right = up_vector

        # Calculate rotation matrix based on current Y-axis and new up vector
        y_axis = self._get_local_axes(catchlight_left)["y"]
        aim_vector = om.MVector(y_axis)
        right_vector = (aim_vector ^ up_vector).normalize()
        up_vector = (right_vector ^ aim_vector).normalize()

        rotation_matrix = [
            right_vector.x,
            right_vector.y,
            right_vector.z,
            0,
            aim_vector.x,
            aim_vector.y,
            aim_vector.z,
            0,
            up_vector.x,
            up_vector.y,
            up_vector.z,
            0,
            0,
            0,
            0,
            1,
        ]

        # Apply rotation to both catchlights while preserving position and scale
        for catchlight in [catchlight_left, catchlight_right]:
            current_position = cmds.xform(catchlight, query=True, translation=True, worldSpace=True)
            current_scale = cmds.xform(catchlight, query=True, scale=True, worldSpace=True)
            cmds.xform(catchlight, matrix=rotation_matrix, worldSpace=True)
            cmds.xform(catchlight, translation=current_position, worldSpace=True)
            cmds.xform(catchlight, scale=current_scale, worldSpace=True)

    def set_catchlight_visibility(self, catchlight_full_name, is_visible, eye_side):
        """Set visibility of catchlights for both eyes.

        Args:
            catchlight_full_name (str): Full name of the catchlight object
            is_visible (bool): Whether the catchlight should be visible
            eye_side (str): Which eye side to set visibility for, "left" or "right"
        """
        catchlight_name_with_suffix = (
            catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
            if eye_side == "left"
            else catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX
        )
        cmds.setAttr(f"{catchlight_name_with_suffix}.v", is_visible)

    def get_catchlight_visibility(self, catchlight_full_name):
        """Get visibility of catchlights for both eyes.

        Args:
            catchlight_full_name (str): Full name of the catchlight object

        Returns:
            tuple: (left_visibility, right_visibility)
        """
        try:
            catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
            catchlight_right = catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX
            return (cmds.getAttr(f"{catchlight_left}.v"), cmds.getAttr(f"{catchlight_right}.v"))
        except Exception as e:
            logging.error(f"Failed to get catchlight visibility: {e}")
            return (True, True)

    def set_catchlight_color(self, color):
        """Set color of catchlights for both eyes.

        Args:
            color (tuple or QColor): RGB color values as a tuple (r, g, b) or QColor object
        """
        try:
            # Handle both QColor objects and tuples
            if hasattr(color, "getRgbF"):  # QColor object
                r, g, b, _ = color.getRgbF()  # Get normalized RGB values (0.0-1.0)
            elif hasattr(color, "red"):  # QColor object (alternative check)
                r = color.red() / 255.0
                g = color.green() / 255.0
                b = color.blue() / 255.0
            else:  # Assume it's a tuple/list
                r, g, b = color[0], color[1], color[2]

            cmds.setAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.color", r, g, b, type="double3")
        except Exception as e:
            logging.error(f"Failed to set catchlight color: {e}")

    def set_catchlight_opacity(self, opacity):
        """Set opacity of catchlights for both eyes.

        Args:
            opacity (float): Opacity value (0.0-1.0)
        """
        try:
            # Maya transparency is inverted: 0 = opaque, 1 = transparent
            # So we need to invert the opacity value
            transparency = 1.0 - opacity

            # Maya transparency attribute expects RGB values (3 components)
            cmds.setAttr(
                f"{const.CATCHLIGHT_MATERIAL_NAME}.transparency",
                transparency,
                transparency,
                transparency,
                type="double3",
            )
        except Exception as e:
            logging.error(f"Failed to set catchlight opacity: {e}")

    def get_catchlight_color(self):
        """Get color of catchlights for both eyes.

        Returns:
            tuple: RGB color values as a tuple (r, g, b)
        """
        try:
            return cmds.getAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.color")[0]
        except Exception as e:
            logging.error(f"Failed to get catchlight color: {e}")
            return (1.0, 1.0, 1.0)  # Default to white if failed

    def get_catchlight_opacity(self):
        """Get opacity of catchlights for both eyes.

        Returns:
            float: Opacity value (0.0-1.0)
        """
        try:
            transparency = cmds.getAttr(f"{const.CATCHLIGHT_MATERIAL_NAME}.transparency")[0][0]
        except Exception as e:
            logging.error(f"Failed to get catchlight opacity: {e}")
            return 1.0  # Default to fully opaque if failed

        # Maya transparency is inverted: 0 = opaque, 1 = transparent
        # So we need to invert the transparency value
        opacity = 1.0 - transparency
        return opacity

    def get_catchlight_size(self, catchlight_full_name):
        """Get size of catchlights for both eyes.

        Returns:
            float: Size value
        """
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
        return cmds.getAttr(f"{catchlight_left}.scaleX")

    def get_catchlight_rotation(self, catchlight_full_name):
        """Get rotation of catchlights for both eyes.

        Args:
            catchlight_full_name (str): Full name of the catchlight object

        Returns:
            float: Rotation angle in degrees (0-360)
        """
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX

        if not cmds.objExists(catchlight_left):
            return 0.0

        # Get the current local axes of the catchlight
        local_axes = self._get_local_axes(catchlight_left)
        z_axis = local_axes["z"]  # This is the up vector that was set during rotation

        # The up vector in rotate_catchlights was calculated as:
        # up_vector = om.MVector(math.sin(angle_rad), 0, math.cos(angle_rad))
        # So we need to reverse this calculation:
        # x = sin(angle_rad), z = cos(angle_rad)
        # angle_rad = atan2(x, z)

        # Extract x and z components from the z-axis (up vector)
        x_component = z_axis[0]  # sin(angle_rad)
        z_component = z_axis[2]  # cos(angle_rad)

        # Calculate angle in radians using atan2
        angle_rad = math.atan2(x_component, z_component)

        # Convert to degrees
        angle_degree = math.degrees(angle_rad)

        # Ensure angle is in 0-360 range
        if angle_degree < 0:
            angle_degree += 360.0

        return angle_degree

    def get_catchlight_revolution(self, catchlight_full_name):
        """Get revolution of catchlights for both eyes.

        Args:
            catchlight_full_name (str): Full name of the catchlight object

        Returns:
            tuple: (degree_up, degree_right) values representing the revolution
        """
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX

        if not cmds.objExists(catchlight_left):
            return (0.0, 0.0)

        # Get current catchlight position and eye anchors
        current_catchlight_pos = np.array(cmds.xform(catchlight_left, query=True, translation=True, worldSpace=True))

        try:
            eyeball_anchors_dict = ParametricEyes().get_eyeball_anchor_pos()
            eye_anchors = eyeball_anchors_dict["left"]
        except Exception:
            # If we can't get eye anchors, return default values
            return (0.0, 0.0)

        # Extract eye positions and calculate axes (same as in _calculate_catchlight_position)
        eye_front_pos = eye_anchors["front"]
        eye_center_pos = eye_anchors["center"]
        up_axis = eye_anchors["top"] - eye_anchors["bottom"]
        right_axis = eye_anchors["right"] - eye_anchors["left"]

        # Calculate the initial position (before rotation)
        normalized_front_axis = (eye_front_pos - eye_center_pos) / np.linalg.norm(eye_front_pos - eye_center_pos)
        start_pose_offset = self.current_catchlight_dist_left * normalized_front_axis
        start_pos = eye_center_pos + start_pose_offset

        # Now we need to reverse the rotation to find degree_up and degree_right
        # The rotation was: end_pos = _rotate_point(start_pos, eye_center_pos, right_axis, up_axis, degree_right, degree_up)
        # We need to solve: current_catchlight_pos = _rotate_point(start_pos, eye_center_pos, right_axis, up_axis, degree_right, degree_up)

        # Normalize axes
        up_axis_normalized = up_axis / np.linalg.norm(up_axis)
        right_axis_normalized = right_axis / np.linalg.norm(right_axis)

        # Use numerical optimization to find the angles that best match the current position
        def objective_function(angles):
            """Objective function to minimize - distance between calculated and actual position."""
            degree_up_test, degree_right_test = angles
            calculated_pos = self._rotate_point(
                start_pos,
                eye_center_pos,
                right_axis_normalized,
                up_axis_normalized,
                degree_right_test,
                degree_up_test,
            )
            return np.linalg.norm(calculated_pos - current_catchlight_pos)

        # Use scipy.optimize if available, otherwise use a simple grid search
        try:
            # Initial guess
            initial_guess = [0.0, 0.0]
            # Bounds for the angles (reasonable range for catchlight revolution)
            bounds = [(-np.pi / 2, np.pi / 2), (-np.pi / 2, np.pi / 2)]

            result = minimize(objective_function, initial_guess, bounds=bounds, method="L-BFGS-B")
            degree_up, degree_right = result.x

        except ImportError:
            # Fallback: simple grid search if scipy is not available
            best_distance = float("inf")
            best_angles = (0.0, 0.0)

            # Grid search with reasonable resolution
            for degree_up_test in np.linspace(-np.pi / 4, np.pi / 4, 21):  # -45 to 45 degrees
                for degree_right_test in np.linspace(-np.pi / 4, np.pi / 4, 21):
                    distance = objective_function([degree_up_test, degree_right_test])
                    if distance < best_distance:
                        best_distance = distance
                        best_angles = (degree_up_test, degree_right_test)

            degree_up, degree_right = best_angles

        return (float(degree_up), float(degree_right))

    def get_catchlight_offset(self, catchlight_full_name):
        """Get offset of catchlights for both eyes.

        Args:
            catchlight_full_name (str): Full name of the catchlight object

        Returns:
            float: Distance offset value
        """
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX

        if not cmds.objExists(catchlight_left):
            return 0.0

        # Get current catchlight position and eye anchors
        current_catchlight_pos = np.array(cmds.xform(catchlight_left, query=True, translation=True, worldSpace=True))

        try:
            eyeball_anchors_dict = ParametricEyes().get_eyeball_anchor_pos()
            eye_anchors = eyeball_anchors_dict["left"]
        except Exception:
            # If we can't get eye anchors, return default value
            return 0.0

        # Extract eye positions (same as in _process_single_eye_offset)
        eye_front_pos = eye_anchors["front"]
        eye_center_pos = eye_anchors["center"]

        # Calculate initial distance (same as in _process_single_eye_offset)
        init_catchlight_dist = np.linalg.norm(eye_front_pos - eye_center_pos) + self.CATCHLIGHT_OFFSET

        # Calculate current distance from eye center to catchlight
        current_catchlight_dir = current_catchlight_pos - np.array(eye_center_pos)
        current_catchlight_dist = np.linalg.norm(current_catchlight_dir)

        # Calculate distance offset
        # From _process_single_eye_offset: current_catchlight_dist = init_catchlight_dist + distance_offset
        # So: distance_offset = current_catchlight_dist - init_catchlight_dist
        distance_offset = current_catchlight_dist - init_catchlight_dist

        return float(distance_offset)

    def enter_edit_mode(self, catchlight_full_name, component_type):
        """Enter edit mode for catchlights.

        Args:
            catchlight_full_name (str): Full name of the catchlight object
            component_type (str): Type of component to select, "edge", "vertex", or "face"
        """
        catchlight_left = catchlight_full_name + self.CATCHLIGHT_LEFT_SUFFIX
        catchlight_right = catchlight_full_name + self.CATCHLIGHT_RIGHT_SUFFIX

        left_visibility, right_visibility = self.get_catchlight_visibility(catchlight_full_name)

        try:
            # Build fit list based on visibility
            fit_list = []
            if left_visibility:
                fit_list.append(catchlight_left)
            if right_visibility:
                fit_list.append(catchlight_right)

            if not fit_list:
                return

            cmds.viewFit(fit_list, animate=True, fitFactor=0.38)
            cmds.selectMode(component=True)

            # Component type selection mapping
            component_config = {
                "edge": {"select_type": {"edge": True}, "suffix": ".e[0]"},
                "vertex": {"select_type": {"vertex": True}, "suffix": ".vtx[0]"},
                "face": {"select_type": {"polymeshFace": True}, "suffix": ".f[0]"},
            }

            if component_type in component_config:
                config = component_config[component_type]
                cmds.selectType(**config["select_type"])

                # Select the first visible catchlight component
                target_catchlight = catchlight_left if left_visibility else catchlight_right
                cmds.select(target_catchlight + config["suffix"], replace=True)

        except Exception as e:
            logging.error(f"Failed to enter edit mode: {e}")
