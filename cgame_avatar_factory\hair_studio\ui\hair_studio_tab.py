"""Hair Studio Tab Module.

This module provides the main tab widget for the Hair Studio tool, which serves as the container
for all hair-related functionality including card, XGen, and curve hair tools.
"""

# Import standard library
import os
import logging

# Import Qt modules
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.manager import Hair<PERSON><PERSON><PERSON>
from cgame_avatar_factory.hair_studio.constants import (
    UI_TEXT_CARD_TAB,
    UI_TEXT_XGEN_TAB,
    UI_TEXT_CURVE_TAB,
    HAIR_TYPE_CARD,
    HAIR_TYPE_XGEN,
    HAIR_TYPE_CURVE,
    DEFAULT_TAB_INDEX,
    OBJECT_NAME_HAIR_STUDIO_TAB,
    ERROR_MSG_FAILED_TO_SETUP_UI,
    ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO,
    ERROR_MSG_ERROR_CHANGING_TABS,
    ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST,
    ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION,
)

# BaseHairTab will be imported in the setup_ui method to avoid circular imports


class HairStudioTab(QtWidgets.QTabWidget):
    """Main tab widget for the Hair Studio tool.

    This class serves as the container for all hair-related tabs including Card, XGen, and Curve tabs.
    It manages the tab switching and coordinates between different hair tools.
    """

    def __init__(self, parent=None, logger=None):
        """Initialize the HairStudioTab.

        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(HairStudioTab, self).__init__(parent)
        self.setObjectName(OBJECT_NAME_HAIR_STUDIO_TAB)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Initialize the hair manager with logger
        self._hair_manager = HairManager(logger=self._logger)

        # Initialize UI components
        self.setup_ui()

        # Connect signals
        self._connect_signals()

    def setup_ui(self):
        """Set up the user interface components.

        This method initializes and adds all the tab widgets to the main tab container.
        """
        # Import here to avoid circular imports
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab

        try:
            # Create tabs with the hair manager and logger
            self.card_tab = BaseHairTab(
                HAIR_TYPE_CARD, self._hair_manager, self, logger=self._logger
            )
            self.xgen_tab = BaseHairTab(
                HAIR_TYPE_XGEN, self._hair_manager, self, logger=self._logger
            )
            self.curve_tab = BaseHairTab(
                HAIR_TYPE_CURVE, self._hair_manager, self, logger=self._logger
            )

            # Add tabs to the tab widget
            self.addTab(self.card_tab, UI_TEXT_CARD_TAB)
            self.addTab(self.xgen_tab, UI_TEXT_XGEN_TAB)
            self.addTab(self.curve_tab, UI_TEXT_CURVE_TAB)

            # Set default tab
            self.setCurrentIndex(DEFAULT_TAB_INDEX)

            # Connect signals
            self.currentChanged.connect(self._on_tab_changed)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_FAILED_TO_SETUP_UI, str(e), exc_info=True
            )
            QtWidgets.QMessageBox.critical(
                self,
                "Error",
                "{}: {}".format(ERROR_MSG_FAILED_TO_INITIALIZE_HAIR_STUDIO, str(e)),
            )

    def _connect_signals(self):
        """Connect signals between components."""
        # Connect component updates
        self._hair_manager.components_updated.connect(self._on_components_updated)
        self._hair_manager.component_selected.connect(self._on_component_selected)

    def _on_tab_changed(self, index):
        """Handle tab change events.

        Args:
            index (int): Index of the newly selected tab
        """
        try:
            if index < 0:  # No tab selected
                return

            # Get the current tab
            current_tab = self.widget(index)

            # Refresh the asset library for the current tab
            current_tab.refresh_asset_library()

            # Update the component list for the current tab type
            self._update_component_list()

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_CHANGING_TABS, str(e), exc_info=True
            )

    def _on_components_updated(self, components):
        """Handle updates to the component list.

        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Update component list in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.update_component_list(components)
        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST, str(e), exc_info=True
            )

    def _on_component_selected(self, component):
        """Handle component selection changes.

        Args:
            component (HairComponent or None): The selected component, or None if deselected
        """
        try:
            # Update editor area in the current tab
            current_tab = self.get_current_tab()
            if current_tab:
                current_tab.set_selected_component(component)
        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_HANDLING_COMPONENT_SELECTION,
                str(e),
                exc_info=True,
            )

    def _update_component_list(self):
        """Update the component list for the current tab type."""
        try:
            current_tab = self.get_current_tab()
            if not current_tab:
                return

            # Get components filtered by the current tab type
            components = self._hair_manager.get_components()
            filtered_components = [
                comp for comp in components if comp.get("type") == current_tab.hair_type
            ]

            # Update the component list
            current_tab.update_component_list(filtered_components)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST, str(e), exc_info=True
            )

    def get_current_tab(self):
        """Get the currently active tab.

        Returns:
            BaseHairTab: The currently active tab widget, or None if no tab is selected
        """
        if self.currentIndex() < 0:
            return None
        return self.currentWidget()

    def get_hair_manager(self):
        """Get the hair manager instance.

        Returns:
            HairManager: The hair manager instance
        """
        return self._hair_manager
