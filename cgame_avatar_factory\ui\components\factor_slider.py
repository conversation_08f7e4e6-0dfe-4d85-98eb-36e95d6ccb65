# Import third-party modules
import dayu_widgets
from qtpy import Qt<PERSON>ore
from qtpy import QtWidgets


class FactorSlider(dayu_widgets.MSlider):
    """
    A Slider component support factor and float.
    """

    uniformed_value_changed = QtCore.Signal(float)

    def __init__(self, factor=1.0, orientation=QtCore.Qt.Horizontal, parent=None):
        super(FactorSlider, self).__init__(orientation, parent=parent)
        self._factor = factor
        self.valueChanged.connect(self.slot_on_orig_value_changed)

    def mouseMoveEvent(self, event):
        """Override the mouseMoveEvent to show current value as a tooltip."""
        if self._show_text_when_move:
            value_str = str(format(self.value(), ".2f"))
            QtWidgets.QToolTip.showText(event.globalPos(), value_str, self)
        return super(dayu_widgets.MSlider, self).mouseMoveEvent(event)

    def value(self):
        return super(FactorSlider, self).value() * self._factor

    def setRange(self, min_range, max_range):
        min_range = min_range / self._factor
        max_range = max_range / self._factor
        return super(FactorSlider, self).setRange(min_range, max_range)

    @QtCore.Slot(float)
    def slot_on_orig_value_changed(self, value):
        self.uniformed_value_changed.emit(round(value * self._factor, 2))
