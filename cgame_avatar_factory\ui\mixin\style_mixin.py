# Import built-in modules
import functools

# Import third-party modules
from dayu_widgets import dayu_theme

# Import local modules
from cgame_avatar_factory.ui import qt_view_utils as qt_utils


class MStyleMixin:
    """Amend instance or class to support style sheet adjustment."""

    frameless_style_sheet = "{border: none;}"
    round_border_style_sheet = "{{border-radius: {}px;}}"
    transparent_background_style_sheet = "{background-color: transparent;}"
    amend_color_style_sheet = "{{color: {};}}"
    amend_background_color_style_sheet = "{{background-color: {};}}"
    amend_padding_style_sheet = "{{padding: {};}}"
    hide_menu_indicator_style_sheet = "::menu-indicator { image: none; }"

    @staticmethod
    def add_style(instance, style, modifier=""):
        qt_utils.add_style_to_sheet(instance, style, modifier)
        instance.style().polish(instance)

    @staticmethod
    def polish(instance):
        instance.style().polish(instance)

    @staticmethod
    def frameless(instance, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.frameless_style_sheet, modifier)
        return instance

    @staticmethod
    def border_radius(instance, radius=None, modifier=""):
        radius = radius or dayu_theme.border_radius_base
        MStyleMixin.add_style(instance, MStyleMixin.round_border_style_sheet.format(radius), modifier)
        return instance

    @staticmethod
    def transparent_background(instance, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.transparent_background_style_sheet, modifier)
        return instance

    @staticmethod
    def foreground_color(instance, color, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.amend_color_style_sheet.format(color), modifier)
        return instance

    @staticmethod
    def background_color(instance, color, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.amend_background_color_style_sheet.format(color), modifier)
        return instance

    @staticmethod
    def padding(instance, padding_distance, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.amend_padding_style_sheet.format(padding_distance), modifier)
        return instance

    @staticmethod
    def hide_menu_indicator(instance, modifier=""):
        MStyleMixin.add_style(instance, MStyleMixin.hide_menu_indicator_style_sheet, modifier)
        return instance

    @staticmethod
    def cls_wrapper(cls):
        setattr(cls, "frameless", MStyleMixin.frameless)
        setattr(cls, "border_radius", MStyleMixin.border_radius)
        setattr(cls, "transparent_background", MStyleMixin.transparent_background)
        setattr(cls, "foreground_color", MStyleMixin.foreground_color)
        setattr(cls, "background_color", MStyleMixin.background_color)
        setattr(cls, "padding", MStyleMixin.padding)
        setattr(cls, "hide_menu_indicator", MStyleMixin.hide_menu_indicator)

        return cls

    @staticmethod
    def instance_wrapper(instance):
        setattr(instance, "frameless", functools.partial(MStyleMixin.frameless, instance))
        setattr(instance, "border_radius", functools.partial(MStyleMixin.border_radius, instance))
        setattr(instance, "transparent_background", functools.partial(MStyleMixin.transparent_background, instance))
        setattr(instance, "foreground_color", functools.partial(MStyleMixin.foreground_color, instance))
        setattr(instance, "background_color", functools.partial(MStyleMixin.background_color, instance))
        setattr(instance, "padding", functools.partial(MStyleMixin.padding, instance))
        setattr(instance, "hide_menu_indicator", functools.partial(MStyleMixin.hide_menu_indicator, instance))

        return instance
