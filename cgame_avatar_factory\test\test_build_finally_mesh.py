# -*- coding: utf-8 -*-
"""Test build_finally_mesh module.

此模块测试BuildFinallyMesh类的功能，包括网格创建、混合形状控制和顶点位置计算等功能。
"""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import unittest
from unittest import mock

# Import third-party modules
import numpy as np

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.build_scenes.build_finally_mesh import BuildFinallyMesh


class TestBuildFinallyMesh(unittest.TestCase):
    """BuildFinallyMesh类的测试用例

    测试网格创建、混合形状控制和顶点位置计算等功能。
    """

    @mock.patch("cgame_avatar_factory.api.dna_utils.get_mesh_name")
    @mock.patch("cgame_avatar_factory.utils.read_json")
    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.BuildFinallyMesh._analyze_data")
    def setUp(self, mock_analyze, mock_read_json, mock_get_mesh_name):
        """设置测试环境

        创建测试数据和BuildFinallyMesh实例。
        """
        # 设置模拟返回值
        mock_get_mesh_name.return_value = ["head_lod0_mesh", "eye_l_mesh", "eye_r_mesh"]
        mock_read_json.return_value = {
            "head_lod0_mesh": {
                "region1": {"0": 1.0, "1": 0.5},
            },
        }

        # 创建测试数据 - 4个源网格，每个网格包含3个子网格，每个子网格有2个顶点
        self.mesh_data = [
            {
                "head_lod0_mesh": [[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]],
                "eye_l_mesh": [[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]],
                "eye_r_mesh": [[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]],
            },
            {
                "head_lod0_mesh": [[0.0, 0.0, 0.0], [2.0, 2.0, 2.0]],
                "eye_l_mesh": [[0.0, 0.0, 0.0], [2.0, 2.0, 2.0]],
                "eye_r_mesh": [[0.0, 0.0, 0.0], [2.0, 2.0, 2.0]],
            },
            {
                "head_lod0_mesh": [[0.0, 0.0, 0.0], [3.0, 3.0, 3.0]],
                "eye_l_mesh": [[0.0, 0.0, 0.0], [3.0, 3.0, 3.0]],
                "eye_r_mesh": [[0.0, 0.0, 0.0], [3.0, 3.0, 3.0]],
            },
            {
                "head_lod0_mesh": [[0.0, 0.0, 0.0], [4.0, 4.0, 4.0]],
                "eye_l_mesh": [[0.0, 0.0, 0.0], [4.0, 4.0, 4.0]],
                "eye_r_mesh": [[0.0, 0.0, 0.0], [4.0, 4.0, 4.0]],
            },
        ]

        # 创建测试对象
        self.builder = BuildFinallyMesh(self.mesh_data)

    def test_create_mesh_arrays(self):
        """测试_create_mesh_arrays方法

        验证方法是否正确创建网格数组字典，包含所有网格和顶点数据。
        """
        # 获取结果
        result = self.builder.mesh_arrays

        # 验证结果
        self.assertEqual(len(result), 3, "应创建3个网格数组")
        self.assertIn("head_lod0_mesh", result, "应包含head_lod0_mesh")
        self.assertIn("eye_l_mesh", result, "应包含eye_l_mesh")
        self.assertIn("eye_r_mesh", result, "应包含eye_r_mesh")

        # 验证每个网格有4个数组
        for mesh_name in result:
            self.assertEqual(len(result[mesh_name]), 4, f"{mesh_name}应有4个数组")

            # 验证数组类型和内容
            for i, arr in enumerate(result[mesh_name]):
                self.assertIsInstance(arr, np.ndarray, f"{mesh_name}的数组{i}应为numpy数组")
                self.assertEqual(arr.shape, (2, 3), f"{mesh_name}的数组{i}应有2个顶点，每个顶点3个坐标")
                np.testing.assert_array_equal(arr[0], [0.0, 0.0, 0.0], f"{mesh_name}的数组{i}第一个顶点应为[0,0,0]")
                np.testing.assert_array_equal(
                    arr[1],
                    [(i + 1), (i + 1), (i + 1)],
                    f"{mesh_name}的数组{i}第二个顶点应为[{i+1},{i+1},{i+1}]",
                )

    def test_get_mesh_gap(self):
        """测试_get_mesh_gap方法

        验证方法是否正确计算网格间的差距数据。
        """
        # 获取结果
        result = self.builder.distance_mesh_data

        # 验证结果
        self.assertEqual(len(result), 3, "应有3个差距数组（T, L, R）")

        # 验证第一个差距（mesh0 - mesh1）
        mesh0t = result[0]
        self.assertIn("head_lod0_mesh", mesh0t, "T差距应包含head_lod0_mesh")
        np.testing.assert_array_equal(
            mesh0t["head_lod0_mesh"][1],
            [-1.0, -1.0, -1.0],
            "T差距的head_lod0_mesh第二个顶点应为[-1,-1,-1]",
        )

        # 验证第二个差距（mesh0 - mesh2）
        mesh0l = result[1]
        self.assertIn("head_lod0_mesh", mesh0l, "L差距应包含head_lod0_mesh")
        np.testing.assert_array_equal(
            mesh0l["head_lod0_mesh"][1],
            [-2.0, -2.0, -2.0],
            "L差距的head_lod0_mesh第二个顶点应为[-2,-2,-2]",
        )

        # 验证第三个差距（mesh0 - mesh3）
        mesh0r = result[2]
        self.assertIn("head_lod0_mesh", mesh0r, "R差距应包含head_lod0_mesh")
        np.testing.assert_array_equal(
            mesh0r["head_lod0_mesh"][1],
            [-3.0, -3.0, -3.0],
            "R差距的head_lod0_mesh第二个顶点应为[-3,-3,-3]",
        )

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_get_or_create_blend_node_existing(self, mock_cmds):
        """测试_get_or_create_blend_node方法（已存在混合节点）

        验证当混合节点已存在时，方法应返回现有节点而不创建新节点。
        """
        # 设置模拟返回值
        mock_cmds.listHistory.return_value = ["blendNode1"]
        mock_cmds.nodeType.return_value = "blendShape"

        # 调用被测试的方法
        result = self.builder._get_or_create_blend_node("head_lod0_mesh")

        # 验证结果
        self.assertEqual(result, "blendNode1", "应返回现有的混合节点名称")
        mock_cmds.listHistory.assert_called_with("head_lod0_mesh", pdo=True)
        mock_cmds.nodeType.assert_called_with("blendNode1")
        mock_cmds.blendShape.assert_not_called()

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_get_or_create_blend_node_new(self, mock_cmds):
        """测试_get_or_create_blend_node方法（创建新混合节点）

        验证当混合节点不存在时，方法应创建并返回新节点。
        """
        # 设置模拟返回值
        mock_cmds.listHistory.return_value = ["otherNode"]
        mock_cmds.nodeType.return_value = "transform"
        mock_cmds.blendShape.return_value = ["head_lod0_mesh_blendShape"]

        # 调用被测试的方法
        result = self.builder._get_or_create_blend_node("head_lod0_mesh")

        # 验证结果
        self.assertEqual(result, "head_lod0_mesh_blendShape", "应返回新创建的混合节点名称")
        mock_cmds.listHistory.assert_called_with("head_lod0_mesh", pdo=True)
        mock_cmds.nodeType.assert_called_with("otherNode")
        mock_cmds.blendShape.assert_called_with("head_lod0_mesh", name="head_lod0_mesh_blendShape", before=True)

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_get_weight_index_empty(self, mock_cmds):
        """测试_get_weight_index方法（无权重）

        验证当混合节点没有权重时，方法应返回0作为第一个权重索引。
        """
        # 设置模拟返回值
        mock_cmds.blendShape.return_value = []

        # 调用被测试的方法
        result = self.builder._get_weight_index("blendNode1")

        # 验证结果
        self.assertEqual(result, 0, "无权重时应返回索引0")
        mock_cmds.blendShape.assert_called_with("blendNode1", query=True, weight=True)

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_get_weight_index_existing(self, mock_cmds):
        """测试_get_weight_index方法（已有权重）

        验证当混合节点已有权重时，方法应返回下一个可用的权重索引。
        """
        # 设置模拟返回值
        mock_cmds.blendShape.return_value = [0.0, 0.5]

        # 调用被测试的方法
        result = self.builder._get_weight_index("blendNode1")

        # 验证结果
        self.assertEqual(result, 2, "已有2个权重时应返回索引2")
        mock_cmds.blendShape.assert_called_with("blendNode1", query=True, weight=True)

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.om2")
    def test_calculate_target_point_position(self, mock_om2, mock_cmds):
        """测试_calculate_target_point_position方法

        验证方法是否正确计算目标点位置并使用OpenMaya API设置顶点位置。
        """
        # 准备测试数据
        mesh_name = "head_lod0_mesh"
        weights = {"0": 0.0, "1": 1.0}
        distance_mesh_data = np.array([[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]])
        target_mesh = "target_mesh"

        # 模拟OpenMaya API
        mock_selection = mock.MagicMock()
        mock_om2.MSelectionList.return_value = mock_selection
        mock_dag_path = mock.MagicMock()
        mock_selection.getDagPath.return_value = mock_dag_path
        mock_mesh_fn = mock.MagicMock()
        mock_om2.MFnMesh.return_value = mock_mesh_fn
        mock_om2.MPoint = mock.MagicMock(side_effect=lambda x, y, z: (x, y, z))

        # 调用被测试的方法
        self.builder._calculate_target_point_position(mesh_name, weights, distance_mesh_data, target_mesh)

        # 验证结果 - 使用OpenMaya API
        mock_selection.add.assert_called_with(target_mesh)
        mock_selection.getDagPath.assert_called_with(0)
        mock_om2.MFnMesh.assert_called_with(mock_dag_path)
        mock_mesh_fn.setPoint.assert_called_with(1, (0.0, 0.0, 0.0))

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_create_pinch_ctrl_existing(self, mock_cmds):
        """测试当控制器已存在时create_pinch_ctrl方法的行为

        验证当MIX_CTRL_NAME已存在时，方法应该直接返回控制器名称，
        而不创建新的控制器节点。
        """
        # 设置模拟返回值 - 控制器已存在
        mock_cmds.objExists.return_value = True

        # 调用被测试的方法
        result = self.builder.create_pinch_ctrl()

        # 验证结果
        self.assertEqual(result, const.MIX_CTRL_NAME, "应返回正确的控制器名称")
        # 验证是否检查了控制器存在性
        mock_cmds.objExists.assert_any_call(const.MIX_CTRL_NAME)
        # 验证没有创建新节点
        mock_cmds.createNode.assert_not_called()

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test_create_pinch_ctrl_new(self, mock_cmds):
        """测试当控制器不存在时create_pinch_ctrl方法的行为

        验证当MIX_CTRL_NAME不存在时，方法应该创建新的控制器节点，
        设置属性，并返回新创建的控制器名称。
        """
        # 设置模拟返回值 - 控制器不存在
        mock_cmds.objExists.return_value = False
        mock_cmds.createNode.return_value = const.MIX_CTRL_NAME
        mock_cmds.attributeQuery.return_value = False

        # 模拟常量 - 注意使用正确的常量名称
        with mock.patch.dict(
            const.__dict__,
            {
                "PINCH_CTRL_ATTR": {"region1": "attr1", "region2": "attr2"},
                "SOURCE_SUFFIX": ["T", "L", "R"],
                "ATTR_LIST": [".tx", ".ty", ".tz"],
            },
        ):
            # 调用被测试的方法
            result = self.builder.create_pinch_ctrl()

            # 验证结果
            self.assertEqual(result, const.MIX_CTRL_NAME, "应返回正确的控制器名称")

            # 验证是否检查了控制器存在性
            mock_cmds.objExists.assert_any_call(const.MIX_CTRL_NAME)

            # 验证是否创建了新节点
            mock_cmds.createNode.assert_any_call("transform", name=const.MIX_CTRL_NAME)

            # 验证是否设置了属性
            mock_cmds.setAttr.assert_any_call(f"{const.MIX_CTRL_NAME}.tx", lock=True, keyable=False, channelBox=False)
            mock_cmds.setAttr.assert_any_call(
                f"{const.MIX_CTRL_NAME}.visibility",
                0,
                lock=True,
                keyable=False,
                channelBox=False,
            )

            # 验证是否添加了自定义属性
            for area_name, attr_name in const.PINCH_CTRL_ATTR.items():
                for suffix in const.SOURCE_SUFFIX:
                    full_attr = f"{attr_name}_{suffix}"
                    mock_cmds.attributeQuery.assert_any_call(full_attr, node=const.MIX_CTRL_NAME, exists=True)
                    mock_cmds.addAttr.assert_any_call(
                        const.MIX_CTRL_NAME,
                        longName=full_attr,
                        attributeType="float",
                        minValue=0,
                        maxValue=1,
                    )

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.BuildFinallyMesh.create_pinch_ctrl")
    def test_analyze_data(self, mock_create_pinch_ctrl, mock_cmds):
        """测试_analyze_data方法

        验证方法是否正确创建混合形状并连接到控制器。
        """
        # 设置模拟返回值
        mock_create_pinch_ctrl.return_value = "pinch_ctrl"

        # 模拟_get_or_create_blend_node和_create_blendshape方法
        self.builder._get_or_create_blend_node = mock.MagicMock(return_value="blend_node")
        self.builder._create_blendshape = mock.MagicMock()

        # 调用被测试的方法
        self.builder._analyze_data()

        # 验证结果
        mock_create_pinch_ctrl.assert_called_once()

        # 验证是否为每个网格和混合形状调用了_create_blendshape
        expected_calls = []
        for mesh_name, mesh_value in self.builder.vertex_weights.items():
            for blendshape_name, weights in mesh_value.items():
                expected_calls.append(mock.call(mesh_name, blendshape_name, weights, "blend_node", "pinch_ctrl"))

        self.builder._create_blendshape.assert_has_calls(expected_calls, any_order=True)
        self.assertEqual(self.builder._create_blendshape.call_count, len(expected_calls))

    @mock.patch("cgame_avatar_factory.build_scenes.build_finally_mesh.cmds")
    def test__create_blendshape(self, mock_cmds):
        """测试_create_blendshape方法

        验证方法是否正确创建混合形状目标并连接到控制器。
        """
        # 准备测试数据
        mesh_name = "head_lod0_mesh"
        blendshape_name = "forehead_blendshape"
        weights = {"0": 0.0, "1": 1.0}
        blend_node = "blend_node"
        pinch_ctrl = "pinch_ctrl"

        # 模拟常量和方法
        with mock.patch.dict(
            const.__dict__,
            {
                "SOURCE_SUFFIX": ["T"],
                "PINCH_CTRL_ATTR": {"forehead_blendshape": "forehead"},
            },
        ):
            # 模拟_get_weight_index和_calculate_target_point_position方法
            self.builder._get_weight_index = mock.MagicMock(return_value=0)
            self.builder._calculate_target_point_position = mock.MagicMock()

            # 设置模拟返回值
            mock_cmds.duplicate.return_value = ["target_mesh"]

            # 调用被测试的方法
            self.builder._create_blendshape(mesh_name, blendshape_name, weights, blend_node, pinch_ctrl)

            # 验证结果
            mock_cmds.duplicate.assert_called_with(mesh_name, name=f"{mesh_name}_{blendshape_name}_T")
            self.builder._calculate_target_point_position.assert_called_once()
            mock_cmds.blendShape.assert_called_with(blend_node, edit=True, target=(mesh_name, 0, "target_mesh", 1.0))
            mock_cmds.setAttr.assert_any_call(f"{blend_node}.w[0]", 0)
            mock_cmds.setAttr.assert_any_call("target_mesh.visibility", 0)
            mock_cmds.connectAttr.assert_called_with("pinch_ctrl.forehead_T", f"{blend_node}.w[0]")
            mock_cmds.delete.assert_called_with("target_mesh")


if __name__ == "__main__":
    unittest.main()
