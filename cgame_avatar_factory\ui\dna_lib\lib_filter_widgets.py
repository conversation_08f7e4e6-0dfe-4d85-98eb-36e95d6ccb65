# Import built-in modules
from functools import partial

# Import third-party modules
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.ui.components.info_section import BaseSectionWidget
import cgame_avatar_factory.ui.dna_lib.tag_util as tag_util


class MFilterComboButton(QtWidgets.QPushButton):
    """A QPushButton with a dropdown arrow for filter selection.

    Signals:
        sig_filter_clicked: Emitted when the button is clicked.
    """

    sig_filter_clicked = QtCore.Signal()

    def __init__(self, text="筛选", parent=None):
        """Initialize the filter combo button.

        Args:
            text (str): Button text.
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        self.setText(text)
        self.setCursor(QtCore.Qt.PointingHandCursor)
        self.setStyleSheet(
            """
            QPushButton {
                border: 1px solid #3a3a3a;
                border-radius: 4px;
                background: #3a3a3a;
                padding: 2px 24px 2px 8px;
                min-width: 150px;
                min-height: 28px;
                text-align: left;
                font-size: 9pt;
            }
            QPushButton:hover {
                border: 1px solid #1890ff;
            }
        """
        )
        self._arrow_icon = QtGui.QPixmap(16, 16)
        self._arrow_icon.fill(QtCore.Qt.transparent)
        painter = QtGui.QPainter(self._arrow_icon)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        points = [
            QtCore.QPointF(3, 6),
            QtCore.QPointF(8, 11),
            QtCore.QPointF(13, 6),
        ]
        painter.setBrush(QtGui.QBrush(QtGui.QColor("#666")))
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawPolygon(QtGui.QPolygonF(points))
        painter.end()

    def paintEvent(self, event):
        super().paintEvent(event)
        rect = self.rect()
        icon_size = 16
        icon_x = rect.right() - icon_size - 8
        icon_y = (rect.height() - icon_size) // 2
        painter = QtGui.QPainter(self)
        painter.drawPixmap(icon_x, icon_y, self._arrow_icon)
        painter.end()


class TagFilterPopupWidget(QtWidgets.QFrame):
    """Popup widget for tag filtering.

    Signals:
        sig_tags_changed (list): Emitted when the selected tags change.
    """

    sig_tags_changed = QtCore.Signal(list)

    def __init__(self, all_tags, selected_tags=None, parent=None, build_tree=True):
        """Initialize the tag filter popup.

        Args:
            all_tags (list of str): All available tags.
            selected_tags (list of str, optional): Initially selected tags.
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        self.setWindowFlags(QtCore.Qt.Popup)
        self.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.setFrameShadow(QtWidgets.QFrame.Raised)
        self.setMinimumWidth(220)
        self.setMaximumHeight(480)
        self.selected_tags = list(dict.fromkeys(selected_tags or []))
        self.all_tags = all_tags

        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        self.tree = QtWidgets.QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        layout.addWidget(self.tree)
        self.tree.setIndentation(10)
        if build_tree:
            self._build_tree()
            self._check_selected_tags(self.tree.invisibleRootItem())
        self.setStyleSheet(
            """
            TagFilterPopupWidget {
                outline: none;
                border: 1px solid #1e1e1e;
            }
        """
        )
        self.tree.setStyleSheet(
            """
            QTreeWidget {
                outline: none;
            }
            QTreeWidget:focus {
                outline: none;
                border: none;
            }
            QTreeWidget::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 1px solid #1e1e1e;
                background: #fff;
            }
            QTreeWidget::indicator:checked {
                background: #588dbe;
            }
            QTreeWidget::indicator:unchecked {
                background: #3a3a3a;
            }
            QTreeWidget::item {
                padding-top: 2px;
                padding-bottom: 2px;
    }
        """
        )
        self.tree.itemChanged.connect(self._on_item_changed)

    def _build_tree(self):
        tag_dict = {}
        for tag in self.all_tags:
            parts = tag.split("/")
            d = tag_dict
            for p in parts:
                d = d.setdefault(p, {})

        def has_leaf(d):
            if not d:
                return True
            return any(has_leaf(v) for v in d.values())

        def add_items(parent, d, prefix=""):
            for k, v in d.items():
                full_tag = k if not prefix else prefix + "/" + k
                if prefix == "" and not has_leaf(v):
                    item = QtWidgets.QTreeWidgetItem([k])
                    item.setData(0, QtCore.Qt.UserRole, full_tag)
                    item.setFlags(QtCore.Qt.ItemIsEnabled)
                    self.tree.invisibleRootItem().addChild(item)
                    continue
                item = QtWidgets.QTreeWidgetItem([k])
                item.setData(0, QtCore.Qt.UserRole, full_tag)
                if not v:
                    item.setFlags(QtCore.Qt.ItemIsUserCheckable | QtCore.Qt.ItemIsEnabled)
                    item.setCheckState(0, QtCore.Qt.Unchecked)
                else:
                    item.setFlags(QtCore.Qt.ItemIsEnabled)
                parent.addChild(item)
                add_items(item, v, full_tag)

        add_items(self.tree.invisibleRootItem(), tag_dict)

    def _check_selected_tags(self, item):
        for i in range(item.childCount()):
            child = item.child(i)
            tag = child.data(0, QtCore.Qt.UserRole)
            if tag in self.selected_tags:
                child.setCheckState(0, QtCore.Qt.Checked)
            self._check_selected_tags(child)

    def _on_item_changed(self, item, column):
        tags = []

        def collect_checked(item):
            for i in range(item.childCount()):
                child = item.child(i)
                if child.checkState(0) == QtCore.Qt.Checked and child.childCount() == 0:
                    tags.append(child.data(0, QtCore.Qt.UserRole))
                collect_checked(child)

        collect_checked(self.tree.invisibleRootItem())
        extra_tags = [t for t in self.selected_tags if t not in self.all_tags]
        tags += [t for t in extra_tags if t not in tags]
        self.selected_tags = list(dict.fromkeys(tags))
        self.sig_tags_changed.emit(tags)

    def get_selected_tags(self):
        """Get the currently selected tags.

        Returns:
            list of str: The selected tags.
        """
        tags = []

        def collect_checked(item):
            for i in range(item.childCount()):
                child = item.child(i)
                if child.checkState(0) == QtCore.Qt.Checked and child.childCount() == 0:
                    tags.append(child.data(0, QtCore.Qt.UserRole))
                collect_checked(child)

        collect_checked(self.tree.invisibleRootItem())
        return tags


class TagEditFilterPopupWidget(TagFilterPopupWidget):
    """Popup widget for editing and filtering tags, with support for user-defined tags.

    Signals:
        sig_user_tag_removed (str): Emitted when a user-defined tag is removed.
    """

    sig_user_tag_removed = QtCore.Signal(str)

    def __init__(self, all_tags, selected_tags=None, user_extra_tags=None, parent=None):
        """
        Initialize the tag edit filter popup widget.

        Args:
            all_tags (list of str): All available tags.
            selected_tags (list of str, optional): Initially selected tags.
            user_extra_tags (list of str, optional): User-defined extra tags.
            parent (QWidget, optional): Parent widget.
        """
        self.user_extra_tags = set(user_extra_tags or [])
        super().__init__(all_tags, selected_tags, parent, build_tree=False)
        try:
            self.tree.itemChanged.disconnect(self._on_item_changed)
        except TypeError:
            pass
        self.tree.setColumnCount(2)
        self.tree.setColumnWidth(1, 22)
        self._build_tree()
        self.tree.itemChanged.connect(self._on_item_changed)

    def _refresh_tree(self):
        """
        Refresh the tree widget by clearing and rebuilding it,
        and re-checking selected tags.
        """
        self.tree.clear()
        self._build_tree()
        self._check_selected_tags(self.tree.invisibleRootItem())

    def _build_tree(self):
        """
        Build the tag tree structure, including user-defined tags with remove buttons.
        """
        tag_dict = {}
        for tag in self.all_tags:
            parts = tag.split("/")
            d = tag_dict
            for p in parts:
                d = d.setdefault(p, {})

        def has_leaf(d):
            if not d:
                return True
            return any(has_leaf(v) for v in d.values())

        def add_items(parent, d, prefix=""):
            for k, v in d.items():
                full_tag = k if not prefix else prefix + "/" + k
                if prefix == "" and not has_leaf(v):
                    item = QtWidgets.QTreeWidgetItem([k, ""])
                    item.setData(0, QtCore.Qt.UserRole, full_tag)
                    item.setFlags(QtCore.Qt.ItemIsEnabled)
                    self.tree.invisibleRootItem().addChild(item)
                    continue
                item = QtWidgets.QTreeWidgetItem([k, ""])
                item.setData(0, QtCore.Qt.UserRole, full_tag)
                if not v:
                    item.setFlags(QtCore.Qt.ItemIsUserCheckable | QtCore.Qt.ItemIsEnabled)
                    item.setCheckState(0, QtCore.Qt.Unchecked)
                    parent.addChild(item)
                    if full_tag in self.user_extra_tags:
                        btn = QtWidgets.QToolButton()
                        btn.setText("X")
                        btn.setStyleSheet(
                            """
                            QToolButton {
                                border: none;
                                background: transparent;
                                color: #ff4d4f;
                                font-size: 14px;
                                padding: 0px;
                                margin-right: 12px;
                            }
                            QToolButton:hover {
                                color: #ff0000;
                            }
                        """
                        )
                        btn.setFixedSize(18, 18)
                        btn.clicked.connect(partial(self._remove_user_tag, str(full_tag)))
                        self.tree.setItemWidget(item, 1, btn)
                else:
                    item.setFlags(QtCore.Qt.ItemIsEnabled)
                    parent.addChild(item)
                    add_items(item, v, full_tag)

        add_items(self.tree.invisibleRootItem(), tag_dict)
        self._check_selected_tags(self.tree.invisibleRootItem())

    def _remove_user_tag(self, tag):
        """
        Remove a user-defined tag and refresh the tree.

        Args:
            tag (str): The tag to remove.
        """
        if tag in self.user_extra_tags:
            self.user_extra_tags.remove(tag)
        self.sig_user_tag_removed.emit(tag)
        self.all_tags = [t for t in self.all_tags if t != tag]
        try:
            self.tree.itemChanged.disconnect(self._on_item_changed)
        except TypeError:
            pass
        self._refresh_tree()
        self.tree.itemChanged.connect(self._on_item_changed)


class TagSelectSectionWidget(BaseSectionWidget):
    """Section widget for tag selection.

    Signals:
        sig_tags_changed (list): Emitted when the selected tags change.
    """

    sig_tags_changed = QtCore.Signal(list)

    def __init__(self, all_tags, section_name="标签选择", hint_text="请选择标签", selected_tags=None, parent=None):
        """Initialize the tag selection section.

        Args:
            all_tags (list of str): All available tags.
            section_name (str): Section title.
            hint_text (str): Placeholder text for input.
            selected_tags (list of str, optional): Initially selected tags.
            parent (QWidget, optional): Parent widget.
        """
        self._all_tags = all_tags
        self._hint_text = hint_text
        self.selected_tags = list(selected_tags) if selected_tags else []
        self.user_extra_tags = [t for t in self.selected_tags if t not in self._all_tags]
        super().__init__(section_name, parent=parent)

    def setup_contents(self):
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_layout.setContentsMargins(0, 0, 0, 0)
        self.contents_layout.setSpacing(0)
        self.contents_container.setLayout(self.contents_layout)

        self.box_frame = QtWidgets.QFrame()
        self.box_frame.setStyleSheet(
            """
            QFrame {
                background: #282828;
                border-radius: 6px;
            }
        """
        )
        self.box_frame.setMinimumHeight(36)
        self.box_layout = QtWidgets.QHBoxLayout()
        self.box_layout.setContentsMargins(8, 4, 8, 4)
        self.box_layout.setSpacing(4)
        self.box_frame.setLayout(self.box_layout)

        self.tag_widgets = []

        self.input_line = TagLineEdit()
        self.input_line.sig_tag_entered.connect(self.add_tag_from_input)

        self.select_btn = QtWidgets.QPushButton("选择标签")
        self.select_btn.setFixedHeight(32)
        self.select_btn.clicked.connect(self.show_tag_popup)
        self.setStyleSheet(
            """
        QPushButton {
            border: 3px solid #3E3E3E;
            border-radius: 8px;
            background-color: #3A3A3A;
            color: #CCCCCC;
            padding: 3px;
        }
        QPushButton:checked{
            background-color: #1E90FF;
            color: white;
            font-weight: bold;
        }
        QPushButton:disabled {
            border: 1px solid #323232;
            color: #565656;
        }
        QPushButton:hover {
            border-color: #5E5E5E;
            background-color: #4A4A4A;
        }
        QPushButton:pressed {
            background-color: #1A1A1A;
        }
        """
        )
        self.box_layout.addStretch(1)
        self.box_layout.addWidget(self.select_btn)

        self.contents_layout.addWidget(self.box_frame)
        self._layout.addWidget(self.contents_container)

        self.refresh_tags_area()

    def show_tag_popup(self):
        popup_tags = tag_util.get_preset_tags() + tag_util.get_user_extra_tags()
        popup = TagEditFilterPopupWidget(
            popup_tags,
            self.selected_tags,
            user_extra_tags=tag_util.get_user_extra_tags(),
            parent=self,
        )
        btn = self.select_btn
        pos = btn.mapToGlobal(QtCore.QPoint(0, btn.height()))
        popup.move(pos)
        popup.sig_tags_changed.connect(self.set_tags)
        popup.sig_user_tag_removed.connect(self.on_user_tag_removed)
        popup.show()
        popup.setFocus()

    def on_user_tag_removed(self, tag):
        tag_util.remove_user_extra_tag(tag)
        if tag in self.user_extra_tags:
            self.user_extra_tags.remove(tag)

    def set_tags(self, tags):
        """Set the selected tags, preserving order of existing tags and appending new ones at the end..

        Args:
            tags (list of str): Tags to set as selected.
        """
        old_tags = list(self.selected_tags)
        new_tags = list(tags)

        result = [tag for tag in old_tags if tag in new_tags]
        result += [tag for tag in new_tags if tag not in old_tags]

        self.selected_tags = result
        self.user_extra_tags = [t for t in self.selected_tags if t not in self._all_tags]
        self.refresh_tags_area()
        self.sig_tags_changed.emit(self.selected_tags)

    def refresh_tags_area(self):
        for w in self.tag_widgets:
            self.box_layout.removeWidget(w)
            w.setParent(None)
        self.tag_widgets.clear()

        for i, tag in enumerate(self.selected_tags):
            tag_widget = TagWidget(tag)
            tag_widget.sig_remove.connect(self.remove_tag)
            self.box_layout.insertWidget(i, tag_widget)
            self.tag_widgets.append(tag_widget)

        self.box_layout.removeWidget(self.input_line)
        self.input_line.setParent(None)
        self.box_layout.insertWidget(len(self.tag_widgets), self.input_line)
        self.input_line.setFocus()

    def add_tag_from_input(self, text):
        tags = [t.strip() for t in text.replace("，", ",").replace(" ", ",").split(",") if t.strip()]
        added = False
        for tag in tags:
            if tag and tag not in self.selected_tags:
                self.selected_tags.append(tag)
                if tag not in self._all_tags and tag not in self.user_extra_tags:
                    self.user_extra_tags.append(tag)
                    tag_util.add_user_extra_tag(tag)
                added = True
        if added:
            self.refresh_tags_area()
            self.sig_tags_changed.emit(self.selected_tags)

    def get_user_extra_tags(self):
        return self.user_extra_tags

    def set_user_extra_tags(self, tags):
        self.user_extra_tags = list(tags)

    user_extra_tags_prop = QtCore.Property(list, get_user_extra_tags, set_user_extra_tags)

    def remove_tag(self, tag):
        if tag in self.selected_tags:
            self.selected_tags.remove(tag)
            self.user_extra_tags = [t for t in self.selected_tags if t not in self._all_tags]
            self.refresh_tags_area()
            self.sig_tags_changed.emit(self.selected_tags)

    def get_tags(self):
        return self.selected_tags

    tags = QtCore.Property(list, get_tags, set_tags)


class TagWidget(QtWidgets.QWidget):
    sig_remove = QtCore.Signal(str)

    def __init__(self, tag, parent=None):
        super().__init__(parent)
        self.setObjectName("TagWidget")

        outer_layout = QtWidgets.QHBoxLayout(self)
        outer_layout.setContentsMargins(0, 0, 0, 0)
        outer_layout.setSpacing(0)

        container = QtWidgets.QWidget(self)
        container.setObjectName("TagContainer")
        container_layout = QtWidgets.QHBoxLayout(container)
        container_layout.setContentsMargins(8, 2, 8, 2)
        container_layout.setSpacing(2)

        label = QtWidgets.QLabel(tag)
        label.setStyleSheet("color: white;background: transparent;")

        close_btn = QtWidgets.QToolButton()
        close_btn.setText("X")
        close_btn.setStyleSheet(
            """
            QToolButton {
                border: none;
                background: transparent;
                color: #d9d9d9;
                font-size: 16px;
                padding: 0px;
                margin: 0px;
            }
            QToolButton:hover {
                color: #ff4d4f;
            }
        """
        )
        close_btn.setFixedSize(24, 24)
        close_btn.clicked.connect(lambda: self.sig_remove.emit(tag))

        container_layout.addWidget(label)
        container_layout.addWidget(close_btn)
        container_layout.setAlignment(close_btn, QtCore.Qt.AlignVCenter)

        container.setStyleSheet(
            """
            QWidget#TagContainer {
                background: #3a3a3a;
                border: 1px solid #3a3a3a;
                border-radius: 12px;

            }
        """
        )

        outer_layout.addWidget(container)
        outer_layout.setAlignment(container, QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)


class TagLineEdit(QtWidgets.QLineEdit):
    """Line edit for tag input.

    Signals:
        sig_tag_entered (str): Emitted when a tag is entered.
        sig_edit_finished (str): Emitted when editing is finished.
    """

    sig_tag_entered = QtCore.Signal(str)
    sig_edit_finished = QtCore.Signal(str)

    def __init__(self, text="", parent=None, editing=False):
        super().__init__(text, parent)
        self.setFixedHeight(28)
        self.setStyleSheet(
            """
            QLineEdit {
                background: #3a3a3a;
                border: 1px solid #3a3a3a;
                border-radius: 12px;
                padding: 2px 8px;
                color: #d9d9d9;
                font-size: 16px;
            }
            QLineEdit:focus {
                border: 1px solid #3a3a3a;
            }
        """
        )
        self.editing = editing
        if not editing:
            self.setPlaceholderText("输入后,回车添加")
        self.setFocus()

    def keyPressEvent(self, event):
        if event.key() in (QtCore.Qt.Key_Return, QtCore.Qt.Key_Enter, QtCore.Qt.Key_Comma, QtCore.Qt.Key_Space):
            text = self.text().strip()
            if text:
                if self.editing:
                    self.sig_edit_finished.emit(text)
                else:
                    self.sig_tag_entered.emit(text)
                self.clear()
                if self.editing:
                    self.deleteLater()
            else:
                super().keyPressEvent(event)
        elif event.key() == QtCore.Qt.Key_Escape and self.editing:
            self.deleteLater()
        else:
            super().keyPressEvent(event)


class TagEditDialog(QtWidgets.QDialog):
    """Dialog for editing tags."""

    def __init__(self, all_tags, common_tags, windowtitle="编辑标签", parent=None):
        """Initialize the tag edit dialog.

        Args:
            all_tags (list of str): All available tags.
            common_tags (list of str): Default selected tags.
            windowtitle (str): Dialog window title.
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        self.setWindowTitle(windowtitle)
        self.resize(660, 220)
        self.selected_tags = list(common_tags)

        layout = QtWidgets.QVBoxLayout(self)

        self.tag_section = TagSelectSectionWidget(
            all_tags=all_tags,
            section_name="标签选择",
            selected_tags=common_tags,
            parent=self,
        )
        layout.addWidget(self.tag_section)

        btn_layout = QtWidgets.QHBoxLayout()
        btn_layout.addStretch()
        ok_btn = QtWidgets.QPushButton("确定")
        cancel_btn = QtWidgets.QPushButton("取消")
        btn_layout.addWidget(ok_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        ok_btn.clicked.connect(self.accept)
        cancel_btn.clicked.connect(self.reject)

    def get_selected_tags(self):
        return self.tag_section.get_tags()
