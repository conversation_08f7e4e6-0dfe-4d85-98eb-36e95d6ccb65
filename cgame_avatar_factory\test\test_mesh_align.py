# -*- coding: utf-8 -*-
"""Test mesh_align module.

这个模块包含了对mesh_align.py中各个函数的单元测试，确保它们按预期工作。
测试覆盖了刚体变换计算、变换应用、顶点位置获取、区域顶点调整以及网格对齐等功能。
"""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import built-in modules
import unittest
from unittest import mock

# Import third-party modules
import numpy as np

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.api import mesh_align


class TestMeshAlign(unittest.TestCase):
    """测试 mesh_align 模块中的函数。

    这个测试类包含了对 mesh_align.py 中各个函数的单元测试，确保它们按预期工作。
    测试覆盖了刚体变换计算、变换应用、顶点位置获取、区域顶点调整以及网格对齐等功能。
    """

    def setUp(self):
        """在每个测试方法运行前设置测试环境。"""
        # 设置常用的测试数据
        self.test_vertices = np.array(
            [
                [0.0, 0.0, 0.0],
                [1.0, 0.0, 0.0],
                [0.0, 1.0, 0.0],
                [0.0, 0.0, 1.0],
            ],
        )

        # 常用的变换矩阵
        self.identity_matrix = np.eye(4)
        self.translation_matrix = np.eye(4)
        self.translation_matrix[:3, 3] = [1.0, 2.0, 3.0]

    def test_compute_rigid_transform_translation(self):
        """测试 compute_rigid_transform 函数处理平移变换。"""
        # 创建平移的目标点
        translation = np.array([1.0, 2.0, 3.0])
        tgt_points = self.test_vertices + translation

        # 计算变换矩阵
        transform = mesh_align.compute_rigid_transform(self.test_vertices, tgt_points)

        # 验证变换矩阵是4x4的
        self.assertEqual(transform.shape, (4, 4))

        # 验证变换矩阵的平移部分 - 注意源点到目标点的变换是负的
        np.testing.assert_array_almost_equal(transform[:3, 3], [-1.0, -2.0, -3.0])

        # 验证变换矩阵的旋转部分接近单位矩阵
        np.testing.assert_array_almost_equal(transform[:3, :3], np.eye(3), decimal=5)

        # 验证变换矩阵的最后一行是[0, 0, 0, 1]
        np.testing.assert_array_almost_equal(transform[3, :], [0, 0, 0, 1])

    def test_compute_rigid_transform_rotation(self):
        """测试 compute_rigid_transform 函数处理旋转变换。"""
        # 创建旋转矩阵（绕Z轴旋转90度）
        rotation_z = np.array(
            [
                [0.0, -1.0, 0.0],
                [1.0, 0.0, 0.0],
                [0.0, 0.0, 1.0],
            ],
        )

        # 应用旋转到测试顶点
        tgt_points = np.array([np.dot(rotation_z, v) for v in self.test_vertices])

        # 计算变换矩阵
        transform = mesh_align.compute_rigid_transform(self.test_vertices, tgt_points)

        # 验证变换矩阵的旋转部分接近我们的旋转矩阵的转置
        np.testing.assert_array_almost_equal(transform[:3, :3], rotation_z.T, decimal=5)

        # 验证变换矩阵的行列式接近1（保持体积）
        det = np.linalg.det(transform[:3, :3])
        self.assertAlmostEqual(abs(det), 1.0, places=5)

    def test_apply_transform_translation(self):
        """测试 apply_transform 函数应用平移变换。"""
        # 创建测试数据
        mesh_data = {
            "mesh1": [
                [1.0, 0.0, 0.0],
                [0.0, 1.0, 0.0],
                [0.0, 0.0, 1.0],
            ],
        }

        # 应用变换
        transformed_mesh = mesh_align.apply_transform(mesh_data, self.translation_matrix)

        # 验证结果
        expected_vertices = [
            [2.0, 2.0, 3.0],  # [1,0,0] + [1,2,3]
            [1.0, 3.0, 3.0],  # [0,1,0] + [1,2,3]
            [1.0, 2.0, 4.0],  # [0,0,1] + [1,2,3]
        ]

        for i, vertex in enumerate(transformed_mesh["mesh1"]):
            np.testing.assert_array_almost_equal(vertex, expected_vertices[i])

    def test_apply_transform_multiple_meshes(self):
        """测试 apply_transform 函数处理多个网格。"""
        # 创建包含多个网格的测试数据
        mesh_data = {
            "mesh1": [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            "mesh2": [[0.0, 0.0, 1.0], [1.0, 1.0, 1.0]],
        }

        # 应用变换
        transformed_mesh = mesh_align.apply_transform(mesh_data, self.translation_matrix)

        # 验证结果 - 检查所有网格都被正确变换
        self.assertEqual(len(transformed_mesh), 2)
        self.assertEqual(len(transformed_mesh["mesh1"]), 2)
        self.assertEqual(len(transformed_mesh["mesh2"]), 2)

        # 验证第一个网格的变换
        np.testing.assert_array_almost_equal(transformed_mesh["mesh1"][0], [2.0, 2.0, 3.0])
        np.testing.assert_array_almost_equal(transformed_mesh["mesh1"][1], [1.0, 3.0, 3.0])

        # 验证第二个网格的变换
        np.testing.assert_array_almost_equal(transformed_mesh["mesh2"][0], [1.0, 2.0, 4.0])
        np.testing.assert_array_almost_equal(transformed_mesh["mesh2"][1], [2.0, 3.0, 4.0])

    def test_get_point_positions_list(self):
        """测试 get_point_positions 函数处理列表数据。"""
        # 创建测试数据 - 使用列表
        mesh_data = [
            [1.0, 0.0, 0.0],  # 索引0
            [0.0, 1.0, 0.0],  # 索引1
            [0.0, 0.0, 1.0],  # 索引2
        ]

        vertex_list = [0, 2]

        # 获取点位置
        point_positions = mesh_align.get_point_positions(mesh_data, vertex_list)

        # 验证结果
        self.assertEqual(len(point_positions), 2)
        np.testing.assert_array_equal(point_positions[0], [1.0, 0.0, 0.0])
        np.testing.assert_array_equal(point_positions[1], [0.0, 0.0, 1.0])

    @mock.patch("cgame_avatar_factory.api.mesh_align.cmds")
    def test_get_point_positions_maya(self, mock_cmds):
        """测试 get_point_positions 函数处理Maya对象。"""
        # 设置模拟返回值
        mock_cmds.xform.side_effect = [
            [1.0, 0.0, 0.0],  # 第一次调用返回
            [0.0, 0.0, 1.0],  # 第二次调用返回
        ]

        # 调用函数
        point_positions = mesh_align.get_point_positions("pCube1", [0, 2])

        # 验证结果
        self.assertEqual(len(point_positions), 2)
        np.testing.assert_array_equal(point_positions[0], [1.0, 0.0, 0.0])
        np.testing.assert_array_equal(point_positions[1], [0.0, 0.0, 1.0])

        # 验证cmds.xform被正确调用
        mock_cmds.xform.assert_any_call("pCube1.vtx[0]", q=True, t=True, ws=True)
        mock_cmds.xform.assert_any_call("pCube1.vtx[2]", q=True, t=True, ws=True)

    def test_get_point_positions_invalid_source(self):
        """测试 get_point_positions 函数处理无效数据源。"""
        # 使用无效的数据源类型（整数）
        invalid_source = 123
        vertex_list = [0, 1]

        # 验证函数抛出正确的异常
        with self.assertRaises(ValueError):
            mesh_align.get_point_positions(invalid_source, vertex_list)

    @mock.patch("cgame_avatar_factory.api.mesh_align.utils.read_json")
    @mock.patch("cgame_avatar_factory.api.mesh_align.os.path.exists")
    def test_region_face_vertex_amend(self, mock_exists, mock_read_json):
        """测试 region_face_vertex_amend 函数的基本功能。

        这个测试模拟了权重和中心顶点数据，验证函数是否正确处理区域顶点调整。
        """
        # 设置模拟返回值
        mock_exists.return_value = True

        # 模拟权重数据 - 包含一个区域的两个顶点的权重
        mock_weights = {
            const.BASE_HEAD_MESH_NAME: {
                "region1": {
                    "0": 1.0,  # 完全权重
                    "1": 0.5,  # 部分权重
                },
            },
        }

        # 模拟中心顶点数据 - 指定区域的中心顶点索引
        mock_central_vertices = {
            "region1": 2,
        }

        # 设置读取JSON的返回值顺序
        mock_read_json.side_effect = [mock_weights, mock_central_vertices]

        # 创建测试数据 - 4个模型（基础模型和3个目标模型）
        all_mesh_data = [
            # 基础模型（索引0）
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 0.0], 1: [1.0, 1.0, 1.0], 2: [2.0, 2.0, 2.0]}},
            # 目标模型1（索引1）
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 0.0], 1: [1.0, 1.0, 1.0], 2: [3.0, 3.0, 3.0]}},
            # 目标模型2（索引2）
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 0.0], 1: [1.0, 1.0, 1.0], 2: [4.0, 4.0, 4.0]}},
            # 目标模型3（索引3）
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 0.0], 1: [1.0, 1.0, 1.0], 2: [5.0, 5.0, 5.0]}},
        ]

        # 模拟区域到模型的映射
        with mock.patch.dict(const.REGION_TO_MODEL_MAP, {"region1": "model1"}):
            # 添加区域模型数据到每个目标模型
            for i in range(1, 4):
                all_mesh_data[i]["model1"] = [[1.0, 1.0, 1.0], [2.0, 2.0, 2.0]]

            # 保存原始数据的深度副本以便后续比较
            # Import built-in modules
            import copy

            copy.deepcopy(all_mesh_data)

            # 调用被测试的函数
            result = mesh_align.region_face_vertex_amend(all_mesh_data)

            # 验证函数返回的是原始数据的引用（原地修改）
            self.assertEqual(id(result), id(all_mesh_data))

            # 验证函数调用
            mock_exists.assert_called()
            self.assertEqual(mock_read_json.call_count, 2)  # 调用两次读取不同的JSON文件

            # 验证数据被正确修改 - 由于模拟环境，我们不能确保数据一定会改变
            # 但我们可以确保函数被正确调用
            # 1. 检查区域模型是否存在
            for i in range(1, 4):
                self.assertIn("model1", result[i])

            # 2. 检查基础网格是否存在
            for i in range(1, 4):
                self.assertIn(const.BASE_HEAD_MESH_NAME, result[i])

            # 3. 检查顶点是否存在
            for i in range(1, 4):
                self.assertIn(0, result[i][const.BASE_HEAD_MESH_NAME])
                self.assertIn(1, result[i][const.BASE_HEAD_MESH_NAME])

    @mock.patch("cgame_avatar_factory.api.mesh_align.utils.read_json")
    @mock.patch("cgame_avatar_factory.api.mesh_align.os.path.exists")
    def test_region_face_vertex_amend_empty_region(self, mock_exists, mock_read_json):
        """测试 region_face_vertex_amend 函数处理空区域的情况。"""
        # 设置模拟返回值
        mock_exists.return_value = True

        # 模拟权重数据 - 包含一个空区域
        mock_weights = {
            const.BASE_HEAD_MESH_NAME: {
                "empty_region": {},
            },
        }

        # 模拟中心顶点数据 - 不包含空区域
        mock_central_vertices = {}

        # 设置读取JSON的返回值顺序
        mock_read_json.side_effect = [mock_weights, mock_central_vertices]

        # 创建测试数据
        all_mesh_data = [
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 0.0]}},
            {const.BASE_HEAD_MESH_NAME: {0: [1.0, 1.0, 1.0]}},
            {const.BASE_HEAD_MESH_NAME: {0: [2.0, 2.0, 2.0]}},
            {const.BASE_HEAD_MESH_NAME: {0: [3.0, 3.0, 3.0]}},
        ]

        # 保存原始数据的副本
        # Import built-in modules
        import copy

        original_data = copy.deepcopy(all_mesh_data)

        # 调用被测试的函数
        result = mesh_align.region_face_vertex_amend(all_mesh_data)

        # 验证函数返回的是原始数据的引用
        self.assertEqual(id(result), id(all_mesh_data))

        # 验证数据没有被修改（因为区域不存在或为空）
        for i in range(len(all_mesh_data)):
            self.assertEqual(original_data[i][const.BASE_HEAD_MESH_NAME][0], result[i][const.BASE_HEAD_MESH_NAME][0])

    @mock.patch("cgame_avatar_factory.api.mesh_align.get_point_positions")
    @mock.patch("cgame_avatar_factory.api.mesh_align.compute_rigid_transform")
    @mock.patch("cgame_avatar_factory.api.mesh_align.apply_transform")
    @mock.patch("cgame_avatar_factory.api.mesh_align.region_face_vertex_amend")
    def test_start_align(self, mock_amend, mock_apply, mock_compute, mock_get_points):
        """测试 start_align 函数的工作流程。

        这个测试验证 start_align 函数是否正确调用了各个子函数，并按预期处理网格对齐。
        """
        # 设置模拟返回值
        mock_get_points.side_effect = [
            [[1.0, 0.0, 0.0]],  # a_points
            [[0.0, 1.0, 0.0]],  # b_points
            [[0.0, 0.0, 1.0]],  # c_points
            [[1.0, 1.0, 1.0]],  # d_points
        ]

        # 模拟计算的变换矩阵为单位矩阵
        mock_compute.return_value = np.eye(4)

        # 模拟 apply_transform 函数返回原始数据
        mock_apply.side_effect = lambda x, _: x

        # 模拟 region_face_vertex_amend 函数返回一个标记值
        mock_amend.return_value = "amended_data"

        # 创建测试数据 - 4个模型的网格数据
        mesh_data = [
            {const.BASE_HEAD_MESH_NAME: {0: [1.0, 0.0, 0.0]}},  # 模型a
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 1.0, 0.0]}},  # 模型b
            {const.BASE_HEAD_MESH_NAME: {0: [0.0, 0.0, 1.0]}},  # 模型c
            {const.BASE_HEAD_MESH_NAME: {0: [1.0, 1.0, 1.0]}},  # 模型d
        ]

        # 调用被测试的函数
        result = mesh_align.start_align(mesh_data)

        # 验证结果
        self.assertEqual(result, "amended_data")

        # 验证函数调用次数和顺序
        # 1. get_point_positions 应该被调用四次，获取四个模型的对应顶点
        self.assertEqual(mock_get_points.call_count, 4)

        # 2. compute_rigid_transform 应该被调用三次，计算三个变换矩阵
        self.assertEqual(mock_compute.call_count, 3)

        # 3. apply_transform 应该被调用三次，应用三个变换
        self.assertEqual(mock_apply.call_count, 3)

        # 4. region_face_vertex_amend 应该被调用一次，处理最终的网格数据
        mock_amend.assert_called_once()

        # 验证函数调用参数
        # 验证 get_point_positions 的调用参数
        mock_get_points.assert_any_call(mesh_data[0][const.BASE_HEAD_MESH_NAME], const.CORRESPONDENCE_INDICES)

        # 验证 compute_rigid_transform 被调用
        self.assertTrue(mock_compute.called, "compute_rigid_transform 应该被调用")

        # 验证 region_face_vertex_amend 的调用参数
        mock_amend.assert_called_once_with(mesh_data)


if __name__ == "__main__":
    unittest.main()
