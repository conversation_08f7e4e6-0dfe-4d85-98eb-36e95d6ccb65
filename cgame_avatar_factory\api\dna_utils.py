# Import built-in modules
import os
import re

# Import third-party modules
from dna import BinaryStreamReader
from dna import BinaryStreamWriter
from dna import CoordinateSystem
from dna import DataLayer_All
from dna import DataLayer_Definition
from dna import FileStream
from dna import Status
from dna_viewer import DNA
from dna_viewer import RigConfig
from dna_viewer import build_meshes
from dna_viewer.builder.mesh import Mesh
from maya import OpenMaya

# Import Maya modules
import maya.cmds as cmds

# Import local modules
import cgame_avatar_factory.constants as const
from cgame_avatar_factory.merge import mesh_util


class TemplateDna:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TemplateDna, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self._initialized = False

        if self._initialized:
            return

        self._path = os.path.join(const.CONFIG_PATH, const.BASE_DNA_PATH)
        self._reader = None
        self._mesh_name_list = None
        self._load_data()
        self._initialized = True

    def _load_data(self):
        self._reader = load_dna_calib(self._path)
        self._mesh_name_list = get_mesh_name(self._path)

    @property
    def path(self):
        return self._path

    @property
    def reader(self):
        return self._reader

    @property
    def mesh_name_list(self):
        return self._mesh_name_list

    @classmethod
    def get_mesh_name(cls):
        return cls().mesh_name_list

    @classmethod
    def get_reader(cls):
        return cls().reader


def run_joints_command(reader, writer):
    """Update joint transforms in DNA file

    Gets current joint translations and rotations from Maya
    and updates them in the DNA writer.

    Args:
        writer: DNA writer object
        reader: DNA reader object
    """
    joint_translations = []
    joint_rotations = []
    for i in range(reader.getJointCount()):
        joint_name = reader.getJointName(i)
        all_bones = cmds.ls(type="joint")
        if joint_name in all_bones:
            translation = cmds.xform(joint_name, query=True, translation=True)
            rotation = cmds.joint(joint_name, query=True, orientation=True)
            joint_translations.append(translation)
            joint_rotations.append(rotation)
        else:
            logging.warning(f"Joint {joint_name} does not exist, skipping connection")
            joint_translations.append([0, 0, 0])
            joint_rotations.append([0, 0, 0])

    writer.setNeutralJointTranslations(joint_translations)
    writer.setNeutralJointRotations(joint_rotations)


def init_face_dna():
    dna_path = os.path.join(const.CONFIG_PATH, const.FACE_RIG_DNA)
    output_dna_path = mesh_util.create_workspace_dna_dir(const.FACE_OUTPUT_DNA_NAME)
    reader = load_dna_calib(dna_path)
    writer = init_writer_from_reader(output_dna_path, reader)
    run_joints_command(reader, writer)
    writer.write()
    return output_dna_path


def load_dna_calib(dna_file: str, layer=DataLayer_All):
    """Load DNA data from file

    Args:
        dna_file: Path to DNA file
        layer: Data layer to load

    Returns:
        BinaryStreamReader: DNA reader instance

    Raises:
        RuntimeError: If DNA loading fails
    """
    stream = FileStream(dna_file, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
    reader = BinaryStreamReader(stream, layer)
    reader.read()
    if not Status.isOk():
        status = Status.get()
        raise RuntimeError("Error loading DNA: {}".format(status.message))
    return reader


def init_writer_from_reader(path, reader=None):
    """Save DNA data to file

    Args:
        reader: DNA reader instance
        path: Save path

    Returns:
        BinaryStreamWriter: DNA writer instance
    """
    stream = FileStream(
        path,
        FileStream.AccessMode_Write,
        FileStream.OpenMode_Binary,
    )
    writer = BinaryStreamWriter(stream)
    if reader != None:
        writer.setFrom(reader)
    return writer


def build_base_mesh(dna: DNA, config=None):
    """Build base mesh

    Args:
        dna: DNA instance
        config: Mesh build configuration
    """
    build_meshes(dna=dna, config=config)


def import_all_skin_clusters(dna_path: str):
    """Import all skin clusters

    Args:
        dna_path: DNA file path
    """
    dna = DNA(dna_path)

    config = RigConfig(
        add_skin_cluster=True,
    )

    for mesh_index in range(len(TemplateDna.get_mesh_name())):
        mesh_builder = Mesh(
            config=config,
            dna=dna,
            mesh_index=mesh_index,
        )
        mesh_builder.add_skin_cluster()


def get_mesh_name(dna_path: str):
    """Get list of mesh names

    Args:
        dna_path: DNA file path

    Returns:
        list: List of mesh names
    """
    reader = load_dna_calib(dna_path, DataLayer_Definition)
    name_list = []
    for index in range(reader.getMeshCount()):
        name_list.append(reader.getMeshName(index))
    return name_list


def get_mesh_index(dna_path: str):
    """Get list of mesh indices

    Args:
        dna_path: DNA file path

    Returns:
        list: List of mesh indices
    """
    reader = load_dna_calib(dna_path, DataLayer_Definition)
    index_list = []
    for index in range(reader.getMeshCount()):
        index_list.append(index)
    return index_list


def write_mesh(output_path, mesh_names):
    """Write meshes to DNA file.

    Args:
        output_path (str): Path to output DNA file.
        mesh_names(List):List of meshes
    Returns:
        list: List of mesh names written, or empty list if any mesh missing.
    """
    meshes = []
    for mesh in mesh_names:
        if not cmds.objExists(mesh):
            cmds.warning(f"Mesh {mesh} does not exist!")
            return []
        meshes.append(mesh)
    DNAMeshWriter(
        lods=[0],
        meshs={0: meshes},
        output_path=output_path,
    )
    return meshes


def get_coord_system():
    """Get CoordinateSystem.
    Returns:
        CoordinateSystem.
    """
    up_axis = cmds.upAxis(query=True, axis=True)  # 返回 'x', 'y' 或 'z'
    # Set coordinate system
    coord_system = CoordinateSystem()
    coord_system.xAxis = 0  # X axis direction (usually 0=Right)
    if up_axis == "z":
        # Z up: x=0, y=4, z=2
        coord_system.yAxis = 4
        coord_system.zAxis = 2
    elif up_axis == "y":
        # Y up: x=0, y=2, z=4
        coord_system.yAxis = 2
        coord_system.zAxis = 4
    else:
        # 其他情况，默认使用Y up
        coord_system.yAxis = 2
        coord_system.zAxis = 4
    return coord_system


class DNAMeshWriter:
    """Writes mesh data to DNA file format.

    Handles writing mesh geometry, topology, and attributes to DNA file.
    """

    def __init__(self, lods: list, meshs: dict, output_path: str):
        """Initialize DNA writer.

        Args:
            lods (list): List of LOD levels.
            meshs (dict): Dictionary mapping LOD levels to mesh names.
            output_path (str): Output DNA file path.

        Returns:
            None
        """
        self.output_path = output_path
        self.meshs = meshs
        self.lods = lods
        self.writer = init_writer_from_reader(self.output_path)
        self.writer.setCoordinateSystem(get_coord_system())
        self.set_mesh_indices()
        self.write_all_meshes()
        self.writer.write()

    def write_all_meshes(self):
        """Write all meshes to DNA file.

        Returns:
            None
        """
        mesh_index = 0
        self.writer.setLODCount(len(self.lods))
        for lod, mesh_list in self.meshs.items():
            for mesh in mesh_list:
                self.write_mesh_to_dna(mesh, mesh_index)
                mesh_index += 1

    def write_mesh_to_dna(self, mesh_name: str, mesh_index: int):
        """Write mesh data to DNA file.

        Args:
            mesh_name (str): Name of mesh to write.
            mesh_index (int): Index of mesh in DNA file.

        Returns:
            None
        """
        self.set_mesh_name(mesh_index, mesh_name)
        self.get_vertex_layouts(mesh_name, mesh_index)
        self.set_vertex_positions(mesh_index, self.get_vertex_positions(mesh_name))
        self.set_vertex_normals(mesh_index, self.get_vertex_normals(mesh_name))
        self.set_vertex_uvs(mesh_index, self.get_vertex_uvs(mesh_name))
        self.get_face_layouts(mesh_index, mesh_name)

    def get_vertex_layouts(self, mesh_name: str, mesh_index: int):
        """Get vertex layouts for mesh.

        Args:
            mesh_name (str): Name of mesh.
            mesh_index (int): Index of mesh in DNA file.

        Returns:
            list: List of vertex layouts.
        """
        layout = []
        uv_count = cmds.polyEvaluate(mesh_name, uv=True)
        position_list = []
        for uv_id in range(uv_count):
            _layout = []
            vtx_list = cmds.polyListComponentConversion(
                f"{mesh_name}.map[{uv_id}]",
                fromUV=True,
                toVertex=True,
            )
            vtx_id = int(re.search(r"\[(\d+)\]", vtx_list[0]).group(1))
            _layout.append(vtx_id)
            _layout.append(uv_id)
            _layout.append(uv_id)
            layout.append(_layout)
            position_list.append(vtx_id)
        self.set_vertex_layouts(mesh_index, layout)
        return position_list

    def get_vertex_positions(self, mesh_name: str):
        """Get vertex positions for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex positions.
        """
        vertices_position = []
        vertices = cmds.ls(f"{mesh_name}.vtx[*]", fl=True)
        for vertex in vertices:
            position = cmds.pointPosition(vertex, world=True)
            vertices_position.append([position[0], position[1], position[2]])
        return vertices_position

    def get_vertex_normals(self, mesh_name: str):
        """Get vertex normals for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex normals.
        """
        normals = []
        vertex_count = cmds.polyEvaluate(mesh_name, vertex=True)
        for i in range(vertex_count):
            vertex_normals = cmds.polyNormalPerVertex(f"{mesh_name}.vtx[{i}]", query=True, xyz=True)
            if vertex_normals is None:
                cmds.warning(f"No normals found for vertex {i} of mesh {mesh_name}.")
                continue
            avg_normal = [sum(vertex_normals[i::3]) / (len(vertex_normals) / 3) for i in range(3)]
            normals.append(avg_normal)
        return normals

    def get_vertex_uvs(self, mesh_name: str):
        """Get vertex UVs for mesh.

        Args:
            mesh_name (str): Name of mesh.

        Returns:
            list: List of vertex UVs.
        """
        uvs = []
        uv_list = cmds.ls(f"{mesh_name}.map[*]", flatten=True)
        for uv in uv_list:
            uv_position = cmds.polyEditUV(uv, query=True)
            uvs.append([uv_position[0], uv_position[1]])
        return uvs

    def get_face_layouts(self, mesh_index: int, mesh_name: str):
        """Get face layouts for mesh.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            mesh_name (str): Name of mesh.

        Returns:
            None
        """
        sel = OpenMaya.MSelectionList()
        sel.add(mesh_name)
        mesh_obj = OpenMaya.MObject()
        sel.getDependNode(0, mesh_obj)

        face_itr = OpenMaya.MItMeshPolygon(mesh_obj)

        while not face_itr.isDone():
            face_vertices = []

            for i in range(face_itr.polygonVertexCount()):
                uv_id = OpenMaya.MScriptUtil()
                uv_id.createFromInt(0)
                uv_id_ptr = uv_id.asIntPtr()
                face_itr.getUVIndex(i, uv_id_ptr)
                layout_idx = OpenMaya.MScriptUtil(uv_id_ptr).asInt()
                face_vertices.append(layout_idx)

            self.set_face_layouts(mesh_index, face_itr.index(), face_vertices)
            face_itr.next()

    def set_mesh_name(self, mesh_index: int, name: str):
        """Set mesh name in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            name (str): Name of mesh.

        Returns:
            None
        """
        self.writer.setMeshName(mesh_index, name)

    def set_mesh_indices(self):
        """Set mesh indices in DNA file.

        Returns:
            None
        """
        mesh_indices = {}
        current_index = 0
        for lod in sorted(self.meshs.keys()):
            mesh_indices[lod] = []
            for mesh in self.meshs[lod]:
                mesh_indices[lod].append(current_index)
                current_index += 1

        for lod in sorted(self.meshs.keys()):
            self.writer.setLODMeshMapping(lod, lod)
            self.writer.setMeshIndices(lod, mesh_indices[lod])

    def set_face_layouts(self, mesh_index: int, face_index: int, face_layouts: list):
        """Set face layouts in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            face_index (int): Index of face in mesh.
            face_layouts (list): List of face layouts.

        Returns:
            None
        """
        self.writer.setFaceVertexLayoutIndices(mesh_index, face_index, face_layouts)

    def set_joint_hierarchy(self, hierarchy_indices: list):
        """Set joint hierarchy in DNA file.

        Args:
            hierarchy_indices (list): List of joint hierarchy indices.

        Returns:
            None
        """
        self.writer.setJointHierarchy(hierarchy_indices)

    def set_vertex_layouts(self, mesh_index: int, layouts: list):
        """Set vertex layouts in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            layouts (list): List of vertex layouts.

        Returns:
            None
        """
        self.writer.setVertexLayouts(mesh_index, layouts)

    def set_vertex_positions(self, mesh_index: int, positions: list):
        """Set vertex positions in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            positions (list): List of vertex positions.

        Returns:
            None
        """
        self.writer.setVertexPositions(mesh_index, positions)

    def set_vertex_normals(self, mesh_index: int, normals: list):
        """Set vertex normals in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            normals (list): List of vertex normals.

        Returns:
            None
        """
        self.writer.setVertexNormals(mesh_index, normals)

    def set_vertex_uvs(self, mesh_index: int, uvs: list):
        """Set vertex UVs in DNA file.

        Args:
            mesh_index (int): Index of mesh in DNA file.
            uvs (list): List of vertex UVs.

        Returns:
            None
        """
        self.writer.setVertexTextureCoordinates(mesh_index, uvs)
