# Import built-in modules
import functools

# Import third-party modules
import dayu_widgets
from qtpy import Qt<PERSON><PERSON>
from qtpy import QtGui
from qtpy import QtWidgets
from qtpy.QtCore import Qt

# Import local modules
import cgame_avatar_factory.constants as const
from cgame_avatar_factory.ui.components.folder_line_edit import FolderLineEdit
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.components.layout import FramelessVLayout


class BaseSectionWidget(QtWidgets.QWidget):
    def __init__(self, section_name, parent=None):
        super(BaseSectionWidget, self).__init__(parent=parent)
        self._section_name = section_name
        self._object_name = const.PACKAGE_NAME + section_name
        self.setup_ui()

    def setup_ui(self):
        self.setObjectName(self._object_name)
        self.setup_layout()
        self.setup_widgets()
        self.setup_signals()
        self.setup_style()

    def setup_layout(self):
        self._layout = QtWidgets.QVBoxLayout()
        self.setLayout(self._layout)

    def setup_widgets(self):
        self.setup_divider()
        self.setup_contents()

    def setup_divider(self):
        self.divider = dayu_widgets.MDivider(self._section_name)
        self._layout.addWidget(self.divider)

    def setup_contents(self):
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_container.setLayout(self.contents_layout)
        self._layout.addWidget(self.contents_container)

    def setup_signals(self):
        pass

    def setup_style(self):
        pass


class FileSelectSectionWidget(BaseSectionWidget):
    sig_file_select_button_clicked = QtCore.Signal()
    sig_file_path_changed = QtCore.Signal(str)

    def __init__(self, section_name="File Selection", hint_text=None, parent=None):
        self._hint_text = hint_text or "Select a file"
        super(FileSelectSectionWidget, self).__init__(section_name, parent=parent)

    def setup_contents(self):
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_container.setLayout(self.contents_layout)

        self.setup_folder_line_edit()

        self._layout.addWidget(self.contents_container)

    def setup_folder_line_edit(self):
        self.folder_line_edit = FolderLineEdit().folder()
        self.folder_line_edit.setPlaceholderText(self._hint_text)
        self.contents_layout.addWidget(self.folder_line_edit)

    def setup_signals(self):
        self.folder_line_edit.sig_file_select_button_clicked.connect(self.sig_file_select_button_clicked)
        self.folder_line_edit.editingFinished.connect(self.slot_file_editing_finished)

    def setup_style(self):
        self.setSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        super(FileSelectSectionWidget, self).setup_style()

    def set_file_path(self, file_path):
        self.folder_line_edit.setText(file_path)
        self.slot_file_editing_finished()

    def get_file_path(self):
        return self.folder_line_edit.text()

    file_path = QtCore.Property(str, get_file_path, set_file_path)

    @QtCore.Slot()
    def slot_file_editing_finished(self):
        if self.folder_line_edit.text():
            self.sig_file_path_changed.emit(self.folder_line_edit.text())


class ThumbnailPreviewWidget(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.label = QtWidgets.QLabel(self)
        layout = QtWidgets.QVBoxLayout()
        layout.addWidget(self.label)
        self.setLayout(layout)

    def set_image(self, path):
        image = QtGui.QImage(path)
        if image.isNull():
            return

        if image.width() != image.height():
            size = min(image.width(), image.height())
            image = image.copy((image.width() - size) / 2, (image.height() - size) / 2, size, size)

        pixmap = QtGui.QPixmap.fromImage(image)
        self.label.setPixmap(pixmap.scaled(self.size(), QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        url = event.mimeData().urls()[0]
        self.set_image(url.toLocalFile())

    def resizeEvent(self, event):
        pixmap = self.label.pixmap()
        if pixmap is not None:
            self.label.setPixmap(pixmap.scaled(self.size(), QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))


class ThumbnailSelectSectionWidget(FileSelectSectionWidget):
    def setup_contents(self):
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_container.setLayout(self.contents_layout)

        self.setup_folder_line_edit()
        self.setup_thumbnail_preview()

        self._layout.addWidget(self.contents_container)

    def setup_thumbnail_preview(self):
        self.thumbnail_preview = ThumbnailPreviewWidget()
        self.thumbnail_preview.setFixedSize(100, 100)
        self.contents_layout.addWidget(self.thumbnail_preview)


class InfoSectionWidget(BaseSectionWidget):
    def __init__(self, info_config, section_name="Info", parent=None):
        self._info_config = info_config  # 根据该config生成info填充页面
        self._info = {}  # 存储填充过的info
        super(InfoSectionWidget, self).__init__(section_name=section_name, parent=parent)

    def setup_contents(self):
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_container.setLayout(self.contents_layout)

        self.setup_multi_info_widgets()

        self._layout.addWidget(self.contents_container)

    def setup_multi_info_widgets(self):
        self.multi_info_widgets_container = QtWidgets.QFrame()
        self.multi_info_widgets_layout = QtWidgets.QVBoxLayout()
        self.multi_info_widgets_container.setLayout(self.multi_info_widgets_layout)

        for key, value in self._info_config.items():
            # 对于必填项，修改label以进行提示
            if not value.get("required", False):
                hint_label_text = value.get("hint_label", "Please fill:")
            else:
                hint_label_text = "{}(必填)".format(value.get("hint_label", "Please fill:"))

            # 设置提示文本
            hint_text = value.get("hint_text", "Please fill...")
            label = dayu_widgets.MLabel(hint_label_text)

            # 根据不同的widget_type设置不同的widget
            widget_type = value.get("widget_type", "text")
            if widget_type == "lineedit":
                widget_container = QtWidgets.QFrame()
                widget_container_layout = FramelessVLayout()
                widget_container.setLayout(widget_container_layout)
                widget = dayu_widgets.MLineEdit().search()
                widget.setPlaceholderText(hint_text)
                widget.textChanged.connect(functools.partial(self.update_info, key))

                widget_container_layout.addWidget(label)
                widget_container_layout.addWidget(widget)

            elif widget_type == "combobox":
                widget_container = QtWidgets.QFrame()
                widget_container_layout = FramelessVLayout()
                widget_container.setLayout(widget_container_layout)

                menu = dayu_widgets.MMenu(parent=self)
                menu.set_data(value.get("options"))
                widget = dayu_widgets.MComboBox()
                widget.set_menu(menu)
                widget.setPlaceholderText(hint_text)
                widget.sig_value_changed.connect(functools.partial(self.update_info, key))

                widget_container_layout.addWidget(label)
                widget_container_layout.addWidget(widget)

            elif widget_type == "checkbox":
                widget_container = QtWidgets.QFrame()
                widget_container_layout = FramelessHLayout()
                widget_container.setLayout(widget_container_layout)

                widget = dayu_widgets.MCheckBox()
                widget.setCheckable(True)
                widget.stateChanged.connect(
                    lambda state, k=key: self.update_info(
                        k,
                        state in (Qt.Checked, Qt.PartiallyChecked),
                    ),
                )
                widget.setToolTip(hint_text)

                widget_container_layout.addWidget(label)
                widget_container_layout.addStretch(1)
                widget_container_layout.addWidget(widget)

            value["label"] = label
            value["widget"] = widget
            self.set_widget_to_default(key)

            self.contents_layout.addWidget(widget_container)
            self.contents_layout.addSpacing(5)
        self.contents_layout.addStretch(1)

    @property
    def info_config(self):
        for key, value in self._info_config.items():
            if value.get("required", False):
                if key not in self._info or not self._info[key]:
                    raise ValueError("{}\n未填写".format(value["hint_label"]))
        return self._info

    @property
    def info_keys(self):
        return self._info_config.keys()

    def set_widget_to_default(self, key):
        widget_type = self._info_config[key].get("widget_type", "text")
        widget = self._info_config[key].get("widget")
        default_value = self._info_config[key].get("default", "")
        if widget:
            if widget_type == "lineedit":
                if default_value:
                    widget.setText(str(default_value))
            elif widget_type == "combobox":
                if default_value:
                    widget.set_value(default_value)
            elif widget_type == "checkbox":
                widget.setChecked(default_value)
            self._info.update({key: default_value})

    @QtCore.Slot(str, object)
    def update_info(self, key, value):
        self._info.update({key: value})

        self.blockSignals(True)
        for k, v in self._info_config.items():
            if k == key:
                widget_type = v.get("widget_type", "text")
                if widget_type == "lineedit":
                    value = str(value)
                    v["widget"].setText(value)
                    break
                elif widget_type == "combobox":
                    v["widget"].set_value(value)
                    break
                elif widget_type == "checkbox":
                    if isinstance(value, str):
                        value = True if value == "True" else False
                    v["widget"].setChecked(value)
                    break
        self.blockSignals(False)


class DoubleCheckInfoMixin:
    def setup_multi_info_widgets(self):
        super().setup_multi_info_widgets()

        for key, value in self._info_config.items():
            if value.get("double_check", False):
                widget_type = value.get("widget_type", "text")
                dc_state = value.get("dc_state", False)
                dc_label = value.get("dc_label", "")
                if widget_type == "checkbox":
                    widget = value["widget"]
                    widget.stateChanged.connect(
                        lambda state, s=dc_state, l=dc_label: self.slot_checkbox_double_check(
                            state in (Qt.Checked, Qt.PartiallyChecked),
                            s,
                            l,
                        ),
                    )

    def slot_checkbox_double_check(self, value, dc_status, dc_label):
        # 如果前置条件满足，则进行二次确认
        # NOTE: 可以是dialog也可以是toast
        if value == dc_status:
            dayu_widgets.MToast.warning(dc_label, self)


class PreconditionsInfoMixin:
    def setup_multi_info_widgets(self):
        super().setup_multi_info_widgets()

        for key, value in self._info_config.items():
            preconditions = value.get("preconditions", None)
            precon_config = self._info_config.get(preconditions, None)
            if preconditions and precon_config:
                precon_state = value.get("precon_state")
                pre_widget = precon_config["widget"]
                widget = value["widget"]
                if precon_config.get("widget_type") == "checkbox":
                    precon_current_value = pre_widget.isChecked()
                    if precon_current_value != precon_state:
                        widget.setEnabled(False)
                    pre_widget.stateChanged.connect(
                        lambda state, s=precon_state, w=widget, k=key: self.slot_preconditions_checkbox_changed(
                            state in (Qt.Checked, Qt.PartiallyChecked),
                            s,
                            w,
                            k,
                        ),
                    )

    def slot_preconditions_checkbox_changed(self, value, preconditions_status, widget, key):
        if value == preconditions_status:
            widget.setEnabled(True)
        else:
            widget.setEnabled(False)
            self.set_widget_to_default(key)


class DCPreconInfoSection(DoubleCheckInfoMixin, PreconditionsInfoMixin, InfoSectionWidget):
    """支持二次确认以及前置条件的组件"""


class FileMultSelectSectionWidget(BaseSectionWidget):
    """Widget for selecting files with multiple options.

    Supports radio buttons with optional path selectors and callbacks.

    Signals:
        sig_file_select_button_clicked (): Emitted when file select button is clicked.
        sig_path_changed (str): Emitted when path text changes.
    """

    sig_file_select_button_clicked = QtCore.Signal()
    sig_path_changed = QtCore.Signal(str)

    def __init__(
        self,
        section_name="文件选择",
        options=None,
        default_index=0,
        parent=None,
    ):
        """Initialize the file multi-select section widget.

        Args:
            section_name (str): The name of the section.
            options (list[dict]): List of option dicts. Each dict can be:
                {
                    "text": str,
                    "callback": callable,  # called when selected
                }
                or
                {
                    "text": str,
                    "path_selector": bool,  # show path selector when selected
                    "hint_text": str,       # placeholder text for path selector
                }
            default_index (int): Default selected radio button index.
            parent (QWidget): Parent widget.
        """
        self._options = options or []
        self._default_index = default_index
        self._radio_buttons = []
        self._callbacks = []
        self._path_widgets = []
        super(FileMultSelectSectionWidget, self).__init__(section_name, parent=parent)

    def setup_widgets(self):
        self.setup_header()
        self.setup_contents()

    def setup_header(self):
        self.header_label = dayu_widgets.MLabel(self._section_name)
        self._layout.addWidget(self.header_label)

    def setup_contents(self):
        """Set up the UI contents of the widget."""
        self.contents_container = QtWidgets.QFrame(self)
        self.contents_layout = QtWidgets.QVBoxLayout()
        self.contents_container.setLayout(self.contents_layout)

        # 单选按钮组
        self.button_group = QtWidgets.QButtonGroup(self)
        h_layout = QtWidgets.QHBoxLayout()
        self.contents_layout.addLayout(h_layout)

        for idx, opt in enumerate(self._options):
            rb = QtWidgets.QRadioButton(opt.get("text", f"选项{idx + 1}"))
            rb.setStyleSheet(
                """
                QRadioButton::indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 6px;
                }
                QRadioButton::indicator::unchecked {
                    border: 1px solid white;
                    background-color: transparent;
                }
                QRadioButton::indicator::checked {
                    border: 1px solid white;
                    background-color: white;
                }
            """
            )
            self.button_group.addButton(rb, idx)
            self._radio_buttons.append(rb)
            h_layout.addWidget(rb)
            # 记录回调
            self._callbacks.append(opt.get("callback"))
            # 路径选择控件
            if opt.get("path_selector"):
                path_widget = FolderLineEdit().folder()
                path_widget.setPlaceholderText(opt.get("hint_text", "请选择文件"))
                path_widget.setVisible(False)
                self._path_widgets.append(path_widget)
                self.contents_layout.setSpacing(10)
                self.contents_layout.addWidget(path_widget)
                # 信号转发
                path_widget.sig_file_select_button_clicked.connect(self.sig_file_select_button_clicked)
                path_widget.editingFinished.connect(
                    lambda idx=idx, w=path_widget: self._on_path_changed(idx, w),
                )
            else:
                self._path_widgets.append(None)

        self._layout.addWidget(self.contents_container)

    def setup_signals(self):
        """Connect signals to slots."""
        self.button_group.buttonToggled.connect(self.on_radio_changed)
        # 继承父类信号
        super(FileMultSelectSectionWidget, self).setup_signals()

    def setup_style(self):
        """Set up widget style and size policy."""
        self.setSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        super(FileMultSelectSectionWidget, self).setup_style()

    def on_radio_changed(self, button, checked):
        """Handle radio button toggled event.

        Args:
            button (QRadioButton): The toggled radio button.
            checked (bool): Whether the button is checked.
        """
        if not checked:
            return
        idx = self.button_group.id(button)
        for w in self._path_widgets:
            if w:
                w.setVisible(False)
        if self._path_widgets[idx]:
            self._path_widgets[idx].setVisible(True)

    def _on_path_changed(self, idx, widget):
        """Handle path text changed event.

        Args:
            idx (int): Index of the option.
            widget (QLineEdit): The path input widget.
        """
        if widget.text():
            self.sig_path_changed.emit(widget.text())

    def set_mode(self, index):
        """Set the selected radio button by index.

        Args:
            index (int): Index to select.
        """
        if 0 <= index < len(self._radio_buttons):
            self._radio_buttons[index].setChecked(True)

    def get_mode(self):
        """Get the index of the selected radio button.

        Returns:
            int: Selected index, or -1 if none selected.
        """
        for i, rb in enumerate(self._radio_buttons):
            if rb.isChecked():
                return i
        return -1

    def get_mode_content(self):
        """Get the content of the selected radio button.

        Returns:
            str: Selected radio button contnet, or None if none selected.
        """
        for i, rb in enumerate(self._radio_buttons):
            if rb.isChecked():
                return rb.text()
        return None

    def set_file_path(self, file_path, index=None):
        """Get the file path for a given option.

        Args:
            index (int, optional): Index of the option. Defaults to current selected.

        Returns:
            str: The file path or callback result, or empty string if none.
        """
        if index is None:
            index = self.get_mode()
        if 0 <= index < len(self._path_widgets) and self._path_widgets[index]:
            self._path_widgets[index].setText(file_path)
            self.set_mode(index)

    def get_file_path(self, index=None):
        """Get the file path for a given option.

        Args:
            index (int, optional): Index of the option. Defaults to current selected.

        Returns:
            str: The file path or callback result, or empty string if none.
        """
        if index is None:
            index = self.get_mode()
        if self._callbacks[index]:
            return self._callbacks[index]()
        if 0 <= index < len(self._path_widgets) and self._path_widgets[index]:
            return self._path_widgets[index].text()

        return ""

    def showEvent(self, event):
        """Handle widget show event to initialize default selection.

        Args:
            event (QShowEvent): Show event.
        """
        self.set_mode(self._default_index)
        super(FileMultSelectSectionWidget, self).showEvent(event)
