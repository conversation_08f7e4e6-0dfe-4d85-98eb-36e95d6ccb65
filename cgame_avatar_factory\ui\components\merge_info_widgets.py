# Import built-in modules
import importlib
import uuid

# Import third-party modules
import dayu_widgets
from dayu_widgets.mixin import hover_shadow_mixin
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
import cgame_avatar_factory.ui.dna_merge_ring.circle_ui

importlib.reload(cgame_avatar_factory.ui.dna_merge_ring.circle_ui)
# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.ui.components.layout import FramelessHLayout
from cgame_avatar_factory.ui.dna_merge_ring.circle_ui import DNACircleSlotWidget
from cgame_avatar_factory.ui.mixin.style_mixin import MStyleMixin


@MStyleMixin.cls_wrapper
@hover_shadow_mixin
class MergeInfoBar(QtWidgets.QFrame):
    sig_clicked = QtCore.Signal(str)

    def __init__(self, dna_infos, area_weights):
        super().__init__()
        valid_dna_keys = ["thumbnail_file_path", "dna_file_path", "dna_order"]
        self.dna_infos = [{key: item[key] for key in valid_dna_keys} for item in dna_infos]
        self.area_weights = area_weights
        self._component_cls = DNACircleSlotWidget
        # NOTE: in maya, name must start from letter, and can not contain some special characters
        self.uuid = "{}_{}".format(const.NAMESPACE_PREFIX, str(uuid.uuid4()).replace("-", "_"))
        self.components = []
        self.setup_ui()
        self.setMinimumHeight(120)
        self.background_color(const.DAYU_BG_IN_COLOR).border_radius(5)

    def setup_ui(self):
        self.main_layout = FramelessHLayout()
        self.main_layout.setContentsMargins(30, 10, 30, 10)
        self.setLayout(self.main_layout)

        self.setup_tbn_area(self.main_layout)
        self.main_layout.addStretch(1)
        self.setup_functional_area(self.main_layout)

    def setup_tbn_area(self, parent_layout):
        self.tbn_layout = FramelessHLayout()
        for data in self.dna_infos:
            tbn_widgets = self._component_cls(dna_data=data, radius=40, image_path=data.get("thumbnail_file_path"))
            self.tbn_layout.addWidget(tbn_widgets)
            self.tbn_layout.addSpacing(10)
            self.components.append(tbn_widgets)
        parent_layout.addLayout(self.tbn_layout)

    def setup_functional_area(self, parent_layout):
        self.functional_layout = FramelessHLayout()
        self.functional_layout.setSpacing(5)

        self.remove_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.remove_btn.text_beside_icon().svg("remove.svg").small()
        self.remove_btn.transparent_background(modifier=":disabled")
        self.remove_btn.setText(" 移除")
        self.functional_layout.addWidget(self.remove_btn)

        self.edit_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.edit_btn.text_beside_icon().svg("edit.svg").small()
        self.edit_btn.transparent_background(modifier=":disabled")
        self.edit_btn.setText(" 编辑")
        self.functional_layout.addWidget(self.edit_btn)

        self.export_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.export_btn.text_beside_icon().svg("export.svg").small()
        self.export_btn.transparent_background(modifier=":disabled")
        self.export_btn.setText(" 导出")
        self.functional_layout.addWidget(self.export_btn)

        parent_layout.addLayout(self.functional_layout)

    @property
    def merge_data(self):
        return {"dna_infos": self.dna_infos, "area_weights": self.area_weights}

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.sig_clicked.emit(str(self.uuid))
