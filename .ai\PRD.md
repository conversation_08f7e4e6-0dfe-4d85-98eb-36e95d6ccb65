项目: cgame_avatar_factory

## 项目背景：

### 开发环境
    maya+pyqt的工具开发；maya版本支持2022和2023；
    可以使用dayu_widget组件，源码路径为D:\_thm\rez_local_cache\ext\dayu_widgets\0.13.15\python-3\site-packages\dayu_widgets
    使用rez打包，属于内部项目，package里面可查看引用包；请尽量使用引用包，如有外部引用需要，额外提出并说明原因；

### UI开发指南
UI开发指南：E:\AI-Memory\# Maya + PyQt + dayu_widget 开发指南.md

## 语言要求
1. 代码及其注释使用英文；
2. 与我回复对话框这些使用中文；

## AI每次必读
### 回复准则
1. 每次回答结束时，需要简要总结你的操作，重点提出不确定或者没有解决的问题；
2. 每次回答结束时，明确哪些是修改的功能，哪些文件只是测试文件，是否需要删除；

### 已有的测试调试流程
 毛发工作室开发环境使用指南：C:\TAHub\bs_merge\cgame_avatar_factory\.ai\毛发工作室开发环境使用指南.md


## 需求概述
整体毛发tab的ui设计如图所示：
![alt text](毛发模块ui设计.png)
该需求是工具下的一个tab模块在main_window.py setup_side_tab_bar 290行中间组装；我希望毛发模块可以嵌入这个main_window页面，同时要保持本模块的独立，尽量不依赖其他模块；

核心功能如下：
    - 毛发模块ui设计：包含三个tab（插片、XGen、曲线）；下面有三个布局区域：毛发编辑区、毛发组件列表、毛发素材库；
        - 每切换一个tab，毛发素材库会刷新对应要求的数据；
        - 毛发素材库要求支持搜索功能(根据数据类型搜索)；后续也要支持右键编辑数据标签等功能；
        - 用户可以从素材库中拖拽一个数据，进入毛发组件列表，默认选中该数据；
            - 这里拖拽以后，会触发maya_api一些操作；
        - 同时组件列表只支持选中一个数据，选中后，编辑区会更新一些数据；

    - 毛发素材数据后续需要支持导入、导出、修改、删除、存储；
    - 用户当前拖拽的组件列表，以及每个组件列表的数据，在毛发编辑区中的一些编辑数据，要考虑后续的导入和导出流程；
    
## 当前任务

## 确认的框架设计-实现具体细节时最好参考这部分，如果需要修改，提出并描述原因
### 1. UI 框架设计

#### 1.1 主界面结构
```
HairStudioTab (QTabWidget)
├── CardTab (QWidget)
├── XgenTab (QWidget)
└── CurveTab (QWidget)

每个Tab包含: 具体也可参考设计图 ![alt text](毛发模块ui设计.png)
├── EditorArea (QWidget)      # 左侧编辑区
├── ComponentList (QListWidget) # 中间组件列表
└── AssetLibrary (QWidget)    # 右侧素材库
    ├── SearchBar (QLineEdit)  # 搜索栏
    ├── FilterBar (QToolBar)   # 过滤工具栏
    └── AssetGrid (QGridLayout) # 资产网格
```

#### 1.2 组件职责
- **HairStudioTab**: 主容器，管理三个子标签页
- **BaseHairTab**: 基础标签页抽象类，定义公共接口
- **EditorArea**: 编辑区域，根据选中组件显示对应编辑控件
- **ComponentList**: 显示已添加的毛发组件
- **AssetLibrary**: 显示可用的毛发资源，支持搜索和过滤

### 2. 数据结构设计

#### 2.1 核心类

```python
class HairAsset:
    """毛发资源基类"""
    def __init__(self, asset_id, name, asset_type, metadata=None):
        self.id = asset_id
        self.name = name
        self.type = asset_type  # "card", "xgen", "curve"
        self.metadata = metadata or {}
        self.tags = []
        self.thumbnail = None
        self.file_path = ""

class HairComponent:
    """毛发组件实例"""
    def __init__(self, asset, component_id=None):
        self.id = component_id or str(uuid.uuid4())
        self.asset_id = asset.id
        self.name = asset.name
        self.type = asset.type
        self.parameters = {}  # 可编辑参数
        self.maya_nodes = []  # 关联的Maya节点
        self.transform = {}   # 变换数据

class HairProject:
    """毛发项目数据"""
    def __init__(self):
        self.components = []  # 当前使用的组件
        self.metadata = {}    # 项目元数据
        self.version = "1.0"
```

### 3. 代码管理模式

#### 3.1 模块结构
```
hair_studio/
├── __init__.py
├── ui/
│   ├── __init__.py
│   ├── hair_studio_tab.py     # 主标签页
│   ├── base_hair_tab.py       # 基础标签页
│   ├── editor_area.py         # 编辑区域
│   ├── component_list.py      # 组件列表
│   └── asset_library/         # 素材库
│       ├── __init__.py
│       ├── asset_library.py
│       └── asset_item.py
├── data/
│   ├── __init__.py
│   ├── models.py             # 数据模型
│   └── repository.py         # 数据存储
├── manager/
│   ├── __init__.py
│   └── hair_manager.py       # 业务逻辑
└── maya_api/
    ├── __init__.py
    └── hair_operations.py    # Maya操作封装
```

#### 3.2 设计模式应用

1. **MVC/MVVM架构**
   - Model: `HairAsset`, `HairComponent`, `HairProject`
   - View: 所有UI组件
   - Controller/ViewModel: `HairManager`

2. **观察者模式**
   - 用于UI组件间的通信
   - 当选中项变化时通知相关组件更新

3. **工厂模式**
   - 创建不同类型的毛发组件
   - 创建不同类型的编辑器

4. **策略模式**
   - 不同毛发类型的处理策略
   - 不同导出/导入策略

### 4. 关键流程

#### 4.1 添加新组件
1. 用户从素材库拖拽资源到组件列表
2. 创建新的`HairComponent`实例
3. 调用Maya API创建对应节点
4. 更新UI显示

#### 4.2 数据持久化
1. 使用JSON格式保存项目数据
2. 资源文件使用相对路径存储
3. 支持增量保存

### 5. 下一步计划

#### 阶段一：UI 集成与基本功能 (当前阶段)
1. 将 `HairStudioTab` 集成到 `main_window.py` 中
   - 导入并实例化 `HairStudioTab`
   - 替换占位符为实际组件
   - 确保标签页切换功能正常

2. 实现基本数据流
   - 创建 `HairManager` 基础类
   - 实现资源加载和缓存
   - 设置组件管理的基本结构

#### 阶段二：核心功能开发
1. 实现拖放功能
   - 从素材库拖拽到组件列表
      - 数据方面：
         - 组件列表中，每一个资产包含数据：is_viewed, icon, name这几个数据用于在组件列表中显示；其他背后数据沿用从素材库的数据；is_viewed需要给出接口，有统一管理的模块，后续用于maya api模块触发功能；
         - 组件列表会存储当前列表数据，需要支持后续的导出、导入json等数据记录功能；
      - UI交互方面：
         - 素材库中选中一个资产时，需要高亮显示；
         - 只有鼠标释放时，该素材才会被显示到组件列表中；
         - 素材库和组件列表都是单选模式；
   - 组件重排序
   - 组件选择与删除

2. 完善编辑器区域
   - 实现属性编辑功能
   - 支持不同类型毛发的特定属性
   - 实时预览更新

#### 阶段三：Maya 集成
1. 实现 Maya 操作接口
   - 创建 `hair_operations.py`
   - 实现节点创建和更新
   - 错误处理与日志记录

2. 数据持久化
   - 组件数据序列化
   - 项目文件保存/加载
   - 自动保存功能

#### 阶段四：高级功能与优化
1. 右键菜单与快捷操作
   - 添加上下文菜单
   - 常用操作快捷键
   - 批量操作支持

2. 性能优化
   - 资源加载优化
   - 视图更新优化
   - 内存管理

#### 阶段五：测试与文档
1. 单元测试
   - 数据模块测试
   - UI 功能测试
   - Maya 操作模拟测试

2. 文档编写
   - 用户手册
   - API 文档
   - 开发指南


## 代码设计要求
1. 必须ui组件和数据分离，各个模块化独立；
2. 设计模式，尽量使用面向对象的设计模式；


## 修改范围

### 涉及其他模块代码：
    - cgame_avatar_factory\ui\main_window.py文件中进行组装毛发tab
    - cgame_avatar_factory\launch.py启动窗口代码
    - cgame_avatar_factory\ui\dna_lib资产库的代码可参考引用其实现
    
### 其他仓库可借鉴代码：
 @todo

## 代码结构
实现过程中涉及的文件自行分类到对应模块；
需要区分模块，如：
hair_studio
    ui # ui组件代码
    maya_api # maya_api代码
    data # 数据
    manager # 管理
    utils # 工具函数
    config # 配置文件
    plugin # 插件
    __init__.py # 模块入口


## 代码规范
- 注释使用英文
- 代码遵循谷歌规范，文件头和函数注释需要补充完善
- python2代码风格
- 代码中避免出现魔法数字、魔法字符串，如果需要可以定义为常量；
