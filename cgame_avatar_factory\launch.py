# Import built-in modules
import sys

# Import third-party modules
from lightbox_log import init_logger

# Import local modules
from cgame_avatar_factory import core
import cgame_avatar_factory.constants as const
from cgame_avatar_factory.constants import PACKAGE_NAME


def module_cleanup(module_name):
    """Cleanup module_name in sys.modules cache.

    Args:
        module_name (str, ModuleType): Module Name

    Raises:
        TypeError: invalid module_name
    """
    if module_name in sys.builtin_module_names:
        return

    pred = "%s." % module_name
    packages = [mod for mod in sys.modules if mod.startswith(pred)]
    packages += [module_name]
    for package in packages:
        module = sys.modules.get(package)
        if module is not None:
            del sys.modules[package]  # noqa:WPS420


def debug_launch_window():
    module_cleanup(PACKAGE_NAME)
    launch_window()


def launch_window():
    # Import third-party modules
    from dayu_widgets import dayu_theme
    from dayu_widgets.qt import application
    from lightbox_ui.banner import setup_banner

    # Import local modules
    from cgame_avatar_factory import style
    from cgame_avatar_factory.constants import BANNER_BUTTON
    from cgame_avatar_factory.ui.main_window import MainWidget

    init_logger(PACKAGE_NAME)

    style.set_global_style()
    dayu_theme.set_theme("dark")
    dayu_theme.set_primary_color(dayu_theme.blue)

    with application():
        main_window = MainWidget()
        dayu_theme.apply(main_window)

        setup_banner(parent=main_window, embed_type="banner", button=BANNER_BUTTON)
        main_window.show()
        with core.get_reporter_instance() as api:
            api.report_count(
                event_name="start",
                action="tool started from shelf",
                tool_name=const.PACKAGE_NAME,
            )
    return main_window
