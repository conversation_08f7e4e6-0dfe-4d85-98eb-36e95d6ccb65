# Import built-in modules
import datetime
import logging
import os
import traceback

# Import third-party modules
import maya.cmds as cmds

# Import local modules
from cgame_avatar_factory import constants as const
from cgame_avatar_factory.merge import mesh_util as mesh


def get_images_dir():
    workspace = mesh.get_maya_workspace()
    images_dir = os.path.join(workspace, "images")
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)
    return images_dir


def _get_camera_shape(camera):
    """Get camera shape node

    Args:
        camera: Camera node name

    Returns:
        str: Camera shape node name, or None if it doesn't exist
    """
    if not camera:
        return None

    camera_shape = camera
    if not cmds.objectType(camera) == "camera":
        camera_shapes = cmds.listRelatives(camera, shapes=True) or []
        camera_shape = camera_shapes[0] if camera_shapes else None

    return camera_shape


def _get_valid_model_panel():
    """Get valid model panel

    Returns:
        str: Valid model panel name, or None if not found
    """
    panel_name = cmds.getPanel(withFocus=True)
    if not cmds.getPanel(typeOf=panel_name) == "modelPanel":
        model_panels = cmds.getPanel(type="modelPanel")
        panel_name = model_panels[0] if model_panels else None
    return panel_name


def _center_camera_on_model(camera):
    """Center camera on model

    Args:
        camera: Camera node name
    """
    cmds.select(clear=True)

    focus_object = None
    if cmds.objExists(const.BAK_HEAD_MESH_NAME):
        focus_object = const.BAK_HEAD_MESH_NAME
    elif cmds.objExists(const.BASE_HEAD_MESH_NAME):
        focus_object = const.BASE_HEAD_MESH_NAME

    if focus_object:
        cmds.select(focus_object)
        cmds.viewFit(camera, animate=False)
        return

    visible_meshes = cmds.ls(type="mesh", visible=True)
    if not visible_meshes:
        return

    mesh_transforms = cmds.listRelatives(visible_meshes, parent=True, fullPath=True) or []
    if mesh_transforms:
        cmds.select(mesh_transforms)
        cmds.viewFit(camera, animate=False)


def _adjust_camera_zoom(camera, zoom_factor):
    """Adjust camera zoom

    Args:
        camera: Camera node name
        zoom_factor: Zoom factor
    """
    if zoom_factor == 1.0 or not camera:
        return

    camera_shape = _get_camera_shape(camera)
    if not camera_shape:
        return

    current_fov = cmds.camera(camera_shape, query=True, horizontalFieldOfView=True)
    cmds.camera(camera_shape, edit=True, horizontalFieldOfView=current_fov / zoom_factor)


def _capture_viewport_image(file_name, size):
    """Capture viewport image

    Args:
        file_name: File name
        size: Image size

    Returns:
        str: Processed image path
    """
    images_dir = get_images_dir()
    image_path = os.path.join(images_dir, file_name)
    maya_image_path = image_path.replace("\\", "/")

    cmds.setAttr("defaultRenderGlobals.imageFormat", 32)
    cmds.playblast(
        frame=cmds.currentTime(query=True),
        format="image",
        compression="png",
        quality=100,
        widthHeight=size,
        viewer=False,
        showOrnaments=False,
        filename=maya_image_path,
        offScreen=True,
        percent=100,
        clearCache=True,
    )

    dir_path = os.path.dirname(image_path)
    base_name = os.path.basename(file_name)
    final_path = f"{image_path}.png"
    result_path = image_path

    if os.path.exists(dir_path):
        files = [f for f in os.listdir(dir_path) if f.startswith(base_name)]
        if files:
            actual_path = os.path.join(dir_path, files[0])
            if os.path.exists(actual_path) and not os.path.exists(final_path):
                try:
                    os.rename(actual_path, final_path)
                    result_path = final_path
                except:
                    result_path = actual_path
            else:
                result_path = actual_path
    return result_path


def capture_maya_viewport(size=(512, 512), zoom_factor=1.0, center_model=True):
    """Capture Maya viewport image

    Args:
        size: Image size, defaults to (512, 512)
        zoom_factor: Zoom factor, defaults to 1.0
        center_model: Whether to center camera on model, defaults to True

    Returns:
        str: Captured image path, or None if failed
    """
    try:
        panel_name = _get_valid_model_panel()
        if not panel_name:
            return None

        camera = cmds.modelPanel(panel_name, query=True, camera=True)

        if center_model:
            _center_camera_on_model(camera)
            _adjust_camera_zoom(camera, zoom_factor)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"maya_viewport_{timestamp}"
        image_path = _capture_viewport_image(file_name, size)

        if camera:
            camera_shape = _get_camera_shape(camera)
            if camera_shape:
                cmds.camera(camera_shape, edit=True, focalLength=65)

        return image_path
    except Exception as e:
        error_msg = f"视口截图捕获失败: {type(e).__name__}: {str(e)}"
        traceback.print_exc()

        logger = logging.getLogger(__name__)
        logger.error(error_msg)
        logger.error("异常堆栈:", exc_info=True)

        return None


def get_viewport_screenshot(size=(128, 128), zoom_factor=1.9, center_model=True):
    return capture_maya_viewport(size=size, zoom_factor=zoom_factor, center_model=center_model)
