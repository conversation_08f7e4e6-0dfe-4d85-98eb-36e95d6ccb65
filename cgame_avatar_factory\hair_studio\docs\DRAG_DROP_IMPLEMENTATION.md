# Hair Studio Drag & Drop Implementation

## Overview

This document describes the implementation of drag and drop functionality in the Hair Studio module, allowing users to drag assets from the Asset Library to the Component List to create new hair components.

## Architecture

### Components Involved

1. **AssetItem** (`asset_item.py`) - Drag Source
2. **ComponentList** (`component_list.py`) - Drop Target
3. **HairManager** (`hair_manager.py`) - Data Management

### Data Flow

```
AssetItem (Drag Source)
    ↓ (Mouse Drag)
JSON Asset Data via QMimeData
    ↓ (Drop Event)
ComponentList (Drop Target)
    ↓ (Create Component)
HairManager.create_component()
    ↓ (Update UI)
Component Added to List
```

## Implementation Details

### AssetItem (Drag Source)

#### Key Features:
- **Drag Initiation**: Detects mouse drag distance threshold
- **Visual Feedback**: Creates semi-transparent drag pixmap
- **Data Transfer**: Serializes asset data as JSON in QMimeData
- **MIME Types**: Supports both custom format and plain text

#### Key Methods:
```python
def mousePressEvent(self, event):
    # Store drag start position
    
def mouseMoveEvent(self, event):
    # Check drag distance and start drag operation
    
def _start_drag(self):
    # Create QDrag with asset data
    # Set custom MIME type: "application/x-hair-asset"
    # Create visual feedback pixmap
    
def _create_drag_pixmap(self):
    # Generate semi-transparent widget screenshot
```

#### MIME Data Format:
- **Primary**: `application/x-hair-asset` (custom format)
- **Fallback**: `text/plain` (JSON string)
- **Content**: Serialized asset dictionary as JSON

### ComponentList (Drop Target)

#### Key Features:
- **Drop Acceptance**: Validates MIME data format
- **Visual Feedback**: Shows dashed border during drag-over
- **Data Extraction**: Parses JSON asset data from drop
- **Component Creation**: Uses HairManager to create components
- **Type Validation**: Warns if asset type doesn't match tab type

#### Key Methods:
```python
def dragEnterEvent(self, event):
    # Check if drop can be accepted
    # Show visual feedback
    
def dragMoveEvent(self, event):
    # Continue drag validation
    
def dragLeaveEvent(self, event):
    # Hide visual feedback
    
def dropEvent(self, event):
    # Extract asset data
    # Create component
    # Update UI
    
def _can_accept_drop(self, event):
    # Validate MIME data format
    
def _extract_asset_data(self, event):
    # Parse JSON from MIME data
    
def _add_component_from_asset(self, asset_data):
    # Create component using HairManager
```

#### Visual Feedback:
- **Drag Enter**: Dashed border + background highlight
- **Drag Leave**: Remove visual effects
- **Drop Success**: Component added to list

### Error Handling

#### Robust Error Management:
1. **JSON Parsing**: Handles malformed data gracefully
2. **MIME Type Fallback**: Tries multiple data formats
3. **Component Creation**: Validates asset data before creation
4. **UI Updates**: Handles MListView compatibility issues
5. **Logging**: Comprehensive error logging for debugging

#### Common Error Scenarios:
- Invalid JSON in drag data
- Missing asset ID
- Component creation failure
- UI widget compatibility issues

## Usage Examples

### Basic Drag Operation:
1. User clicks and drags an AssetItem
2. AssetItem creates QDrag with asset JSON data
3. Visual feedback shows semi-transparent drag image
4. User drags over ComponentList
5. ComponentList shows dashed border feedback

### Successful Drop:
1. User releases mouse over ComponentList
2. ComponentList extracts asset data from QMimeData
3. HairManager creates new component from asset
4. Component appears in list and gets selected
5. Editor area updates with component properties

### Type Mismatch Handling:
- If asset type doesn't match current tab (e.g., XGen asset dropped on Card tab)
- Warning logged but component still created
- Allows flexibility while maintaining data integrity

## Testing

### Test Coverage:
1. **Basic Functionality**: Asset data serialization/deserialization
2. **UI Components**: Widget creation and interaction
3. **Error Handling**: Invalid data and edge cases
4. **Integration**: End-to-end drag and drop workflow

### Test Files:
- `test_drag_drop_functionality.py` - Comprehensive test suite
- Manual testing through UI interaction

## Future Enhancements

### Planned Improvements:
1. **Drag Preview**: Enhanced visual feedback during drag
2. **Drop Zones**: Multiple drop targets with different behaviors
3. **Batch Operations**: Drag multiple assets simultaneously
4. **Undo/Redo**: Support for drag and drop operations
5. **Asset Validation**: Pre-drop validation of asset compatibility

### Performance Optimizations:
1. **Lazy Loading**: Load drag pixmaps on demand
2. **Caching**: Cache frequently used asset data
3. **Throttling**: Limit drag event frequency

## Troubleshooting

### Common Issues:

#### Drag Not Starting:
- Check mouse drag distance threshold
- Verify AssetItem mouse event handling
- Ensure Qt application is properly initialized

#### Drop Not Accepted:
- Verify MIME data format
- Check ComponentList drop acceptance logic
- Ensure proper event handling chain

#### Component Not Created:
- Check HairManager.create_component() method
- Verify asset ID exists in mock data
- Check error logs for creation failures

#### UI Not Updating:
- Verify signal connections
- Check MListView compatibility methods
- Ensure proper widget refresh

### Debug Tips:
1. Enable debug logging for detailed event tracking
2. Use Qt Inspector to examine widget hierarchy
3. Test with simplified asset data first
4. Verify MIME data content in drop events

## Conclusion

The drag and drop implementation provides a robust, user-friendly way to create hair components from assets. The architecture is designed for extensibility and maintainability, with comprehensive error handling and testing coverage.
