"""Test Asset Library Fixes.

This script tests the fixes for the Asset Library data display issue.
Run this in a proper Maya/Qt environment using debug_start_hair_dev_direct.cmd
"""

import sys
import os
import logging

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

def setup_test_logging():
    """Set up logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Enable debug logging for key modules
    loggers = [
        'cgame_avatar_factory.hair_studio.ui.asset_library.asset_library',
        'cgame_avatar_factory.hair_studio.ui.base_hair_tab',
        'cgame_avatar_factory.hair_studio.manager.hair_manager',
    ]
    
    for logger_name in loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)

def test_signal_parameter_fix():
    """Test the signal parameter compatibility fix."""
    print("=" * 60)
    print("信号参数兼容性修复测试")
    print("=" * 60)
    
    try:
        from qtpy import QtWidgets, QtCore
        
        # Create application if needed
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab
        
        # Create components
        hair_manager = HairManager()
        base_tab = BaseHairTab("card", hair_manager)
        
        # Test signal parameter handling
        sample_asset = {
            "id": "card_1",
            "name": "Basic Hair Card",
            "asset_type": "card",
            "thumbnail": "card_1_thumb.jpg",
            "file_path": "assets/cards/basic_hair_card.ma",
        }
        
        print("✓ 测试信号参数处理...")
        
        # Test with asset dictionary (new format)
        try:
            base_tab._on_asset_selected(sample_asset)
            print("✓ 资产字典参数处理成功")
        except Exception as e:
            print(f"✗ 资产字典参数处理失败: {str(e)}")
            return False
        
        # Test with asset ID string (legacy format)
        try:
            base_tab._on_asset_selected("card_1")
            print("✓ 资产ID字符串参数处理成功")
        except Exception as e:
            print(f"✗ 资产ID字符串参数处理失败: {str(e)}")
            return False
        
        # Test with invalid parameter
        try:
            base_tab._on_asset_selected(123)  # Invalid type
            print("✓ 无效参数处理成功")
        except Exception as e:
            print(f"✓ 无效参数正确拒绝: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 信号参数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_asset_library_refresh():
    """Test the asset library refresh functionality."""
    print("\n" + "=" * 60)
    print("AssetLibrary刷新功能测试")
    print("=" * 60)
    
    try:
        from qtpy import QtWidgets
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        # Create application if needed
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create components
        hair_manager = HairManager()
        
        print("✓ 测试Card类型AssetLibrary...")
        card_library = AssetLibrary("card", hair_manager)
        print(f"✓ Card资产数量: {len(card_library.assets)}")
        
        print("✓ 测试XGen类型AssetLibrary...")
        xgen_library = AssetLibrary("xgen", hair_manager)
        print(f"✓ XGen资产数量: {len(xgen_library.assets)}")
        
        print("✓ 测试Curve类型AssetLibrary...")
        curve_library = AssetLibrary("curve", hair_manager)
        print(f"✓ Curve资产数量: {len(curve_library.assets)}")
        
        # Test refresh
        print("\n✓ 测试刷新功能...")
        card_library.refresh()
        print(f"✓ 刷新后Card资产数量: {len(card_library.assets)}")
        
        # Test update_assets
        print("✓ 测试update_assets功能...")
        test_assets = hair_manager.get_assets("card")
        card_library.update_assets(test_assets)
        print(f"✓ 更新后Card资产数量: {len(card_library.assets)}")
        
        return True
        
    except Exception as e:
        print(f"✗ AssetLibrary刷新测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_full_integration():
    """Test the full integration with Hair Studio tab."""
    print("\n" + "=" * 60)
    print("完整集成测试")
    print("=" * 60)
    
    try:
        from qtpy import QtWidgets
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create application if needed
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create hair studio
        hair_manager = HairManager()
        hair_studio = HairStudioTab(hair_manager)
        
        print("✓ HairStudioTab创建成功")
        
        # Test each tab
        tab_types = ["card", "xgen", "curve"]
        for i, expected_type in enumerate(tab_types):
            hair_studio.setCurrentIndex(i)
            current_tab = hair_studio.get_current_tab()
            
            print(f"\n✓ 测试{expected_type.upper()}标签页:")
            print(f"  - 标签页类型: {current_tab.hair_type}")
            print(f"  - AssetLibrary资产数量: {len(current_tab.asset_library.assets)}")
            
            # List assets
            for asset in current_tab.asset_library.assets:
                print(f"    * {asset['name']} (ID: {asset['id']})")
            
            # Test refresh
            current_tab.refresh_asset_library()
            print(f"  - 刷新后资产数量: {len(current_tab.asset_library.assets)}")
        
        print("\n✓ 所有标签页测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 完整集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_drop_integration():
    """Test drag and drop integration with the fixes."""
    print("\n" + "=" * 60)
    print("拖拽功能集成测试")
    print("=" * 60)
    
    try:
        from qtpy import QtWidgets
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create application if needed
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create hair studio
        hair_manager = HairManager()
        hair_studio = HairStudioTab(hair_manager)
        
        # Get the card tab
        hair_studio.setCurrentIndex(0)  # Card tab
        card_tab = hair_studio.get_current_tab()
        
        print("✓ 测试拖拽功能集成...")
        print(f"✓ Card标签页资产数量: {len(card_tab.asset_library.assets)}")
        print(f"✓ Card标签页组件数量: {len(hair_manager.get_components('card'))}")
        
        # Simulate asset selection (this should work with our fix)
        if card_tab.asset_library.assets:
            test_asset = card_tab.asset_library.assets[0]
            print(f"✓ 模拟选择资产: {test_asset['name']}")
            
            # This should now work with our signal parameter fix
            card_tab._on_asset_selected(test_asset)
            print("✓ 资产选择处理成功")
            
            # Check if component was created
            components_after = hair_manager.get_components('card')
            print(f"✓ 选择后组件数量: {len(components_after)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 拖拽功能集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("开始Asset Library修复验证测试...")
    print("注意: 此测试需要在正确的Maya/Qt环境中运行")
    print("使用 debug_start_hair_dev_direct.cmd 启动测试环境")
    
    # Set up logging
    setup_test_logging()
    
    # Run tests
    tests = [
        ("信号参数兼容性修复", test_signal_parameter_fix),
        ("AssetLibrary刷新功能", test_asset_library_refresh),
        ("完整集成", test_full_integration),
        ("拖拽功能集成", test_drag_drop_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    if passed == total:
        print(f"🎉 所有修复验证通过! ({passed}/{total})")
        print("✅ 已修复的问题:")
        print("  1. 信号参数不匹配问题")
        print("  2. AssetLibrary错误处理")
        print("  3. 调试日志增强")
        print("  4. 拖拽功能兼容性")
        print("\n✅ Asset Library现在应该正确显示资产!")
    else:
        print(f"❌ 部分修复验证失败 ({passed}/{total} 通过)")
        print("请检查上述错误信息")
    
    print("\n测试完成。")

if __name__ == "__main__":
    main()
