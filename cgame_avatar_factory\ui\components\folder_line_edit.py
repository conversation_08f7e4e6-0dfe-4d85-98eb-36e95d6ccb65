# Import third-party modules
import dayu_widgets
from qtpy import QtCore


class FolderLineEdit(dayu_widgets.MLineEdit):
    """Patch the MClickBrowserFolderToolButton to work with more dcc api."""

    sig_file_select_button_clicked = QtCore.Signal()

    def folder(self):
        """Add a MClickBrowserFolderToolButton for MLineEdit to select folder"""
        PatchedFolderToolButton = dayu_widgets.MClickBrowserFolderToolButton
        PatchedFolderToolButton.slot_browser_folder = self.slot_browser_folder  # Patch the slot_browser_folder

        _suffix_button = PatchedFolderToolButton()

        self.set_suffix_widget(_suffix_button)
        self.setPlaceholderText(self.tr("Click button to browser folder"))
        return self

    def slot_browser_folder(self):
        """Slot for sending signal when folder is selected"""
        self.sig_file_select_button_clicked.emit()

    def slot_folder_changed(self, folder_path):
        """Slot for sending signal when folder is selected"""
        self.setText(folder_path)
