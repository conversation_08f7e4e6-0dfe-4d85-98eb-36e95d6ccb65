@echo off
:: 毛发工作室开发测试环境启动脚本
:: 使用Maya环境但不启动Maya GUI

echo ========================================
echo 毛发工作室开发测试环境启动器
echo ========================================
echo.

cd /d %~dp0

echo 正在检查开发环境...

:: 检查thm命令是否可用
thm --version >nul 2>&1
if errorlevel 1 (
    echo ✗ thm命令不可用，请确保lightbox开发环境已正确安装
    pause
    exit /b 1
)

echo ✓ thm命令可用

:: 方案1: 尝试使用Maya环境的mayapy
echo.
echo 方案1: 尝试使用Maya环境的mayapy...
echo 命令: thm +p python-3.7..3.11 +p maya-2022..2024 +p lightbox_config-1 +p lightbox_paths-0 +p lightbox_log-0 +p dayu_widgets-0 +p qtpy-1.11 +p setuptools-41..71 +p blinker-1.4 +p lightbox_ui-0 +p dcc_menu_api-0 +p metahuman_dnaclib-1.2 +p maya_metahuman_dna_viewer-1.2 +p pydantic-2..2.8 +p scipy-1 +p blade_client_reporter-0 +p photoshop_python_api-0.13 +p maya-2022 +p python-3.7 +p pydantic-2.5 run mayapy run_hair_studio_dev.py

thm +p python-3.7..3.11 +p maya-2022..2024 +p lightbox_config-1 +p lightbox_paths-0 +p lightbox_log-0 +p dayu_widgets-0 +p qtpy-1.11 +p setuptools-41..71 +p blinker-1.4 +p lightbox_ui-0 +p dcc_menu_api-0 +p metahuman_dnaclib-1.2 +p maya_metahuman_dna_viewer-1.2 +p pydantic-2..2.8 +p scipy-1 +p blade_client_reporter-0 +p photoshop_python_api-0.13 +p maya-2022 +p python-3.7 +p pydantic-2.5 run mayapy run_hair_studio_dev.py

if errorlevel 1 (
    echo.
    echo 方案1失败，尝试方案2...
    echo.
    
    :: 方案2: 使用tox环境
    echo 方案2: 使用tox lm环境...
    echo 命令: thm dev --ignore-standard-cmd tox -e lm -- mayapy run_hair_studio_dev.py
    
    thm dev --ignore-standard-cmd tox -e lm -- mayapy run_hair_studio_dev.py
    
    if errorlevel 1 (
        echo.
        echo 方案2失败，尝试方案3...
        echo.
        
        :: 方案3: 使用开发环境的Python
        echo 方案3: 使用开发环境的Python...
        echo 命令: thm dev --ignore-standard-cmd python run_hair_studio_dev.py
        
        thm dev --ignore-standard-cmd python run_hair_studio_dev.py
        
        if errorlevel 1 (
            echo.
            echo ✗ 所有启动方案都失败了
            echo.
            echo 请检查以下问题:
            echo 1. lightbox开发环境是否正确安装
            echo 2. Maya是否正确安装
            echo 3. 项目依赖是否完整
            echo.
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo 毛发工作室开发环境已退出
echo ========================================
pause
