"""Debug Asset Library Display Issue.

This script helps debug why the Asset Library is not displaying mock data
in the Hair Studio UI.
"""

import sys
import os
import logging

# Add the project root to the Python path
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

def setup_logging():
    """Set up detailed logging for debugging."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('hair_studio_debug.log', mode='w')
        ]
    )
    
    # Enable debug logging for hair studio modules
    hair_studio_loggers = [
        'cgame_avatar_factory.hair_studio.manager.hair_manager',
        'cgame_avatar_factory.hair_studio.data.mock_data_manager',
        'cgame_avatar_factory.hair_studio.ui.asset_library.asset_library',
        'cgame_avatar_factory.hair_studio.ui.base_hair_tab',
        'cgame_avatar_factory.hair_studio.ui.hair_studio_tab',
    ]
    
    for logger_name in hair_studio_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
    
    print("✓ 调试日志已启用")
    print("✓ 日志文件: hair_studio_debug.log")

def test_data_managers():
    """Test the data managers to ensure they have data."""
    print("\n" + "=" * 60)
    print("数据管理器测试")
    print("=" * 60)
    
    try:
        # Test MockDataManager
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        mock_manager = MockDataManager()
        
        all_assets = mock_manager.get_assets()
        print(f"✓ MockDataManager: {len(all_assets)} 个总资产")
        
        for asset_type in ["card", "xgen", "curve"]:
            assets = mock_manager.get_assets(asset_type)
            print(f"  - {asset_type}: {len(assets)} 个资产")
            for asset in assets:
                print(f"    * {asset['name']} (ID: {asset['id']})")
        
        # Test HairManager
        print("\n✓ 测试 HairManager...")
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Mock QtCore for testing
        class MockQtCore:
            class QObject:
                def __init__(self, parent=None): pass
            class Signal:
                def __init__(self, *args): pass
                def emit(self, *args): pass
                def connect(self, *args): pass
        
        sys.modules['qtpy'] = type('MockModule', (), {})()
        sys.modules['qtpy.QtCore'] = MockQtCore
        sys.modules['qtpy'].QtCore = MockQtCore
        
        hair_manager = HairManager()
        
        for asset_type in ["card", "xgen", "curve"]:
            assets = hair_manager.get_assets(asset_type)
            print(f"  - HairManager {asset_type}: {len(assets)} 个资产")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_creation():
    """Test UI component creation without showing the window."""
    print("\n" + "=" * 60)
    print("UI组件创建测试")
    print("=" * 60)
    
    try:
        # Import Qt
        from qtpy import QtWidgets, QtCore
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        print("✓ Qt应用程序创建成功")
        
        # Import hair studio components
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab
        
        # Create hair manager
        hair_manager = HairManager()
        print("✓ HairManager创建成功")
        
        # Test AssetLibrary creation
        print("\n✓ 测试AssetLibrary创建...")
        asset_library = AssetLibrary("card", hair_manager)
        print(f"✓ AssetLibrary创建成功 (类型: card)")
        
        # Check if assets were loaded
        print(f"✓ AssetLibrary资产数量: {len(asset_library.assets)}")
        for asset in asset_library.assets:
            print(f"  - {asset['name']} (ID: {asset['id']})")
        
        # Test BaseHairTab creation
        print("\n✓ 测试BaseHairTab创建...")
        base_tab = BaseHairTab("card", hair_manager)
        print("✓ BaseHairTab创建成功")
        
        # Test refresh
        print("\n✓ 测试refresh_asset_library...")
        base_tab.refresh_asset_library()
        print("✓ refresh_asset_library调用成功")
        
        # Check asset library after refresh
        print(f"✓ 刷新后AssetLibrary资产数量: {len(base_tab.asset_library.assets)}")
        
        return True
        
    except Exception as e:
        print(f"✗ UI组件创建测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_full_hair_studio():
    """Test the full Hair Studio tab creation."""
    print("\n" + "=" * 60)
    print("完整Hair Studio测试")
    print("=" * 60)
    
    try:
        # Import Qt
        from qtpy import QtWidgets
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Import hair studio
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create hair manager
        hair_manager = HairManager()
        
        # Create hair studio tab
        print("✓ 创建HairStudioTab...")
        hair_studio = HairStudioTab(hair_manager)
        print("✓ HairStudioTab创建成功")
        
        # Check each tab
        for i in range(hair_studio.count()):
            tab = hair_studio.widget(i)
            tab_text = hair_studio.tabText(i)
            print(f"\n✓ 检查标签页: {tab_text}")
            print(f"  - 类型: {tab.hair_type}")
            print(f"  - AssetLibrary资产数量: {len(tab.asset_library.assets)}")
            
            # List assets
            for asset in tab.asset_library.assets:
                print(f"    * {asset['name']} (ID: {asset['id']})")
        
        # Test tab switching
        print("\n✓ 测试标签页切换...")
        for i in range(hair_studio.count()):
            hair_studio.setCurrentIndex(i)
            current_tab = hair_studio.get_current_tab()
            print(f"  - 切换到: {current_tab.hair_type}")
            print(f"  - 资产数量: {len(current_tab.asset_library.assets)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 完整Hair Studio测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debugging function."""
    print("开始Asset Library显示问题调试...")
    
    # Set up logging
    setup_logging()
    
    # Run tests
    tests = [
        ("数据管理器", test_data_managers),
        ("UI组件创建", test_ui_creation),
        ("完整Hair Studio", test_full_hair_studio),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    
    if passed == total:
        print(f"✅ 所有测试通过 ({passed}/{total})")
        print("✓ 数据管理器正常工作")
        print("✓ UI组件创建正常")
        print("✓ Asset Library应该显示资产")
        print("\n如果UI中仍然没有显示资产，请检查:")
        print("1. Qt样式表是否隐藏了资产项目")
        print("2. 布局是否正确设置")
        print("3. 窗口大小是否足够显示资产")
        print("4. 是否有其他UI更新覆盖了资产显示")
    else:
        print(f"❌ 发现问题 ({passed}/{total} 通过)")
        print("请查看上述错误信息和日志文件")
    
    print(f"\n📋 详细日志已保存到: hair_studio_debug.log")
    print("调试完成。")

if __name__ == "__main__":
    main()
